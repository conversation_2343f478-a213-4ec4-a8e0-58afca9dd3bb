// Simple script to list all sessions and their users
const { PrismaClient } = require('@prisma/client');

async function listSessions() {
  const prisma = new PrismaClient();
  
  try {
    console.log('📋 Listing all chat sessions...');
    
    const sessions = await prisma.chatSession.findMany({
      select: {
        sessionId: true,
        userId: true,
        title: true,
        roadmap: true
      },
      take: 10
    });
    
    console.log(`Found ${sessions.length} sessions:`);
    sessions.forEach((session, index) => {
      console.log(`${index + 1}. Session ID: ${session.sessionId}`);
      console.log(`   User ID: ${session.userId}`);
      console.log(`   Title: ${session.title || 'No title'}`);
      console.log(`   Roadmap: ${session.roadmap || 'null'}`);
      console.log('');
    });
    
    return sessions;
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

listSessions().then(() => process.exit(0));
