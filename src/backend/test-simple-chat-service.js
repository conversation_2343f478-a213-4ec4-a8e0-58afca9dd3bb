// Simple test for ChatServicePrisma
const { PrismaClient } = require('@prisma/client');
const { ChatServicePrisma } = require('./dist/src/services/implementations/ChatServicePrisma');

async function testSimpleChatService() {
  const prisma = new PrismaClient();
  const chatService = new ChatServicePrisma(prisma);

  try {
    console.log('🧪 Testing Simple ChatServicePrisma...\n');

    // Test 1: Create a chat session
    console.log('1. Creating a chat session...');
    const session = await chatService.createChatSession({
      userId: 1,
      title: 'Simple Test Chat'
    });
    console.log('✅ Session created:', session.sessionId);
    console.log('   Title:', session.title);
    console.log('   Created at:', session.createdAt);

    // Test 2: Add user message
    console.log('\n2. Adding user message...');
    const userMessage = await chatService.createChatMessage({
      sessionId: session.sessionId,
      userId: 1,
      messageType: 'user',
      content: 'Hello, this is a test message.',
      metadata: { source: 'test', timestamp: new Date().toISOString() }
    });
    console.log('✅ User message created with messageOrder:', userMessage.messageOrder);

    // Test 3: Add assistant message
    console.log('\n3. Adding assistant message...');
    const assistantMessage = await chatService.createChatMessage({
      sessionId: session.sessionId,
      userId: 1,
      messageType: 'assistant',
      content: 'Hello! I received your test message.',
      metadata: { aiGenerated: true }
    });
    console.log('✅ Assistant message created with messageOrder:', assistantMessage.messageOrder);

    // Test 4: Get the session with all messages
    console.log('\n4. Retrieving session with all messages...');
    const fullSession = await chatService.getChatSession(session.sessionId);
    console.log('✅ Session retrieved with', fullSession.messages.length, 'messages:');
    fullSession.messages.forEach(msg => {
      console.log(`   [${msg.messageOrder}] ${msg.messageType}: ${msg.content}`);
    });

    // Test 5: Get all sessions for user
    console.log('\n5. Getting all sessions for user...');
    const userSessions = await chatService.getChatSessionsByUser(1);
    console.log('✅ Found', userSessions.length, 'sessions for user 1');

    console.log('\n🎉 Simple ChatServicePrisma test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSimpleChatService();
