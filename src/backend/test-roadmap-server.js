// Simple test server for roadmap endpoints
const express = require('express');
const cors = require('cors');
const SimpleChatController = require('./simple-chat-controller');

console.log('🔧 Starting roadmap test server initialization...');

const app = express();
const PORT = 3001; // Use different port to avoid conflicts

console.log('⚙️ Setting up middleware...');

// Middleware
app.use(cors());
app.use(express.json());

// Simple auth middleware for testing (bypassing JWT for now)
const testAuth = (req, res, next) => {
  // For testing, use the user ID from the first session we found in our database test
  // Session: bfe25103-73ae-447f-9ca6-595b56293186 - we need to find its user ID
  req.user = {
    userId: 1, // Changed from 11 to 1 - we'll test with different user IDs
    email: '<EMAIL>',
    role: 'user'
  };
  next();
};

// Initialize controller
console.log('🎮 Initializing chat controller...');
const chatController = new SimpleChatController(null);
console.log('✅ Chat controller initialized');

// Routes
console.log('🛣️ Setting up routes...');
app.get('/api/chat/sessions/:sessionId/roadmap', testAuth, chatController.getChatSessionRoadmap);
app.post('/api/chat/sessions/:sessionId/roadmap', testAuth, chatController.updateChatSessionRoadmap);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', message: 'Roadmap test server running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Roadmap test server running on port ${PORT}`);
  console.log(`📝 Test endpoints available:`);
  console.log(`   GET  http://localhost:${PORT}/api/chat/sessions/{sessionId}/roadmap`);
  console.log(`   POST http://localhost:${PORT}/api/chat/sessions/{sessionId}/roadmap`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`🔑 Using test user ID: 1`);
});
