import { PasswordUtils } from '../src/utils/password';
import bcrypt from 'bcryptjs';

jest.mock('bcryptjs');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

beforeEach(() => {
  jest.resetAllMocks();
});

describe('PasswordUtils', () => {
  test('hashPassword', async () => {
    (mockedBcrypt.hash as jest.Mock).mockResolvedValue('hashed');
    const result = await PasswordUtils.hashPassword('pass');
    expect(result).toBe('hashed');
    expect(mockedBcrypt.hash).toHaveBeenCalledWith('pass', 12);
  });
  test('comparePassword', async () => {
    (mockedBcrypt.compare as jest.Mock).mockResolvedValue(true);
    await expect(PasswordUtils.comparePassword('p', 'h')).resolves.toBe(true);
    expect(mockedBcrypt.compare).toHaveBeenCalledWith('p', 'h');
  });

  test('validatePasswordStrength', () => {
    expect(PasswordUtils.validatePasswordStrength('')).toEqual({ isValid: false, errors: ['Password is required'] });
    const weak = PasswordUtils.validatePasswordStrength('abc');
    expect(weak.isValid).toBe(false);
    expect(weak.errors.length).toBeGreaterThan(0);
    expect(PasswordUtils.validatePasswordStrength('Strong1!')).toEqual({ isValid: true, errors: [] });
  });
});
