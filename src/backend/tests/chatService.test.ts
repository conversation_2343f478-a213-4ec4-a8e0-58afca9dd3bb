import { ChatService } from '../src/services/implementations/ChatService';

describe('ChatService', () => {
  let service: ChatService;  beforeEach(() => {
    service = new ChatService({} as any);
  });

  test('full chat session lifecycle', async () => {
    const session = await service.createChatSession({ userId: 1, title: 'first' });
    expect(session.sessionId).toBeDefined();

    expect(await service.getChatSessionsByUser(1)).toHaveLength(1);

    const message = await service.createChatMessage({
      sessionId: session.sessionId,
      userId: 1,
      messageType: 'user',
      content: 'hello',
    });
    expect(message.messageOrder).toBe(1);
    expect((await service.getChatMessages(session.sessionId)).length).toBe(1);

    const fetched = await service.getChatMessage(message.id);
    expect(fetched?.session?.sessionId).toBe(session.sessionId);

    await service.updateChatSessionTitle(session.sessionId, 'updated');
    expect((await service.getChatSession(session.sessionId))?.title).toBe('updated');

    await service.deleteChatMessage(message.id);
    expect(await service.getChatMessages(session.sessionId)).toEqual([]);

    await service.deleteChatSession(session.sessionId);
    expect(await service.getChatSession(session.sessionId)).toBeNull();
  });

  test('deleteChatMessage throws when not found', async () => {
    await expect(service.deleteChatMessage(999)).rejects.toThrow('Message not found');
  });
  test('updateChatSessionTitle throws for missing session', async () => {
    await expect(service.updateChatSessionTitle('x', 't')).rejects.toThrow('Session not found');
  });

  test('getChatMessage returns null when not found', async () => {
    expect(await service.getChatMessage(123)).toBeNull();
  });

  test('getNextMessageOrder computes correctly', async () => {
    const session = await service.createChatSession({ userId: 2 });
    expect(await service.getNextMessageOrder(session.sessionId)).toBe(1);
    await service.createChatMessage({ sessionId: session.sessionId, userId: 2, messageType: 'user', content: 'a' });
    expect(await service.getNextMessageOrder(session.sessionId)).toBe(2);
  });
});
