import jwt from 'jsonwebtoken';
import { JwtPayload } from '../src/utils/jwt';

let JwtUtils: typeof import('../src/utils/jwt').JwtUtils;
let mockedJwt: jest.Mocked<typeof jwt>;

jest.mock('jsonwebtoken');

beforeEach(() => {
  jest.resetModules();
  process.env.JWT_SECRET = 'secret';
  mockedJwt = require('jsonwebtoken');
  ({ JwtUtils } = require('../src/utils/jwt'));
  jest.resetAllMocks();
});

describe('JwtUtils', () => {
  test('generateToken delegates to jwt.sign', () => {
    (mockedJwt.sign as jest.Mock).mockReturnValue('token');
    const token = JwtUtils.generateToken({ id: 1, email: 'a', role: 'r' });
    expect(token).toBe('token');
    expect(mockedJwt.sign).toHaveBeenCalledWith({ userId: 1, email: 'a', role: 'r' }, 'secret');
  });
  test('verifyToken returns payload', () => {
    const payload: JwtPayload = { userId: 1, email: 'a', role: 'r' };
    (mockedJwt.verify as jest.Mock).mockReturnValue(payload);
    expect(JwtUtils.verifyToken('tok')).toEqual(payload);
  });

  test('verifyToken handles token expired', () => {
    class TokenExpiredError extends Error { constructor(){ super('x'); this.name='TokenExpiredError'; } }
    // @ts-ignore
    mockedJwt.TokenExpiredError = TokenExpiredError as any;
    mockedJwt.verify.mockImplementation(() => { throw new TokenExpiredError(); });
    expect(() => JwtUtils.verifyToken('t')).toThrow('Token has expired');
  });

  test('verifyToken handles invalid token', () => {
    class JsonWebTokenError extends Error { constructor(){ super('x'); this.name='JsonWebTokenError'; } }
    // @ts-ignore
    mockedJwt.JsonWebTokenError = JsonWebTokenError as any;
    mockedJwt.verify.mockImplementation(() => { throw new JsonWebTokenError(); });
    expect(() => JwtUtils.verifyToken('t')).toThrow('Invalid token');
  });

  test('verifyToken handles generic error', () => {
    mockedJwt.verify.mockImplementation(() => { throw new Error('other'); });
    expect(() => JwtUtils.verifyToken('t')).toThrow('Token verification failed');
  });

  test('extractTokenFromHeader validations', () => {
    expect(() => JwtUtils.extractTokenFromHeader(undefined)).toThrow('Authorization header is missing');
    expect(() => JwtUtils.extractTokenFromHeader('Bad')).toThrow('Authorization header must start with "Bearer "');
    expect(() => JwtUtils.extractTokenFromHeader('Bearer ')).toThrow('Token is missing from authorization header');
    expect(JwtUtils.extractTokenFromHeader('Bearer abc')).toBe('abc');
  });
});
