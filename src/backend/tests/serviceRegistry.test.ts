import { ServiceRegistry } from '../src/services/ServiceRegistry';
import { DependencyContainer } from '../src/services/DependencyContainer';
import '../src/services/implementations';

const mockPrisma = {} as any;

describe('ServiceRegistry', () => {
  test('singleton and getters', () => {
    const registry = ServiceRegistry.getInstance(mockPrisma);
    expect(registry.getSkillService()).toBeDefined();
    expect(registry.getUserService()).toBeDefined();
    expect(registry.getCategoryService()).toBeDefined();
    expect(registry.getChatService()).toBeDefined();
  });

  test('setters replace services', () => {
    const registry = ServiceRegistry.getInstance(mockPrisma);
    const dummy: any = {};
    registry.setSkillService(dummy);
    registry.setUserService(dummy);
    registry.setCategoryService(dummy);
    registry.setChatService(dummy);
    expect(registry.getSkillService()).toBe(dummy);
    expect(registry.getUserService()).toBe(dummy);
    expect(registry.getCategoryService()).toBe(dummy);
    expect(registry.getChatService()).toBe(dummy);
  });
});

test('DependencyContainer exposes prisma', () => {
  const container = new DependencyContainer(mockPrisma);
  expect(container.prisma).toBe(mockPrisma);
});
