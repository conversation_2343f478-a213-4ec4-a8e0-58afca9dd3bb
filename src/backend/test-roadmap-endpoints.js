const http = require('http');

async function testRoadmapEndpoints() {
    console.log('🧪 Testing Roadmap Endpoints...\n');

    // Helper function for HTTP requests
    function makeRequest(options, data = null) {
        return new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => {
                    body += chunk;
                });
                res.on('end', () => {
                    try {
                        const parsedBody = body ? JSON.parse(body) : {};
                        resolve({ status: res.statusCode, data: parsedBody });
                    } catch (error) {
                        resolve({ status: res.statusCode, data: body });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            if (data) {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    try {
        // 1. Test Login with seed user
        console.log('1. Testing login...');
        const loginResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/auth/login',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }, {
            email: '<EMAIL>',
            password: 'password123'
        });

        if (loginResult.status !== 200) {
            console.log('❌ Login failed:', loginResult.data);
            return;
        }

        const token = loginResult.data.token;
        console.log(`✅ Login successful! Token: ${token.substring(0, 20)}...`);

        // 2. Create a chat session
        console.log('\n2. Creating chat session...');
        const sessionResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/chat/sessions',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            title: 'Test Roadmap Session'
        });

        if (sessionResult.status !== 201) {
            console.log('❌ Session creation failed:', sessionResult.data);
            return;
        }

        const sessionId = sessionResult.data.sessionId;
        console.log(`✅ Session created! ID: ${sessionId}`);

        // 3. Test GET roadmap (should be empty initially)
        console.log('\n3. Testing GET roadmap (initial state)...');
        const getRoadmapResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/roadmap`,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (getRoadmapResult.status !== 200) {
            console.log('❌ GET roadmap failed:', getRoadmapResult.data);
            return;
        }

        console.log(`✅ GET roadmap successful! Initial roadmap:`, getRoadmapResult.data.roadmap);

        // 4. Test POST roadmap (update roadmap)
        console.log('\n4. Testing POST roadmap (update)...');
        const testRoadmap = "Phase 1: Learn JavaScript basics\\nPhase 2: Build a simple web app\\nPhase 3: Deploy to production";
        
        const postRoadmapResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/roadmap`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            roadmap: testRoadmap
        });

        if (postRoadmapResult.status !== 200) {
            console.log('❌ POST roadmap failed:', postRoadmapResult.data);
            return;
        }

        console.log(`✅ POST roadmap successful! Updated session:`, postRoadmapResult.data.title);

        // 5. Test GET roadmap again (should have the updated content)
        console.log('\n5. Testing GET roadmap (after update)...');
        const getRoadmapResult2 = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/roadmap`,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (getRoadmapResult2.status !== 200) {
            console.log('❌ GET roadmap (after update) failed:', getRoadmapResult2.data);
            return;
        }

        console.log(`✅ GET roadmap (after update) successful!`);
        console.log(`Roadmap content:\n${getRoadmapResult2.data.roadmap}`);

        // 6. Verify the roadmap matches what we set
        if (getRoadmapResult2.data.roadmap === testRoadmap) {
            console.log('\n🎉 All roadmap endpoint tests passed successfully!');
            console.log('✅ GET /api/chat/sessions/{sessionId}/roadmap - Working');
            console.log('✅ POST /api/chat/sessions/{sessionId}/roadmap - Working');
            console.log('✅ Database persistence - Working');
        } else {
            console.log('\n❌ Roadmap content mismatch!');
            console.log('Expected:', testRoadmap);
            console.log('Got:', getRoadmapResult2.data.roadmap);
        }

    } catch (error) {
        console.log('❌ Test failed with error:', error.message);
    }
}

testRoadmapEndpoints();
