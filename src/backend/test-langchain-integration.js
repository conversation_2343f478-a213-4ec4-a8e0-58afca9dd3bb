const http = require('http');

async function testLangChainIntegration() {
    console.log('🧪 Testing LangChain Integration...\n');

    // Helper function for HTTP requests
    function makeRequest(options, data = null) {
        return new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', chunk => body += chunk);
                res.on('end', () => {
                    try {
                        resolve({ status: res.statusCode, data: JSON.parse(body) });
                    } catch {
                        resolve({ status: res.statusCode, data: body });
                    }
                });
            });
            
            req.on('error', reject);
            if (data) req.write(JSON.stringify(data));
            req.end();
        });
    }

    try {
        // 1. Test Authentication
        console.log('1. Testing authentication...');
        const authResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }, {
            email: '<EMAIL>',
            password: 'test123'
        });

        if (authResult.status !== 200) {
            console.log('❌ Authentication failed:', authResult.data);
            return;
        }

        const token = authResult.data.token;
        console.log('✅ Authentication successful');

        // 2. Test start conversation with AI-powered title and response
        console.log('\n2. Testing AI-powered conversation start...');
        const conversationResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/chat/start-conversation',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            message: 'I want to become a frontend developer specializing in React'
        });

        if (conversationResult.status !== 201) {
            console.log('❌ Conversation start failed:', conversationResult.data);
            return;
        }

        const sessionId = conversationResult.data.session.sessionId;
        console.log(`✅ AI conversation started! Session ID: ${sessionId}`);
        console.log(`   Title: "${conversationResult.data.session.title}"`);
        console.log(`   AI Response Preview: "${conversationResult.data.messages[1].content.substring(0, 100)}..."`);

        // 3. Test sending a follow-up message with automatic AI response
        console.log('\n3. Testing follow-up message with AI response...');
        const followUpResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/messages`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            content: 'What should I learn first to get started?',
            messageType: 'user'
        });

        if (followUpResult.status !== 201) {
            console.log('❌ Follow-up message failed:', followUpResult.data);
            return;
        }

        if (followUpResult.data.aiResponse) {
            console.log('✅ Follow-up message with AI response successful!');
            console.log(`   User Message: "${followUpResult.data.userMessage.content}"`);
            console.log(`   AI Response Preview: "${followUpResult.data.aiResponse.content.substring(0, 100)}..."`);
        } else {
            console.log('⚠️  Follow-up message sent, but AI response failed:', followUpResult.data.warning || 'Unknown error');
        }

        // 4. Test message history
        console.log('\n4. Testing message history...');
        const historyResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/messages`,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (historyResult.status !== 200) {
            console.log('❌ Message history retrieval failed:', historyResult.data);
            return;
        }

        console.log(`✅ Message history retrieved! Total messages: ${historyResult.data.length}`);
        historyResult.data.forEach((msg, i) => {
            console.log(`   ${i + 1}. [${msg.messageType.toUpperCase()}] ${msg.content.substring(0, 80)}...`);
        });

        console.log('\n🎉 LangChain Integration Test Completed Successfully!');
        console.log('✅ All AI-powered features are working correctly');

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
    }
}

// Check if backend server is running
async function checkServerHealth() {
    try {
        const healthResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/health',
            method: 'GET'
        });
        return healthResult.status === 200;
    } catch {
        return false;
    }
}

function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', chunk => body += chunk);
            res.on('end', () => {
                try {
                    resolve({ status: res.statusCode, data: JSON.parse(body) });
                } catch {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });
        
        req.on('error', reject);
        if (data) req.write(JSON.stringify(data));
        req.end();
    });
}

// Run the test
async function main() {
    console.log('🔍 Checking if backend server is running...');
    const serverHealthy = await checkServerHealth();
    
    if (!serverHealthy) {
        console.log('❌ Backend server is not running on localhost:8080');
        console.log('💡 Please start the backend server first with: npm run dev');
        process.exit(1);
    }
    
    console.log('✅ Backend server is running\n');
    await testLangChainIntegration();
}

main().catch(console.error);
