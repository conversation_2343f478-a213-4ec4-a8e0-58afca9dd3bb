// Comprehensive integration test: Chat<PERSON>ontroller -> ServiceRegistry -> ChatServicePrisma -> Database
const { PrismaClient } = require('@prisma/client');

// Import the ServiceRegistry
const { ServiceRegistry } = require('./dist/services/ServiceRegistry');

console.log('Starting integration test...');

async function createTestIntegration() {
  console.log('🧪 Testing Complete Chat Integration...\n');

  const prisma = new PrismaClient();
  
  try {
    // 1. Initialize ServiceRegistry with ChatServicePrisma
    console.log('1. Initializing ServiceRegistry with ChatServicePrisma...');
    const serviceRegistry = ServiceRegistry.getInstance(prisma);
    console.log('✅ ServiceRegistry initialized');    // 2. Test chat service through ServiceRegistry
    console.log('\n2. Testing chat service through ServiceRegistry...');
    const chatService = serviceRegistry.getChatService();
    console.log('✅ ChatService obtained from ServiceRegistry');
    console.log('   Service type:', chatService.constructor.name);
    console.log('   Has prisma?', !!chatService.prisma);
    
    // Verify it's ChatServicePrisma by testing its behavior
    const testSession = await chatService.createChatSession({
      userId: 1,
      title: 'Integration Test Session'
    });
    console.log('✅ Created session via ServiceRegistry:', testSession.sessionId);

    // 3. Test message creation with proper message ordering
    console.log('\n3. Testing message creation and ordering...');
    
    // First message should have order 1
    const message1 = await chatService.createChatMessage({
      sessionId: testSession.sessionId,
      userId: 1,
      messageType: 'user',
      content: 'Hello, this is the first message.',
      metadata: { test: true }
    });
    console.log('✅ First message created with order:', message1.messageOrder);

    // Second message should have order 2
    const message2 = await chatService.createChatMessage({
      sessionId: testSession.sessionId,
      userId: 1,
      messageType: 'assistant',
      content: 'Hello! This is the assistant response.',
      metadata: { aiGenerated: true }
    });
    console.log('✅ Second message created with order:', message2.messageOrder);

    // 4. Verify session retrieval with messages
    console.log('\n4. Verifying session retrieval with messages...');
    const sessionWithMessages = await chatService.getChatSession(testSession.sessionId);
    console.log('✅ Session retrieved with', sessionWithMessages.messages.length, 'messages');
    console.log('   Message orders:', sessionWithMessages.messages.map(m => `[${m.messageOrder}] ${m.messageType}: ${m.content.substring(0, 30)}...`));

    // 5. Test user sessions retrieval
    console.log('\n5. Testing user sessions retrieval...');
    const userSessions = await chatService.getChatSessionsByUser(1);
    console.log('✅ Retrieved', userSessions.length, 'sessions for user 1');    // 6. Verify database persistence by checking directly
    console.log('\n6. Verifying database persistence...');
    console.log('   Looking for sessionId:', testSession.sessionId);
    
    // First check all sessions in the database
    const allSessions = await prisma.chatSession.findMany();
    console.log('   Total sessions in database:', allSessions.length);
    if (allSessions.length > 0) {
      console.log('   Session IDs in database:', allSessions.map(s => s.sessionId));
    }
    
    const dbSession = await prisma.chatSession.findUnique({
      where: { sessionId: testSession.sessionId },
      include: { ChatMessage: { orderBy: { messageOrder: 'asc' } } }
    });
    
    if (!dbSession) {
      console.log('❌ Session not found in database!');
      console.log('   This might indicate the service is not actually persisting to the database');
      return;
    }
    
    console.log('✅ Session found in database with', dbSession.ChatMessage.length, 'messages');
    console.log('   Database messages:', dbSession.ChatMessage.map(m => `[${m.messageOrder}] ${m.messageType}`));

    // 7. Test session title update
    console.log('\n7. Testing session title update...');
    const updatedSession = await chatService.updateChatSessionTitle(testSession.sessionId, 'Updated Integration Test');
    console.log('✅ Session title updated to:', updatedSession.title);

    console.log('\n🎉 Complete Integration Test PASSED!');
    console.log('✅ ChatController -> ServiceRegistry -> ChatServicePrisma -> Database');
    console.log('✅ Session creation with UUID generation');
    console.log('✅ Message ordering (1, 2, 3...)');
    console.log('✅ Database persistence verified');
    console.log('✅ Metadata handling');
    console.log('✅ Session title management');

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the integration test
createTestIntegration();
