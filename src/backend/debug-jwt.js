// Test script to debug JWT authentication issues
const jwt = require('jsonwebtoken');

const JWT_SECRET = 'fallback-secret-key-for-development';

// Test token from your login response
const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.Mz-WBb1ylMFox4hT0aR7IoaXaisdXXNF-luKRHrcSwA';

console.log('JWT_SECRET:', JWT_SECRET);
console.log('Test Token:', testToken);

try {
  const decoded = jwt.verify(testToken, JWT_SECRET);
  console.log('Token verification successful:', decoded);
} catch (error) {
  console.error('Token verification failed:', error.message);
  console.error('Error type:', error.constructor.name);
}

// Test token generation
const testUser = { id: 12, email: '<EMAIL>', role: 'employee' };
const newToken = jwt.sign({ userId: testUser.id, email: testUser.email, role: testUser.role }, JWT_SECRET);
console.log('New test token:', newToken);

try {
  const decodedNew = jwt.verify(newToken, JWT_SECRET);
  console.log('New token verification successful:', decodedNew);
} catch (error) {
  console.error('New token verification failed:', error.message);
}
