// Test script to verify /auth/me endpoint returns complete user profile
const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

async function testAuthMe() {
  console.log('🔐 Testing /auth/me endpoint for complete user profile');
  console.log('=======================================================');

  try {
    // Step 1: Register a test user
    console.log('\n1️⃣ Registering test user...');
    const testUser = {
      username: 'TestUser AuthMe',
      firstName: 'John',
      lastName: 'Doe',
      email: `authme.test.${Date.now()}@example.com`,
      password: 'TestPassword123!'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ User registered successfully');
    console.log('Response:', registerResponse.data);

    const token = registerResponse.data.token;
    
    // Step 2: Test /auth/me endpoint
    console.log('\n2️⃣ Testing /auth/me endpoint...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Profile retrieved successfully');
    console.log('\n📋 User Profile Data:');
    console.log('====================');
    
    const user = profileResponse.data;
    console.log(`ID: ${user.id}`);
    console.log(`Username: ${user.username}`);
    console.log(`Email: ${user.email}`);
    console.log(`First Name: ${user.firstName || 'null'}`);
    console.log(`Last Name: ${user.lastName || 'null'}`);
    console.log(`Avatar URL: ${user.avatarUrl || 'null'}`);
    console.log(`Role: ${user.role}`);
    console.log(`Created At: ${user.createdAt}`);
    console.log(`Updated At: ${user.updatedAt}`);
    
    // Check if all required fields are present
    console.log('\n🔍 Field Validation:');
    console.log('===================');
    
    const requiredFields = ['firstName', 'lastName', 'email', 'avatarUrl', 'role'];
    const missingFields = [];
    const presentFields = [];
    
    requiredFields.forEach(field => {
      if (user.hasOwnProperty(field)) {
        presentFields.push(field);
        console.log(`✅ ${field}: ${user[field] || 'null (present but empty)'}`);
      } else {
        missingFields.push(field);
        console.log(`❌ ${field}: MISSING`);
      }
    });
    
    console.log('\n📊 Summary:');
    console.log('==========');
    console.log(`Present fields: ${presentFields.length}/${requiredFields.length}`);
    console.log(`Missing fields: ${missingFields.length}`);
    
    if (missingFields.length === 0) {
      console.log('🎉 All required fields are present in /auth/me response!');
    } else {
      console.log(`⚠️  Missing fields: ${missingFields.join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testAuthMe();
