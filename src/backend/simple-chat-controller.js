// Simplified JavaScript version of ChatController for roadmap endpoints
const { PrismaClient } = require('@prisma/client');

class SimpleChatController {
  constructor(serviceRegistry) {
    this.serviceRegistry = serviceRegistry;
    this.prisma = new PrismaClient();
  }

  // GET /api/chat/sessions/:sessionId/roadmap
  getChatSessionRoadmap = async (req, res) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { sessionId } = req.params;

      // Verify session exists and user owns it
      const session = await this.prisma.chatSession.findUnique({
        where: { sessionId: sessionId },
        select: { userId: true, roadmap: true, sessionId: true }
      });

      if (!session) {
        return res.status(404).json({ error: 'Chat session not found' });
      }

      if (session.userId !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json({ roadmap: session.roadmap });
    } catch (error) {
      console.error('Error getting chat session roadmap:', error);
      res.status(500).json({ error: 'Failed to get chat session roadmap' });
    }
  };

  // POST /api/chat/sessions/:sessionId/roadmap
  updateChatSessionRoadmap = async (req, res) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { sessionId } = req.params;
      const { roadmap } = req.body;

      if (typeof roadmap !== 'string') {
        return res.status(400).json({ error: 'Roadmap must be a string' });
      }

      // Verify session exists and user owns it
      const session = await this.prisma.chatSession.findUnique({
        where: { sessionId: sessionId },
        select: { userId: true, sessionId: true }
      });

      if (!session) {
        return res.status(404).json({ error: 'Chat session not found' });
      }

      if (session.userId !== userId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const updatedSession = await this.prisma.chatSession.update({
        where: { sessionId: sessionId },
        data: { roadmap: roadmap }
      });

      res.json(updatedSession);
    } catch (error) {
      console.error('Error updating chat session roadmap:', error);
      res.status(500).json({ error: 'Failed to update chat session roadmap' });
    }
  };
}

module.exports = SimpleChatController;
