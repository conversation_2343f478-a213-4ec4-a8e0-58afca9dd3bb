# JWT Authentication Troubleshooting Guide

## Quick Fix: Get a Fresh Token

### Step 1: Login to get a new token
```powershell
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method POST -ContentType "application/json" -Body '{"email":"<EMAIL>","password":"your-password"}'
$token = $loginResponse.token
```

### Step 2: Use the token in your requests
```powershell
$headers = @{ 
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json" 
}

# Test the debug endpoint
$debugResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/chat/debug-auth" -Method POST -Headers $headers

# Use the chat endpoint
$chatResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/chat/start-conversation" -Method POST -Headers $headers -Body '{"message":"Your message here"}'
```

## Common Issues and Solutions

### Issue 1: "Invalid token" Error
**Causes:**
- <PERSON><PERSON> has expired
- Wrong JWT_SECRET environment variable
- Malformed Authorization header
- <PERSON>ken copied incorrectly

**Diagnostic Steps:**
1. Test with debug endpoint: `POST /api/chat/debug-auth`
2. Check if you can login successfully
3. Verify token format starts with "Bearer "

### Issue 2: Missing Authorization Header
**Error:** "Authorization header is missing"
**Solution:** Ensure you include the Authorization header:
```powershell
$headers = @{ "Authorization" = "Bearer YOUR_TOKEN_HERE" }
```

### Issue 3: Wrong Bearer Format
**Error:** "Authorization header must start with 'Bearer '"
**Solution:** Ensure the header is formatted as: `Bearer <token>`

### Issue 4: Token Verification Failed
**Causes:**
- JWT_SECRET mismatch between login and verification
- Corrupted token
- Server restart with different environment

**Solution:** 
1. Restart the server
2. Get a fresh login token
3. Verify JWT_SECRET in .env file

## Environment Check

Current server configuration:
- JWT_SECRET: Present ✓
- Environment: development ✓  
- Token generation: Working ✓
- Token verification: Working ✓

## Test Commands

### Register a new user:
```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/auth/register" -Method POST -ContentType "application/json" -Body '{"username":"Test User","email":"<EMAIL>","password":"TestPass123!"}'
```

### Login:
```powershell
$login = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method POST -ContentType "application/json" -Body '{"email":"<EMAIL>","password":"TestPass123!"}'
$token = $login.token
```

### Test authentication:
```powershell
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }
Invoke-RestMethod -Uri "http://localhost:8080/api/chat/debug-auth" -Method POST -Headers $headers
```

### Start a conversation:
```powershell
Invoke-RestMethod -Uri "http://localhost:8080/api/chat/start-conversation" -Method POST -Headers $headers -Body '{"message":"I want to become a Solution Architect"}'
```
