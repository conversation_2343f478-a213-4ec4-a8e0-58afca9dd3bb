// Test script to verify ChatService database persistence
const { PrismaClient } = require('@prisma/client');
const { ChatService } = require('./dist/services/implementations/ChatService');

async function testChatServicePersistence() {
  const prisma = new PrismaClient();
  const chatService = new ChatService(prisma);

  try {
    console.log('🧪 Testing ChatService database persistence...\n');

    // Test 1: Create a chat session
    console.log('1. Creating a chat session...');
    const session = await chatService.createChatSession({
      userId: 1,
      title: 'Test Session'
    });
    console.log('✅ Session created:', session.sessionId);

    // Test 2: Add messages to the session
    console.log('\n2. Adding messages to the session...');
    const userMessage = await chatService.createChatMessage({
      sessionId: session.sessionId,
      userId: 1,
      messageType: 'user',
      content: 'Hello, how are you?',
      metadata: { source: 'test' }
    });
    console.log('✅ User message created:', userMessage.id);

    const aiMessage = await chatService.createChatMessage({
      sessionId: session.sessionId,
      userId: 1,
      messageType: 'assistant',
      content: 'I am doing well, thank you for asking!',
      metadata: { aiGenerated: true }
    });
    console.log('✅ AI message created:', aiMessage.id);

    // Test 3: Retrieve the session with messages
    console.log('\n3. Retrieving session with messages...');
    const sessionWithMessages = await chatService.getChatSession(session.sessionId);
    console.log('✅ Session retrieved with', sessionWithMessages.messages.length, 'messages');
    console.log('   Messages:', sessionWithMessages.messages.map(m => `${m.messageType}: ${m.content}`));

    // Test 4: Get sessions by user
    console.log('\n4. Getting sessions by user...');
    const userSessions = await chatService.getChatSessionsByUser(1);
    console.log('✅ Found', userSessions.length, 'sessions for user 1');

    // Test 5: Update session title
    console.log('\n5. Updating session title...');
    const updatedSession = await chatService.updateChatSessionTitle(session.sessionId, 'Updated Test Session');
    console.log('✅ Session title updated:', updatedSession.title);

    console.log('\n🎉 All tests passed! ChatService is working with database persistence.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testChatServicePersistence();
