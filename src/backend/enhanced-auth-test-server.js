// Enhanced auth test server with complete user profile fields
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const JWT_SECRET = 'test-secret-key-for-development';

// In-memory users storage for testing
const users = [];

// Middleware
app.use(cors());
app.use(express.json());

// Request logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', message: 'Enhanced auth test server running' });
});

// API info
app.get('/api', (req, res) => {
  res.json({
    name: 'Enhanced Auth Test Server',
    version: '1.0.0',
    description: 'Testing complete user profile in /auth/me endpoint',
    endpoints: {
      auth: '/api/auth',
      health: '/health'
    }
  });
});

// Register endpoint
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('Registration endpoint called with:', req.body);
    const { username, firstName, lastName, email, password } = req.body;
    
    // Basic validation
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }
    
    // Check if user exists
    if (users.find(u => u.email === email)) {
      return res.status(400).json({ error: 'User already exists' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user with complete profile
    const user = {
      id: users.length + 1,
      username: username || `${firstName || ''} ${lastName || ''}`.trim() || email.split('@')[0],
      firstName: firstName || null,
      lastName: lastName || null,
      email,
      password: hashedPassword,
      role: 'employee',
      avatarUrl: null,
      oauthProvider: null,
      oauthId: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    users.push(user);
    
    // Generate token
    const token = jwt.sign({ 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    }, JWT_SECRET, { expiresIn: '24h' });
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    
    res.status(201).json({
      message: 'User registered successfully',
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login endpoint called with:', req.body);
    const { email, password } = req.body;
    
    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Check password
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Generate token
    const token = jwt.sign({ 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    }, JWT_SECRET, { expiresIn: '24h' });
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    
    res.json({
      message: 'Login successful',
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Enhanced /auth/me endpoint with complete user profile
app.get('/api/auth/me', (req, res) => {
  try {
    console.log('Profile endpoint called');
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }
    
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = users.find(u => u.id === decoded.userId);
    
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }
    
    // Return complete user profile (excluding password)
    const { password: _, ...userProfile } = user;
    
    console.log('Returning user profile:', userProfile);
    res.json(userProfile);
    
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
});

const PORT = 8080;
app.listen(PORT, () => {
  console.log(`🚀 Enhanced Authentication test server running on port ${PORT}`);
  console.log('📋 Endpoints available:');
  console.log('- GET /api - API info');
  console.log('- GET /health - Health check');
  console.log('- POST /api/auth/register - Register user with complete profile');
  console.log('- POST /api/auth/login - Login user');
  console.log('- GET /api/auth/me - Get complete user profile (protected)');
  console.log('');
  console.log('🔍 Testing complete user profile fields:');
  console.log('- firstName, lastName, email, avatarUrl, role');
});
