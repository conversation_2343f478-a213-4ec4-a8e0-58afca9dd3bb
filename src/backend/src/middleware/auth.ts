import { Request, Response, NextFunction } from 'express';
import { JwtUtils } from '../utils/jwt';

// Extend the Request interface to include user property
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: number;
    email: string;
    role: string;
  };
}

export const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    console.log('🔐 Auth middleware called');
    const authHeader = req.headers.authorization;
    
    // Enhanced logging for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔐 Auth attempt for ${req.method} ${req.path}`);
      console.log(`📋 Auth header present: ${authHeader ? 'Yes' : 'No'}`);
    }
    
    console.log('🔍 Auth header:', authHeader ? 'Present' : 'Missing');
    
    const token = JwtUtils.extractTokenFromHeader(authHeader);
    console.log('🔍 Extracted token:', token ? `${token.substring(0, 20)}...` : 'No token');
    
    const decoded = JwtUtils.verifyToken(token);
    console.log('🔍 Decoded token:', decoded);
    
    req.user = decoded;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Auth successful for user: ${decoded.userId} (${decoded.email})`);
    }
    
    console.log('✅ Authentication successful');
    next();
  } catch (error) {
    console.error('❌ Authentication failed:', error);
    const message = error instanceof Error ? error.message : 'Authentication failed';
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`❌ Auth failed for ${req.method} ${req.path}: ${message}`);
    }
    
    res.status(401).json({ 
      error: 'Unauthorized',
      message 
    });
  }
};

export const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader) {
      const token = JwtUtils.extractTokenFromHeader(authHeader);
      const decoded = JwtUtils.verifyToken(token);
      req.user = decoded;
    }
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
