import express from 'express';
import cors from 'cors';
import swaggerUi from 'swagger-ui-express';
import prisma from './prisma/client';
import { ServiceRegistry } from './services';
import { createUserRouter, createCategoryRouter, createSkillRouter, createChatRouter } from './routes';
import { specs } from './config/swagger';
import { AuthController } from './controllers/authController';
import { authenticateToken } from './middleware/auth';

const app = express();

console.log('🔧 Initializing Express app...');

// Initialize dependency injection
console.log('🔧 Creating service registry...');
const serviceRegistry = ServiceRegistry.getInstance(prisma);
console.log('✅ Service registry created successfully');

// Basic middleware - CORS configuration
app.use(cors({
  origin: true, // Allow requests from any origin (more permissive than '*')
  credentials: true, // Allow cookies and authorization headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name',
    'X-API-Key'
  ],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📨 ${req.method} ${req.path} - ${new Date().toISOString()}`);
  console.log('🔍 Headers:', req.headers.authorization ? 'Authorization header present' : 'No auth header');
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('🔍 Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

// API Info endpoint
app.get('/api', (req, res) => {
  console.log('📊 API Info endpoint called');
  try {
    res.json({
      name: 'CodePlus Platform Backend API',
      version: '1.0.0',
      description: 'REST API for managing users, skills, categories, and authentication',
      documentation: '/api-docs',
      endpoints: {
        auth: '/api/auth',
        users: '/api/users',
        categories: '/api/categories',
        skills: '/api/skills'
      },
      authEndpoints: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        profile: 'GET /api/auth/me (requires auth)'
      }
    });
    console.log('✅ API Info response sent');
  } catch (error) {
    console.error('❌ Error in API Info endpoint:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Routes
console.log('🔧 Setting up API routes...');

// Temporary inline auth routes for testing
const authRouter = express.Router();
const authController = new AuthController(serviceRegistry);

authRouter.post('/register', authController.register.bind(authController));
authRouter.post('/login', authController.login.bind(authController));
authRouter.get('/me', authenticateToken, authController.getCurrentUser.bind(authController));

app.use('/api/auth', authRouter);
app.use('/api/users', createUserRouter(serviceRegistry));
app.use('/api/categories', createCategoryRouter(serviceRegistry));
app.use('/api/skills', createSkillRouter(serviceRegistry));
app.use('/api/chat', createChatRouter(serviceRegistry));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check database connectivity
    await prisma.$queryRaw`SELECT 1`;
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: process.env.npm_package_version || '1.0.0'
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: 'Database connection failed'
    });
  }
});

// 404 handler - catch all unmatched routes
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Global error handler
app.use((error: Error, req: express.Request, res: express.Response) => {
  console.error('Global error handler:', error);
  res.status(500).json({ error: 'Internal server error' });
});

console.log('✅ Express app initialized successfully');

export default app;