import { PrismaClient, User } from '@prisma/client';
import { IUserService, CreateUserDto, AddSkillToUserDto, UserWithSkills, RegisterDto, LoginDto, AuthResponse } from '../interfaces';
import { PasswordUtils } from '../../utils/password';
import { JwtUtils } from '../../utils/jwt';

export class UserService implements IUserService {
  constructor(private readonly prisma: PrismaClient) {}  async register(data: RegisterDto): Promise<AuthResponse> {
    // Check if user already exists
    const existingUser = await this.getUserByEmail(data.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await PasswordUtils.hashPassword(data.password);

    // Create user
    const user = await this.prisma.user.create({
      data: {
        username: data.username || `${data.firstName || ''} ${data.lastName || ''}`.trim(),
        email: data.email,
        password: hashedPassword,
        firstName: data.firstName || null,
        lastName: data.lastName || null,
        role: 'employee',
        updatedAt: new Date()
      }
    });// Generate JWT token
    const token = JwtUtils.generateToken({
      id: user.id,
      email: user.email,
      role: user.role || 'employee'
    });

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword,
      token
    };
  }

  async login(data: LoginDto): Promise<AuthResponse> {
    // Find user by email
    const user = await this.getUserByEmail(data.email);
    if (!user || !user.password) {
      throw new Error('Invalid email or password');
    }

    // Check password
    const isPasswordValid = await PasswordUtils.comparePassword(data.password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid email or password');
    }

    // Generate JWT token
    const token = JwtUtils.generateToken({
      id: user.id,
      email: user.email,
      role: user.role || 'employee'
    });

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword,
      token
    };
  }

  async getUsers(): Promise<Omit<User, 'password'>[]> {
    const users = await this.prisma.user.findMany();
    // Remove passwords from all users
    return users.map(({ password: _, ...user }) => user);
  }  async getUserById(id: number): Promise<UserWithSkills | null> {
    console.log('🔍 getUserById called with ID:', id);
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          email: true,
          avatarUrl: true,
          role: true,
          oauthProvider: true,
          oauthId: true,
          createdAt: true,
          updatedAt: true
        }
      });
      
      console.log('🔍 Database query result:', user ? 'User found' : 'User not found');
      if (user) {
        console.log('🔍 User data:', JSON.stringify(user, null, 2));
      }
      
      if (!user) return null;

      // Return user with empty skills array to match UserWithSkills interface
      return {
        ...user,
        skills: []
      } as UserWithSkills;
    } catch (error) {
      console.error('❌ Error in getUserById:', error);
      throw error;
    }
  }
  async getBasicUserById(id: number): Promise<Omit<User, 'password'> | null> {
    console.log('🔍 getBasicUserById called with ID:', id);
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          email: true,
          avatarUrl: true,
          role: true,
          oauthProvider: true,
          oauthId: true,
          createdAt: true,
          updatedAt: true
        }
      });
      
      console.log('🔍 Database query result:', user ? 'User found' : 'User not found');
      if (user) {
        console.log('🔍 User data:', JSON.stringify(user, null, 2));
      }
      
      return user;
    } catch (error) {
      console.error('❌ Error in getBasicUserById:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return await this.prisma.user.findUnique({
      where: { email }
    });
  }
  async getUserSkills(userId: number) {
    const userSkills = await this.prisma.userSkill.findMany({
      where: { userId },
      include: {
        Skill: {
          include: {
            Category: true
          }
        }
      }
    });
    return userSkills;
  }

  async createUser(data: CreateUserDto): Promise<Omit<User, 'password'>> {
    let hashedPassword: string;
    
    if (data.password) {
      // Validate password strength if provided
      const passwordValidation = PasswordUtils.validatePasswordStrength(data.password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }
      hashedPassword = await PasswordUtils.hashPassword(data.password);    } else {
      // Generate a temporary password if none provided (for admin creation)
      hashedPassword = await PasswordUtils.hashPassword('TempPassword123!');
    }    const user = await this.prisma.user.create({
      data: {
        username: data.name, // Use the name as username
        email: data.email,
        password: hashedPassword,
        role: data.role || 'employee',
        avatarUrl: data.avatarUrl,
        oauthProvider: data.oauthProvider,
        oauthId: data.oauthId,
        updatedAt: new Date()
      }
    });

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  async updateUser(id: number, data: Partial<CreateUserDto>): Promise<Omit<User, 'password'>> {
    const updateData: Partial<CreateUserDto & { password: string }> = { ...data };
    
    if (data.password) {
      // Validate password strength if updating password
      const passwordValidation = PasswordUtils.validatePasswordStrength(data.password);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }
      updateData.password = await PasswordUtils.hashPassword(data.password);
    }

    const user = await this.prisma.user.update({
      where: { id },
      data: updateData
    });

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async deleteUser(id: number): Promise<void> {
    // First delete all user-skill relationships
    await this.prisma.userSkill.deleteMany({
      where: { userId: id }
    });

    // Then delete the user
    await this.prisma.user.delete({
      where: { id }
    });
  }

  async addSkillToUser(userId: number, data: AddSkillToUserDto) {
    // Check if the relationship already exists
    const existingRelation = await this.prisma.userSkill.findUnique({
      where: {
        userId_skillId: {
          userId,
          skillId: data.skillId
        }
      }
    });

    if (existingRelation) {
      throw new Error('User already has this skill');
    }    return await this.prisma.userSkill.create({
      data: {
        userId,
        skillId: data.skillId,
        updatedAt: new Date()
      },
      include: {
        Skill: {
          include: {
            Category: true
          }
        }
      }
    });
  }

  async removeSkillFromUser(userId: number, skillId: number): Promise<void> {
    try {
      await this.prisma.userSkill.delete({
        where: {
          userId_skillId: {
            userId,
            skillId
          }
        }
      });    } catch (error) {
      throw new Error('User-skill relationship not found');
    }
  }

  // Convenience methods for OAuth2 compatibility
  async create(data: CreateUserDto): Promise<UserWithSkills> {
    const user = await this.createUser(data);
    return await this.getUserById(user.id) as UserWithSkills;
  }

  async findById(id: number): Promise<UserWithSkills | null> {
    return await this.getUserById(id);
  }

  async findByEmail(email: string): Promise<UserWithSkills | null> {
    const user = await this.getUserByEmail(email);
    if (!user) return null;
    return await this.getUserById(user.id);
  }

  async update(id: number, data: Partial<CreateUserDto>): Promise<UserWithSkills> {
    await this.updateUser(id, data);
    return await this.getUserById(id) as UserWithSkills;
  }
}
