/**
 * LangChain Service Integration
 * 
 * This service handles communication between the Node.js backend 
 * and the Python FastAPI LangChain service.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface LangChainMessage {
  type: 'human' | 'ai' | 'system';
  content: string;
  run_id?: string;
}

export interface LangChainUserInput {
  message: string;
  thread_id?: string;
  user_id?: string;
  model?: string;
  agent_config?: Record<string, any>;
}

export interface LangChainStreamInput extends LangChainUserInput {
  stream_tokens?: boolean;
}

export interface LangChainResponse {
  type: 'human' | 'ai' | 'system';
  content: string;
  run_id?: string;
  metadata?: Record<string, any>;
}

export interface ServiceMetadata {
  agents: Array<{
    key: string;
    name: string;
    description: string;
  }>;
  models: string[];
  default_agent: string;
  default_model: string;
}

export class LangChainService {
  private apiClient: AxiosInstance;
  private baseUrl: string;
  private timeout: number;

  constructor(
    baseUrl: string = process.env.LANGCHAIN_SERVICE_URL || 'http://localhost:8000',
    timeout: number = 30000
  ) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;

    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.LANGCHAIN_AUTH_SECRET || 'dev-secret'}`,
      },
    });

    // Request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`🔗 LangChain API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ LangChain API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`✅ LangChain API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('❌ LangChain API Response Error:', error.response?.status, error.response?.data);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get service metadata including available agents and models
   */
  async getServiceInfo(): Promise<ServiceMetadata> {
    try {
      const response: AxiosResponse<ServiceMetadata> = await this.apiClient.get('/info');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get LangChain service info:', error);
      throw new Error('Failed to connect to LangChain service');
    }
  }

  /**
   * Generate a conversation title using AI
   */
  async generateConversationTitle(userMessage: string, agentId: string = 'learning-supervisor-agent'): Promise<string> {
    try {
      const titlePrompt = `Generate a concise, descriptive title (max 50 characters) for a learning conversation that starts with: "${userMessage}". 
      Focus on the learning goal or career path mentioned. Respond with only the title, no quotes or additional text.`;

      const response = await this.invokeAgent({
        message: titlePrompt,
        user_id: 'system',
        model: 'gpt-4o-mini'
      }, agentId);

      // Clean up the response to extract just the title
      let title = response.content.replace(/['"]/g, '').trim();
      
      // Ensure it's not too long
      if (title.length > 50) {
        title = title.substring(0, 47) + '...';
      }

      return title || 'Learning Conversation';
    } catch (error) {
      console.error('❌ Failed to generate conversation title:', error);
      // Fallback to simple keyword-based title generation
      return this.generateFallbackTitle(userMessage);
    }
  }

  /**
   * Generate an AI response for a user message
   */
  async generateResponse(
    userMessage: string, 
    conversationTitle: string, 
    sessionId: string,
    userId: string,
    agentId: string = 'learning-supervisor-agent'
  ): Promise<string> {
    try {
      const contextualPrompt = `Context: This is a learning conversation titled "${conversationTitle}".

User message: ${userMessage}

Please provide a helpful, personalized response that guides the user in their learning journey. Include specific actionable advice, resources, or next steps when appropriate.`;

      const response = await this.invokeAgent({
        message: contextualPrompt,
        thread_id: sessionId,
        user_id: userId,
        model: 'gpt-4o-mini'
      }, agentId);

      return response.content;
    } catch (error) {
      console.error('❌ Failed to generate AI response:', error);
      // Fallback to a helpful error response
      return this.generateFallbackResponse(userMessage, conversationTitle);
    }
  }

  /**
   * Invoke a specific LangChain agent
   */
  async invokeAgent(input: LangChainUserInput, agentId: string = 'learning-supervisor-agent'): Promise<LangChainResponse> {
    try {
      const endpoint = agentId === 'default' ? '/invoke' : `/${agentId}/invoke`;
      const response: AxiosResponse<LangChainResponse> = await this.apiClient.post(endpoint, input);
      return response.data;
    } catch (error) {
      console.error(`❌ Failed to invoke agent ${agentId}:`, error);
      throw new Error(`Failed to get response from AI agent: ${agentId}`);
    }
  }
  /**
   * Stream response from a LangChain agent (for future implementation)
   */
  async streamAgent(input: LangChainStreamInput, agentId: string = 'learning-supervisor-agent'): Promise<AsyncIterable<string>> {
    // This would be implemented for streaming responses
    // For now, we'll use the regular invoke method and convert to async iterable
    const response = await this.invokeAgent(input, agentId);
    
    // Create a simple async iterable that yields the response
    async function* generateResponse() {
      yield response.content;
    }
    
    return generateResponse();
  }

  /**
   * Check if the LangChain service is healthy
   */
  async isHealthy(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/health', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      console.error('❌ LangChain service health check failed:', error);
      return false;
    }
  }

  /**
   * Fallback title generation when AI service is unavailable
   */
  private generateFallbackTitle(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('solution architect')) {
      return 'Solution Architect Roadmap';
    } else if (lowerMessage.includes('frontend') || lowerMessage.includes('react')) {
      return 'Frontend Development Path';
    } else if (lowerMessage.includes('backend') || lowerMessage.includes('api')) {
      return 'Backend Development Journey';
    } else if (lowerMessage.includes('devops') || lowerMessage.includes('docker') || lowerMessage.includes('kubernetes')) {
      return 'DevOps Engineer Path';
    } else if (lowerMessage.includes('data science') || lowerMessage.includes('machine learning')) {
      return 'Data Science Journey';
    } else if (lowerMessage.includes('mobile') || lowerMessage.includes('ios') || lowerMessage.includes('android')) {
      return 'Mobile Development Path';
    } else if (lowerMessage.includes('career') || lowerMessage.includes('job')) {
      return 'Career Guidance';
    } else {
      return 'Learning Journey';
    }
  }

  /**
   * Fallback response generation when AI service is unavailable
   */
  private generateFallbackResponse(userMessage: string, title: string): string {
    return `Thank you for reaching out about "${title}". I understand you're interested in learning more about this topic.

While I'm temporarily having trouble connecting to our AI learning system, I can still help guide you in the right direction.

Here are some general next steps you might consider:
• Research the fundamental concepts and skills needed
• Look for online courses and tutorials  
• Join relevant communities and forums
• Practice with hands-on projects
• Connect with mentors in the field

Please try again in a few moments, and our full AI-powered learning system should be available to provide you with a personalized roadmap and detailed guidance.

Is there anything specific about your learning goals I can help clarify while we resolve this?`;
  }

  /**
   * Set custom authentication token
   */
  setAuthToken(token: string): void {
    this.apiClient.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Get the base URL of the service
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

// Export singleton instance
export const langChainService = new LangChainService();
