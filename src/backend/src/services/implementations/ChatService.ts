import { PrismaClient } from '@prisma/client';
import {
  IChatService,
  CreateChatSessionDto,
  CreateChatMessageDto,
  ChatSessionWithMessages,
  ChatMessageWithSession,
  ChatSession,
  ChatMessage
} from '../interfaces/IChatService';
import { v4 as uuidv4 } from 'uuid';

export class ChatService implements IChatService {
  constructor(
    private readonly prisma: PrismaClient
  ) {}

  // Helper functions to convert Prisma types to interface types
  private convertPrismaSession(prismaSession: any): ChatSession {
    return {
      ...prismaSession,
      title: prismaSession.title || undefined,
    };
  }

  private convertPrismaMessage(prismaMessage: any): ChatMessage {
    return {
      ...prismaMessage,
      metadata: (prismaMessage.metadata as Record<string, unknown>) || undefined,
    };
  }

  async createChatSession(data: CreateChatSessionDto): Promise<ChatSession> {
    const sessionId = uuidv4();
    
    const session = await this.prisma.chatSession.create({
      data: {
        sessionId,
        userId: data.userId,
        title: data.title,
        updatedAt: new Date(),
      },
    });

    return this.convertPrismaSession(session);
  }

  async getChatSession(sessionId: string): Promise<ChatSessionWithMessages | null> {
    const session = await this.prisma.chatSession.findUnique({
      where: { sessionId },
      include: {
        ChatMessage: {
          orderBy: { messageOrder: 'asc' },
        },
      },
    });

    if (!session) {
      return null;
    }

    return {
      ...this.convertPrismaSession(session),
      messages: session.ChatMessage.map(msg => this.convertPrismaMessage(msg)),
    };
  }

  async getChatSessionsByUser(userId: number): Promise<ChatSession[]> {
    const sessions = await this.prisma.chatSession.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
    });

    return sessions.map(session => this.convertPrismaSession(session));
  }

  async updateChatSessionTitle(sessionId: string, title: string): Promise<ChatSession> {
    const session = await this.prisma.chatSession.update({
      where: { sessionId },
      data: { 
        title,
        updatedAt: new Date(),
      },
    });

    return this.convertPrismaSession(session);
  }

  async deleteChatSession(sessionId: string): Promise<void> {
    // First delete all messages in the session
    await this.prisma.chatMessage.deleteMany({
      where: { sessionId },
    });

    // Then delete the session itself
    await this.prisma.chatSession.delete({
      where: { sessionId },
    });
  }

  async createChatMessage(data: CreateChatMessageDto): Promise<ChatMessage> {
    const messageOrder = await this.getNextMessageOrder(data.sessionId);
    
    const message = await this.prisma.chatMessage.create({
      data: {
        sessionId: data.sessionId,
        userId: data.userId,
        messageType: data.messageType,
        content: data.content,
        messageOrder,
        metadata: data.metadata ? JSON.parse(JSON.stringify(data.metadata)) : null,
        updatedAt: new Date(),
      },
    });

    // Update session timestamp
    await this.prisma.chatSession.update({
      where: { sessionId: data.sessionId },
      data: { updatedAt: new Date() },
    });

    return this.convertPrismaMessage(message);
  }

  async getChatMessages(sessionId: string): Promise<ChatMessage[]> {
    const messages = await this.prisma.chatMessage.findMany({
      where: { sessionId },
      orderBy: { messageOrder: 'asc' },
    });

    return messages.map(msg => this.convertPrismaMessage(msg));
  }

  async getChatMessage(messageId: number): Promise<ChatMessageWithSession | null> {
    const message = await this.prisma.chatMessage.findUnique({
      where: { id: messageId },
      include: {
        ChatSession: true,
      },
    });

    if (!message) {
      return null;
    }

    return {
      ...this.convertPrismaMessage(message),
      session: this.convertPrismaSession(message.ChatSession),
    };
  }

  async deleteChatMessage(messageId: number): Promise<void> {
    await this.prisma.chatMessage.delete({
      where: { id: messageId },
    });
  }

  async getNextMessageOrder(sessionId: string): Promise<number> {
    const lastMessage = await this.prisma.chatMessage.findFirst({
      where: { sessionId },
      orderBy: { messageOrder: 'desc' },
      select: { messageOrder: true },
    });

    return lastMessage ? lastMessage.messageOrder + 1 : 1;
  }

  // Roadmap methods - these use Prisma for database operations
  async getChatSessionRoadmap(sessionId: string): Promise<string | null> {
    const session = await this.prisma.chatSession.findUnique({
      where: { sessionId },
    });
    return (session as any)?.roadmap || null;
  }

  async updateChatSessionRoadmap(sessionId: string, roadmap: string): Promise<ChatSession> {
    const session = await this.prisma.chatSession.update({
      where: { sessionId },
      data: { 
        roadmap,
        updatedAt: new Date()
      } as any
    });

    return {
      ...session,
      title: session.title || undefined,
    };
  }
}

export default ChatService;
