import { User, UserSkill, Skill, Category } from '@prisma/client';

export interface CreateUserDto {
  name: string;
  email: string;
  password?: string; // Optional for existing users, required for new registrations
  role?: string;
  avatarUrl?: string; // Profile picture URL
  oauthProvider?: string; // OAuth provider (google, github, etc.)
  oauthId?: string; // OAuth provider user ID
}

export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  username?: string; // Optional for backward compatibility
  firstName?: string; // For frontend compatibility
  lastName?: string; // For frontend compatibility
  email: string;
  password: string;
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
}

export interface AddSkillToUserDto {
  skillId: number;
}

export interface UserWithSkills extends Omit<User, 'password'> {
  skills: (UserSkill & { skill: Skill })[];
}

export interface IUserService {
  // Authentication methods
  register(data: RegisterDto): Promise<AuthResponse>;
  login(data: LoginDto): Promise<AuthResponse>;
    // User management methods
  getUsers(): Promise<Omit<User, 'password'>[]>;
  getUserById(id: number): Promise<UserWithSkills | null>;
  getBasicUserById(id: number): Promise<Omit<User, 'password'> | null>;
  getUserByEmail(email: string): Promise<User | null>;
  getUserSkills(userId: number): Promise<(UserSkill & { Skill: Skill & { Category: Category } })[]>;
  createUser(data: CreateUserDto): Promise<Omit<User, 'password'>>;
  updateUser(id: number, data: Partial<CreateUserDto>): Promise<Omit<User, 'password'>>;
  deleteUser(id: number): Promise<void>;
  addSkillToUser(userId: number, data: AddSkillToUserDto): Promise<UserSkill>;
  removeSkillFromUser(userId: number, skillId: number): Promise<void>;
  
  // Convenience methods for compatibility
  create(data: CreateUserDto): Promise<UserWithSkills>;
  findById(id: number): Promise<UserWithSkills | null>;
  findByEmail(email: string): Promise<UserWithSkills | null>;
  update(id: number, data: Partial<CreateUserDto>): Promise<UserWithSkills>;
}
