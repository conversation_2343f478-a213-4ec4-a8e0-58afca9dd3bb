import { Request, Response } from 'express';
import { ServiceRegistry } from '../services';
import { RegisterDto, LoginDto } from '../services/interfaces/IUserService';
import { AuthenticatedRequest } from '../middleware/auth';

/**
 * @swagger
 * tags:
 *   name: Authentication
 *   description: User authentication endpoints
 */
export class AuthController {
  constructor(private readonly serviceRegistry: ServiceRegistry) {}

  /**
   * @swagger
   * /api/auth/register:
   *   post:
   *     summary: Register a new user
   *     tags: [Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/RegisterDto'
   *     responses:
   *       201:
   *         description: User registered successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AuthResponse'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       409:
   *         description: User already exists
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public register = async (req: Request, res: Response): Promise<void> => {
    try {
      const { username, firstName, lastName, email, password }: RegisterDto = req.body;
      
      if (!email || !password) {
        res.status(400).json({ error: 'Email and password are required' });
        return;
      }

      // Generate username if not provided but firstName/lastName are available
      let finalUsername = username;
      if (!finalUsername && (firstName || lastName)) {
        finalUsername = `${firstName || ''} ${lastName || ''}`.trim();
      }
      
      if (!finalUsername) {
        res.status(400).json({ error: 'Username or firstName/lastName are required' });
        return;
      }

      const userService = this.serviceRegistry.getUserService();
      const result = await userService.register({ 
        username: finalUsername, 
        firstName, 
        lastName, 
        email, 
        password 
      });
      
      res.status(201).json(result);} catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      console.error('Registration error:', error);
      
      if (message.includes('already exists')) {
        res.status(409).json({ error: message });
      } else {
        res.status(500).json({ error: message || 'Failed to register user' });
      }
    }
  };

  /**
   * @swagger
   * /api/auth/login:
   *   post:
   *     summary: Login user
   *     tags: [Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/LoginDto'
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/AuthResponse'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         description: Invalid credentials
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public login = async (req: Request, res: Response): Promise<void> => {
    try {
      const { email, password }: LoginDto = req.body;
      
      if (!email || !password) {
        res.status(400).json({ error: 'Email and password are required' });
        return;
      }

      const userService = this.serviceRegistry.getUserService();
      const result = await userService.login({ email, password });
      
      res.status(200).json(result);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      
      if (message.includes('Invalid email or password')) {
        res.status(401).json({ error: message });
      } else {
        res.status(500).json({ error: 'Failed to login' });
      }
    }
  };

  /**
   * @swagger
   * /api/auth/me:
   *   get:
   *     summary: Get current user profile
   *     tags: [Authentication]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Current user profile
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/User'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getCurrentUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      console.log('🔍 getCurrentUser called');
      console.log('🔍 req.user:', req.user);

      if (!req.user) {
        console.log('❌ User not authenticated');
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }      console.log('🔍 Getting user by ID:', req.user.userId);
      const userService = this.serviceRegistry.getUserService();
      const user = await userService.getUserById(req.user.userId);
      
      console.log('🔍 User found:', user ? 'Yes' : 'No');
      
      if (!user) {
        console.log('❌ User not found for ID:', req.user.userId);
        res.status(404).json({ error: 'User not found' });
        return;
      }      console.log('✅ Returning user data');
      res.status(200).json(user);
    } catch (error) {
      console.error('❌ Error in getCurrentUser:', error);
      res.status(500).json({ error: 'Failed to get user profile' });
    }
  };
}
