import { Request, Response } from 'express';
import { ServiceRegistry } from '../services';
import { LangChainService } from '../services/implementations/LangChainService';

interface AuthenticatedRequest extends Request {
  user?: {
    userId: number;
    email: string;
    role: string;
  };
}

/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: Chat management endpoints
 */
export class ChatController {
  private readonly langChainService: LangChainService;

  constructor(private readonly serviceRegistry: ServiceRegistry) {
    this.langChainService = new LangChainService();
  }
  /**
   * @swagger
   * /api/chat/sessions:
   *   post:
   *     summary: Create a new chat session
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *                 description: Optional title for the chat session
   *     responses:
   *       201:
   *         description: Chat session created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ChatSession'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public createChatSession = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { title } = req.body;

      const chatService = this.serviceRegistry.getChatService();
      const result = await chatService.createChatSession({
        userId,
        title
      });

      res.status(201).json(result);
    } catch (error) {
      console.error('Error creating chat session:', error);
      res.status(500).json({ error: 'Failed to create chat session' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions:
   *   get:
   *     summary: Get all chat sessions for the authenticated user
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of user's chat sessions
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/ChatSession'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public getChatSessions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const chatService = this.serviceRegistry.getChatService();
      const sessions = await chatService.getChatSessionsByUser(userId);

      res.json(sessions);
    } catch (error) {
      console.error('Error getting chat sessions:', error);
      res.status(500).json({ error: 'Failed to get chat sessions' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}:
   *   get:
   *     summary: Get a specific chat session with messages
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     responses:
   *       200:
   *         description: Chat session with messages
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ChatSessionWithMessages'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public getChatSession = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;

      const chatService = this.serviceRegistry.getChatService();
      const session = await chatService.getChatSession(sessionId);

      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      // Check if user owns this session
      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      res.json(session);
    } catch (error) {
      console.error('Error getting chat session:', error);
      res.status(500).json({ error: 'Failed to get chat session' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}/messages:
   *   post:
   *     summary: Send a message to a chat session
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - content
   *               - messageType
   *             properties:
   *               content:
   *                 type: string
   *                 description: The message content
   *               messageType:
   *                 type: string
   *                 enum: [user, assistant]
   *                 description: Type of message
   *               metadata:
   *                 type: object
   *                 description: Optional metadata
   *     responses:
   *       201:
   *         description: Message sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ChatMessage'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public sendMessage = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;
      const { content, messageType, metadata } = req.body;

      if (!content || !messageType) {
        res.status(400).json({ error: 'Content and messageType are required' });
        return;
      }

      if (!['user', 'assistant'].includes(messageType)) {
        res.status(400).json({ error: 'messageType must be either "user" or "assistant"' });
        return;
      }

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }      const result = await chatService.createChatMessage({
        sessionId,
        userId,
        messageType,
        content,
        metadata
      });

      // If this is a user message, automatically generate an AI response
      if (messageType === 'user') {
        try {
          const aiResponse = await this.langChainService.generateResponse(
            content,
            session.title || 'Learning Conversation',
            sessionId,
            userId.toString()
          );

          // Create the AI assistant response
          const assistantMessage = await chatService.createChatMessage({
            sessionId,
            userId,
            messageType: 'assistant',
            content: aiResponse,
            metadata: { generatedBy: 'ai', timestamp: new Date().toISOString() }
          });

          // Return both the user message and AI response
          res.status(201).json({
            userMessage: result,
            aiResponse: assistantMessage
          });
        } catch (aiError) {
          console.error('Error generating AI response:', aiError);
          // Still return the user message even if AI response fails
          res.status(201).json({ 
            userMessage: result,
            aiResponse: null,
            warning: 'AI response generation failed'
          });
        }
      } else {
        // For assistant messages, just return the message
        res.status(201).json(result);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      res.status(500).json({ error: 'Failed to send message' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}/messages:
   *   get:
   *     summary: Get all messages for a chat session
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     responses:
   *       200:
   *         description: List of messages in the chat session
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/ChatMessage'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public getChatMessages = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const messages = await chatService.getChatMessages(sessionId);
      res.json(messages);
    } catch (error) {
      console.error('Error getting chat messages:', error);
      res.status(500).json({ error: 'Failed to get chat messages' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}:
   *   put:
   *     summary: Update chat session title
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *             properties:
   *               title:
   *                 type: string
   *                 description: New title for the chat session
   *     responses:
   *       200:
   *         description: Chat session updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ChatSession'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public updateChatSession = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;
      const { title } = req.body;

      if (!title) {
        res.status(400).json({ error: 'Title is required' });
        return;
      }

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const updatedSession = await chatService.updateChatSessionTitle(sessionId, title);
      res.json(updatedSession);
    } catch (error) {
      console.error('Error updating chat session:', error);
      res.status(500).json({ error: 'Failed to update chat session' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}:
   *   delete:
   *     summary: Delete a chat session and all its messages
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     responses:
   *       204:
   *         description: Chat session deleted successfully
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */  public deleteChatSession = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      await chatService.deleteChatSession(sessionId);      res.status(204).send();
    } catch (error) {
      console.error('Error deleting chat session:', error);
      res.status(500).json({ error: 'Failed to delete chat session' });
    }
  };

  /**
   * @swagger
   * /api/chat/start-conversation:
   *   post:
   *     summary: Start a new conversation with the chatbot
   *     description: Creates a new chat session and sends the first user message. The AI will respond with a generated title and assistant message.
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - message
   *             properties:
   *               message:
   *                 type: string
   *                 description: The initial user message to start the conversation
   *                 example: "I want to become a Solution Architect"
   *     responses:
   *       201:
   *         description: Conversation started successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 session:
   *                   $ref: '#/components/schemas/ChatSession'
   *                 messages:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/ChatMessage'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public startConversation = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { message } = req.body;

      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        res.status(400).json({ error: 'Message is required and cannot be empty' });
        return;
      }      const chatService = this.serviceRegistry.getChatService();

      // Generate AI title using LangChain service
      const aiGeneratedTitle = await this.langChainService.generateConversationTitle(message);

      // Create new chat session with AI-generated title
      const session = await chatService.createChatSession({
        userId,
        title: aiGeneratedTitle
      });

      // Create the initial user message
      const userMessage = await chatService.createChatMessage({
        sessionId: session.sessionId,
        userId,
        messageType: 'user',
        content: message.trim()
      });      // Generate AI response using LangChain service
      const aiResponse = await this.langChainService.generateResponse(
        message,
        aiGeneratedTitle,
        session.sessionId,
        userId.toString()
      );

      // Create the AI assistant response
      const assistantMessage = await chatService.createChatMessage({
        sessionId: session.sessionId,
        userId,
        messageType: 'assistant',
        content: aiResponse
      });

      res.status(201).json({
        session,
        messages: [userMessage, assistantMessage]
      });
    } catch (error) {
      console.error('Error starting conversation:', error);
      res.status(500).json({ error: 'Failed to start conversation' });
    }
  };
  /**
   * @swagger
   * /api/chat/debug-auth:
   *   post:
   *     summary: Debug authentication issues (development only)
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Debug information returned
   *       401:
   *         description: Authentication failed
   */
  public debugAuth = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      const environment = process.env.NODE_ENV;
      
      // Only allow in development
      if (environment !== 'development') {
        res.status(403).json({ error: 'Debug endpoint only available in development' });
        return;
      }

      res.json({
        message: 'Authentication successful',
        user: req.user,
        authHeader: authHeader ? `${authHeader.substring(0, 20)}...` : 'missing',
        environment,
        jwtSecret: process.env.JWT_SECRET ? 'present' : 'missing',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: 'Debug failed', details: error instanceof Error ? error.message : 'Unknown error' });
    }
  };

  // ...existing code...

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}/roadmap:
   *   get:
   *     summary: Get roadmap for a chat session
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     responses:
   *       200:
   *         description: Roadmap retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 roadmap:
   *                   type: string
   *                   nullable: true
   *                   description: The roadmap content for the session
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getChatSessionRoadmap = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const roadmap = await chatService.getChatSessionRoadmap(sessionId);
      res.json({ roadmap });
    } catch (error) {
      console.error('Error getting chat session roadmap:', error);
      res.status(500).json({ error: 'Failed to get chat session roadmap' });
    }
  };

  /**
   * @swagger
   * /api/chat/sessions/{sessionId}/roadmap:
   *   post:
   *     summary: Update roadmap for a chat session
   *     tags: [Chat]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: sessionId
   *         required: true
   *         schema:
   *           type: string
   *         description: Chat session ID (threadId)
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - roadmap
   *             properties:
   *               roadmap:
   *                 type: string
   *                 description: The roadmap content to save
   *     responses:
   *       200:
   *         description: Roadmap updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ChatSession'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public updateChatSessionRoadmap = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      const { sessionId } = req.params;
      const { roadmap } = req.body;

      if (typeof roadmap !== 'string') {
        res.status(400).json({ error: 'Roadmap must be a string' });
        return;
      }

      const chatService = this.serviceRegistry.getChatService();
      
      // Verify session exists and user owns it
      const session = await chatService.getChatSession(sessionId);
      if (!session) {
        res.status(404).json({ error: 'Chat session not found' });
        return;
      }

      if (session.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      const updatedSession = await chatService.updateChatSessionRoadmap(sessionId, roadmap);
      res.json(updatedSession);
    } catch (error) {
      console.error('Error updating chat session roadmap:', error);
      res.status(500).json({ error: 'Failed to update chat session roadmap' });
    }
  };
}
