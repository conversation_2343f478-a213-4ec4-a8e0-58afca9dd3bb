// Simple test script to verify roadmap functionality works
const { PrismaClient } = require('@prisma/client');

async function testRoadmapFunctionality() {
  console.log('🚀 Starting roadmap functionality test...');
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Testing roadmap functionality...');
    
    // Test 1: Check if we can connect to database
    console.log('1️⃣ Testing database connection...');
    const userCount = await prisma.user.count();
    console.log(`✅ Database connected. Found ${userCount} users.`);
    
    // Test 2: Check if ChatSession model has roadmap field
    console.log('2️⃣ Testing ChatSession model...');
    const sessionCount = await prisma.chatSession.count();
    console.log(`✅ ChatSession model accessible. Found ${sessionCount} sessions.`);
    
    // Test 3: Try to find a session to test roadmap functionality
    console.log('3️⃣ Testing roadmap functionality...');
    const sessions = await prisma.chatSession.findMany({
      take: 1
    });
    
    if (sessions.length > 0) {
      const testSession = sessions[0];
      console.log(`✅ Found test session: ${testSession.sessionId}`);
      console.log(`   Current roadmap: ${testSession.roadmap || 'null'}`);
      
      // Test 4: Update roadmap
      console.log('4️⃣ Testing roadmap update...');
      const testRoadmap = `Test roadmap updated at ${new Date().toISOString()}`;
      
      const updatedSession = await prisma.chatSession.update({
        where: { sessionId: testSession.sessionId },
        data: { roadmap: testRoadmap }
      });
      
      console.log(`✅ Roadmap updated successfully: ${updatedSession.roadmap}`);
      
      // Test 5: Read roadmap back
      console.log('5️⃣ Testing roadmap retrieval...');
      const retrievedSession = await prisma.chatSession.findUnique({
        where: { sessionId: testSession.sessionId },
        select: { roadmap: true, sessionId: true }
      });
      
      console.log(`✅ Roadmap retrieved: ${retrievedSession.roadmap}`);
      
    } else {
      console.log('⚠️  No sessions found to test with');
    }
    
    console.log('🎉 All roadmap functionality tests passed!');
      } catch (error) {
    console.error('❌ Error during testing:', error);
    console.error('❌ Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database disconnected');
  }
}

console.log('📝 Script loaded, calling test function...');
testRoadmapFunctionality()
  .then(() => {
    console.log('✅ Test function completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
