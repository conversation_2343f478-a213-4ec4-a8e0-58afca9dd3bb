# Test script to verify JWT authentication is working
# Run this script to test the authentication flow

Write-Host "🔧 Testing JWT Authentication..." -ForegroundColor Green

# Test 1: Health check
Write-Host "`n1. Testing server health..."
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8080/health"
    Write-Host "✅ Server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not running. Please start it with 'npm run dev'" -ForegroundColor Red
    exit 1
}

# Test 2: Login
Write-Host "`n2. Testing login..."
try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method POST -ContentType "application/json" -Body '{"email":"<EMAIL>","password":"TestPass123!"}'
    $token = $loginResponse.token
    Write-Host "✅ Login successful" -ForegroundColor Green
    Write-Host "Token: $($token.Substring(0,30))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure the user exists or register a new one" -ForegroundColor Yellow
    exit 1
}

# Test 3: Authentication debug
Write-Host "`n3. Testing authentication..."
$headers = @{ 
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json" 
}

try {
    $debugResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/chat/debug-auth" -Method POST -Headers $headers
    Write-Host "✅ Authentication successful" -ForegroundColor Green
    Write-Host "User ID: $($debugResponse.user.userId)" -ForegroundColor Gray
    Write-Host "Email: $($debugResponse.user.email)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# Test 4: Chat endpoint
Write-Host "`n4. Testing chat endpoint..."
try {
    $chatResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/chat/start-conversation" -Method POST -Headers $headers -Body '{"message":"Test authentication flow"}'
    Write-Host "✅ Chat endpoint successful" -ForegroundColor Green
    Write-Host "Session: $($chatResponse.session.title)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Chat endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 All tests passed! Authentication is working correctly." -ForegroundColor Green
Write-Host "`nYour fresh token is:" -ForegroundColor Yellow
Write-Host $token -ForegroundColor Cyan
Write-Host "`nTo use this token in your requests, include this header:" -ForegroundColor Yellow
Write-Host "Authorization: Bearer $token" -ForegroundColor Cyan
