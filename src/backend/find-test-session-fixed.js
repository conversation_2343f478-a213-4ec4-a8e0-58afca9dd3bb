// Find session for user ID 11 to test roadmap endpoints
const { PrismaClient } = require('@prisma/client');

console.log('📝 Script starting...');

async function findTestSession() {
  console.log('🔍 Finding test session for user ID 11...');
  const prisma = new PrismaClient();
  
  try {
    console.log('🔌 Connected to database');
    
    // Find a session for user ID 11
    console.log('🔎 Searching for existing session...');
    const session = await prisma.chatSession.findFirst({
      where: { userId: 11 },
      select: { sessionId: true, userId: true, roadmap: true, title: true }
    });
    
    if (session) {
      console.log('✅ Found session for user 11:');
      console.log(`   Session ID: ${session.sessionId}`);
      console.log(`   User ID: ${session.userId}`);
      console.log(`   Title: ${session.title || 'No title'}`);
      console.log(`   Current roadmap: ${session.roadmap || 'null'}`);
      
      return session.sessionId;
    } else {
      console.log('⚠️  No sessions found for user ID 11');
      
      // Create a test session for user 11
      console.log('🔧 Creating test session for user 11...');
      const newSession = await prisma.chatSession.create({
        data: {
          sessionId: `test-session-${Date.now()}`,
          userId: 11,
          title: 'Test Roadmap Session',
          roadmap: 'Initial test roadmap'
        }
      });
      
      console.log('✅ Created test session:');
      console.log(`   Session ID: ${newSession.sessionId}`);
      console.log(`   User ID: ${newSession.userId}`);
      console.log(`   Title: ${newSession.title}`);
      
      return newSession.sessionId;
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
    return null;
  } finally {
    console.log('🔌 Disconnecting from database...');
    await prisma.$disconnect();
  }
}

console.log('🚀 Calling findTestSession...');
findTestSession()
  .then((sessionId) => {
    if (sessionId) {
      console.log(`\n🧪 Test this session with:`);
      console.log(`   GET:  curl "http://localhost:3001/api/chat/sessions/${sessionId}/roadmap"`);
      console.log(`   POST: curl -X POST -H "Content-Type: application/json" -d "{\\"roadmap\\":\\"Updated roadmap content\\"}" "http://localhost:3001/api/chat/sessions/${sessionId}/roadmap"`);
    }
    console.log('✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error:', error);
    process.exit(1);
  });
