/*
  Add firstName and lastName to User model
  This migration adds firstName and lastN<PERSON> fields to support frontend requirements
  while keeping username for backward compatibility
*/

-- AddColumn
ALTER TABLE "User" ADD COLUMN "firstName" TEXT;
ALTER TABLE "User" ADD COLUMN "lastName" TEXT;

-- Set default values for existing users by splitting username
-- This is a temporary solution - in production you'd handle this more carefully
UPDATE "User" 
SET 
  "firstName" = CASE 
    WHEN "username" LIKE '% %' THEN split_part("username", ' ', 1)
    ELSE "username"
  END,
  "lastName" = CASE 
    WHEN "username" LIKE '% %' THEN substring("username" from position(' ' in "username") + 1)
    ELSE ''
  END
WHERE "firstName" IS NULL OR "lastName" IS NULL;
