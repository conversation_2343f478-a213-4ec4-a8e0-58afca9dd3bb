generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime
  Skill       Skill[]
}

model ChatMessage {
  id           Int         @id @default(autoincrement())
  sessionId    String
  userId       Int
  messageType  String
  content      String
  messageOrder Int
  metadata     Json?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime
  ChatSession  ChatSession @relation(fields: [sessionId], references: [sessionId])
  User         User        @relation(fields: [userId], references: [id])

  @@index([sessionId, messageOrder])
}

model ChatSession {
  id          Int           @id @default(autoincrement())
  sessionId   String        @unique
  userId      Int
  title       String?
  roadmap     String?       @default("")
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  ChatMessage ChatMessage[]
  User        User          @relation(fields: [userId], references: [id])
}

model Skill {
  id          Int         @id @default(autoincrement())
  name        String
  description String?
  categoryId  Int
  createdAt   DateTime    @default(now())
  updatedAt   DateTime
  Category    Category    @relation(fields: [categoryId], references: [id])
  UserSkill   UserSkill[]
}

model User {
  id            Int           @id @default(autoincrement())
  username      String        @unique
  email         String        @unique
  password      String
  role          String        @default("employee")
  avatarUrl     String?
  oauthProvider String?
  oauthId       String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime
  firstName     String?
  lastName      String?
  ChatMessage   ChatMessage[]
  ChatSession   ChatSession[]
  UserSkill     UserSkill[]
}

model UserSkill {
  id        Int      @id @default(autoincrement())
  userId    Int
  skillId   Int
  createdAt DateTime @default(now())
  updatedAt DateTime
  Skill     Skill    @relation(fields: [skillId], references: [id])
  User      User     @relation(fields: [userId], references: [id])

  @@unique([userId, skillId])
}

model checkpoint_blobs {
  thread_id     String
  checkpoint_ns String @default("")
  channel       String
  version       String
  type          String
  blob          Bytes?

  @@id([thread_id, checkpoint_ns, channel, version])
  @@index([thread_id])
}

model checkpoint_migrations {
  v Int @id
}

model checkpoint_writes {
  thread_id     String
  checkpoint_ns String  @default("")
  checkpoint_id String
  task_id       String
  idx           Int
  channel       String
  type          String?
  blob          Bytes
  task_path     String  @default("")

  @@id([thread_id, checkpoint_ns, checkpoint_id, task_id, idx])
  @@index([thread_id])
}

model checkpoints {
  thread_id            String
  checkpoint_ns        String  @default("")
  checkpoint_id        String
  parent_checkpoint_id String?
  type                 String?
  checkpoint           Json
  metadata             Json    @default("{}")

  @@id([thread_id, checkpoint_ns, checkpoint_id])
  @@index([thread_id])
}

model people_skill_set {
  id            Int                    @id @default(autoincrement())
  user_id       String                 @db.VarChar
  source_id     String                 @db.VarChar
  full_name     String
  email         String?
  chunk_type    String
  chunk_subtype String?
  content       String
  embedding     Unsupported("vector")?
  metadata      Json?                  @default("{}")
  created_at    DateTime?              @default(now()) @db.Timestamp(6)

  @@index([email], map: "idx_people_skill_set_email")
  @@index([embedding], map: "idx_people_skill_set_embedding")
  @@index([full_name], map: "idx_people_skill_set_full_name")
  @@index([metadata], map: "idx_people_skill_set_metadata_gin", type: Gin)
  @@index([chunk_type, chunk_subtype], map: "idx_people_skill_set_type_subtype")
  @@index([user_id], map: "idx_people_skill_set_user_id")
}

model store {
  prefix      String
  key         String
  value       Json
  created_at  DateTime? @default(now()) @db.Timestamptz(6)
  updated_at  DateTime? @default(now()) @db.Timestamptz(6)
  expires_at  DateTime? @db.Timestamptz(6)
  ttl_minutes Int?

  @@id([prefix, key])
  @@index([prefix])
}

model store_migrations {
  v Int @id
}
