// Direct test of ChatServicePrisma without ServiceRegistry
const { PrismaClient } = require('@prisma/client');
const { ChatServicePrisma } = require('./dist/src/services/implementations/ChatServicePrisma');

console.log('Testing ChatServicePrisma directly...');

async function testDirectChatServicePrisma() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing ChatServicePrisma directly...\n');

    // 1. Create ChatServicePrisma instance
    console.log('1. Creating ChatServicePrisma instance...');
    const chatService = new ChatServicePrisma(prisma);
    console.log('✅ ChatServicePrisma created:', chatService.constructor.name);

    // 2. Test session creation
    console.log('\n2. Testing session creation...');
    const testSession = await chatService.createChatSession({
      userId: 1,
      title: 'Direct Test Session'
    });
    console.log('✅ Session created:', testSession.sessionId);
    console.log('   Title:', testSession.title);

    // 3. Test message creation
    console.log('\n3. Testing message creation...');
    const message1 = await chatService.createChatMessage({
      sessionId: testSession.sessionId,
      userId: 1,
      messageType: 'user',
      content: 'First message in direct test',
      metadata: { directTest: true }
    });
    console.log('✅ Message created with order:', message1.messageOrder);

    // 4. Verify database persistence immediately
    console.log('\n4. Verifying database persistence...');
    const dbSession = await prisma.chatSession.findUnique({
      where: { sessionId: testSession.sessionId },
      include: { ChatMessage: true }
    });
    
    if (dbSession) {
      console.log('✅ Session found in database!');
      console.log('   Database title:', dbSession.title);
      console.log('   Database messages:', dbSession.ChatMessage.length);
    } else {
      console.log('❌ Session NOT found in database!');
    }

    console.log('\n🎉 Direct ChatServicePrisma test completed successfully!');

  } catch (error) {
    console.error('❌ Direct test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDirectChatServicePrisma();
