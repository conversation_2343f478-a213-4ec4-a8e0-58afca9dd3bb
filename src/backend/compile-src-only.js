const { exec } = require('child_process');
const path = require('path');

// Compile only the src directory, excluding tests
const command = 'npx tsc src/**/*.ts --outDir dist --target ES2020 --module ESNext --strict --esModuleInterop --skipLibCheck --experimentalDecorators --emitDecoratorMetadata --moduleResolution node --declaration false';

console.log('Compiling source files only...');
exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error('Compilation error:', error);
    return;
  }
  if (stdout) console.log(stdout);
  if (stderr) console.error(stderr);
  console.log('Compilation completed');
});
