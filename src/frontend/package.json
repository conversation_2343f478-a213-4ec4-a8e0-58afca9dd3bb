{"name": "pathforge-ai-frontend", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "tsc --project tsconfig.build.json && vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint . --cache", "stylelint": "stylelint '**/*.css' --cache", "prettier": "prettier --check \"**/*.{ts,tsx}\"", "prettier:write": "prettier --write \"**/*.{ts,tsx}\"", "vitest": "vitest run", "vitest:watch": "vitest", "vitest:coverage": "vitest run --coverage", "vitest:coverage100": "vitest run --coverage --coverage.threshold=100", "test": "npm run typecheck && npm run prettier && npm run lint && npm run vitest && npm run build", "test:coverage": "npm run typecheck && npm run prettier && npm run lint && npm run vitest:coverage", "test:coverage100": "npm run typecheck && npm run prettier && npm run lint && npm run vitest:coverage100", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "docker:build": "docker build -f ../../docker/Dockerfile.frontend -t pathforge-ai-frontend ../../", "docker:run": "docker run -p 8080:80 pathforge-ai-frontend", "prepare": "test \"$CI\" = true || husky"}, "dependencies": {"@mantine/core": "8.0.2", "@mantine/dropzone": "^8.0.2", "@mantine/hooks": "8.0.2", "@mantine/notifications": "^8.0.2", "@rollup/rollup-darwin-arm64": "^4.41.1", "@tabler/icons-react": "^3.33.0", "@xyflow/react": "^12.6.2", "axios": "^1.9.0", "chart.js": "^4.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.26.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.11", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-istanbul": "^3.1.3", "@vitest/coverage-v8": "^3.1.3", "eslint": "^9.26.0", "eslint-config-mantine": "^4.0.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "postcss": "^8.5.3", "postcss-preset-mantine": "1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.5.3", "prop-types": "^15.8.1", "storybook": "^8.6.12", "storybook-dark-mode": "^4.0.2", "stylelint": "^16.19.1", "stylelint-config-standard-scss": "^14.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3"}, "lint-staged": {"**/*.{ts,tsx}": ["prettier --write", "eslint --fix"], "**/*.css": ["stylelint --fix"]}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.41.1"}}