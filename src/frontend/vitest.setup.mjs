import '@testing-library/jest-dom/vitest';
import { vi, beforeEach } from 'vitest';

// Create comprehensive matchMedia mock function
const createMatchMediaMock = () => vi.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(), // deprecated
  removeListener: vi.fn(), // deprecated
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Set up matchMedia mock in beforeEach to ensure it's available for each test
beforeEach(() => {
  const matchMediaMock = createMatchMediaMock();

  // Mock for JSDOM window object
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    configurable: true,
    value: matchMediaMock,
  });

  // Also mock on global objects for safety
  Object.defineProperty(global, 'matchMedia', {
    writable: true,
    configurable: true,
    value: matchMediaMock,
  });

  Object.defineProperty(globalThis, 'matchMedia', {
    writable: true,
    configurable: true,
    value: matchMediaMock,
  });
});

// Additional window setup for JSDOM
if (typeof window !== 'undefined') {
  const { getComputedStyle } = window;
  if (getComputedStyle) {
    window.getComputedStyle = (elt) => getComputedStyle(elt);
  }

  if (window.HTMLElement && window.HTMLElement.prototype) {
    window.HTMLElement.prototype.scrollIntoView = () => { };
  }
}

// ResizeObserver mock
class ResizeObserver {
  observe() {
    // Mock implementation - no actual observation needed in tests
  }
  unobserve() {
    // Mock implementation - no actual unobservation needed in tests
  }
  disconnect() {
    // Mock implementation - no actual disconnection needed in tests
  }
}

if (typeof window !== 'undefined') {
  window.ResizeObserver = ResizeObserver;
}
if (typeof global !== 'undefined') {
  global.ResizeObserver = ResizeObserver;
}
if (typeof globalThis !== 'undefined') {
  globalThis.ResizeObserver = ResizeObserver;
}
