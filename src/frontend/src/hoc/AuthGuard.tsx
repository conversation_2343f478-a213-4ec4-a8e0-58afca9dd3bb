import { PropsWithChildren, useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

export default function AuthGuard(props: PropsWithChildren) {
  const { children } = props;
  const { isAuthenticated } = useAuthentication();

  // Get current path for redirect after login
  const location = useLocation();

  const navigateToSignInPage = useCallback(() => {
    // Encode the current path to handle special characters and query parameters
    const currentPath = location.pathname + location.search;
    const redirectParam = encodeURIComponent(currentPath);
    window.location.assign(`/sign-in?redirect=${redirectParam}`);
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      navigateToSignInPage();
    }
  }, [isAuthenticated, navigateToSignInPage]);

  if (!isAuthenticated) {
    // Return null while navigation is happening
    return;
  }

  return <>{children}</>;
}
