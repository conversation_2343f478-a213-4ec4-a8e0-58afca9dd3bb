import { PropsWithChildren, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

/**
 * HOC that redirects first-time users (who haven't uploaded a CV) to the CV upload page
 * This ensures new users complete their profile setup before accessing the main app
 */
export default function FirstTimeUserGuard(props: PropsWithChildren) {
  const { children } = props;
  const { isAuthenticated, user, isLoading } = useAuthentication();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) {
      return;
    }

    // Don't redirect if not authenticated (AuthGuard will handle this)
    if (!isAuthenticated || !user) {
      return;
    }

    // Don't redirect if already on CV upload page
    if (location.pathname === '/cv-upload') {
      return;
    }

    // Don't redirect if user has already uploaded a CV
    if (user.hasUploadedCV) {
      return;
    }

    // Redirect first-time users to CV upload
    navigate('/cv-upload', { replace: true });
  }, [isLoading, isAuthenticated, user, location.pathname, navigate]);

  // Don't redirect while loading
  if (isLoading) {
    return null;
  }

  // Don't redirect if not authenticated (AuthGuard will handle this)
  if (!isAuthenticated || !user) {
    return <>{children}</>;
  }

  // Don't redirect if already on CV upload page
  if (location.pathname === '/cv-upload') {
    return <>{children}</>;
  }

  // Don't redirect if user has already uploaded a CV
  if (user.hasUploadedCV) {
    return <>{children}</>;
  }

  // Return null while navigation is happening
  return null;
}
