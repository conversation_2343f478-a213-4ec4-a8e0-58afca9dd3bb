import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

// This HOC is used for users who are not authenticated to access the authentication pages.

export default function GuestOnly({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthentication();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      // Check if there's a redirect parameter to go back to the original page
      const searchParams = new URLSearchParams(location.search);
      const redirectTo = searchParams.get('redirect');

      // If there's a valid redirect path, go there; otherwise go to dashboard
      const destination = redirectTo && redirectTo.startsWith('/') ? redirectTo : '/dashboard';
      navigate(destination, { replace: true });
    }
  }, [isAuthenticated, location.search, navigate]);

  if (isAuthenticated) {
    // Return null while navigation is happening
    return null;
  }

  return <>{children}</>;
}
