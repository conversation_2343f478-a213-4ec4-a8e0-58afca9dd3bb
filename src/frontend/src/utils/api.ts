export interface LearningAgentPayload {
  message: string;
  thread_id: string;
  user_id: string;
  stream_tokens: boolean;
}

export interface LearningAgentResponse {
  success: boolean;
  error?: string;
  content?: string; // Complete accumulated content
}

/**
 * Base URL for API requests
 * Uses localhost:8080 based on existing backend configuration
 */
const API_BASE_URL = 'http://localhost:8080';

/**
 * Makes an API call to the learning supervisor agent and returns the complete response
 * @param payload - The request payload
 * @returns Promise that resolves with the complete response content
 */
export async function streamLearningAgent(
  payload: LearningAgentPayload
): Promise<LearningAgentResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/learning-supervisor-agent/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add mock Bearer token for development
        Authorization: 'Bearer mock-token-for-development',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let accumulatedText = ''; // Track accumulated text across all tokens

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        // Decode the chunk
        const chunkText = decoder.decode(value, { stream: true });

        // Split by newlines in case multiple chunks are received together
        const lines = chunkText.split('\n').filter((line) => line.trim());

        for (const line of lines) {
          // Check if line follows SSE format: "data: {...}"
          if (line.startsWith('data: ')) {
            try {
              // Extract JSON payload after "data: " prefix
              const jsonPayload = line.substring(6); // Remove "data: " prefix
              const parsedData = JSON.parse(jsonPayload);

              // Handle token accumulation for streaming text
              if (
                parsedData.type === 'message' &&
                parsedData?.content &&
                parsedData?.content?.type === 'ai'
              ) {
                accumulatedText += parsedData?.content?.content || '';
              }
            } catch (parseError) {
              accumulatedText += '\n';
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return {
      success: true,
      content: accumulatedText,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Creates a learning agent payload from the provided parameters
 */
export function createLearningAgentPayload(
  message: string,
  threadId: string,
  userId: string = 'dainq11'
): LearningAgentPayload {
  return {
    message: message.trim(),
    thread_id: threadId,
    user_id: userId,
    stream_tokens: false,
  };
}
