/**
 * Session storage utilities for managing temporary data across navigation
 */

const GOAL_PROMPT_KEY = 'skillpath_goal_prompt';

export interface GoalPromptData {
  goalId: string;
  prompt: string;
  timestamp: number;
}

/**
 * Save a goal prompt to session storage
 */
export function saveGoalPrompt(goalId: string, prompt: string): void {
  try {
    const data: GoalPromptData = {
      goalId,
      prompt: prompt.trim(),
      timestamp: Date.now(),
    };
    sessionStorage.setItem(GOAL_PROMPT_KEY, JSON.stringify(data));
  } catch (error) {
    console.warn('Failed to save goal prompt to session storage:', error);
  }
}

/**
 * Get a goal prompt from session storage
 */
export function getGoalPrompt(goalId: string): string | null {
  try {
    const stored = sessionStorage.getItem(GOAL_PROMPT_KEY);
    if (!stored) {
      return null;
    }

    const data: GoalPromptData = JSON.parse(stored);

    // Check if the stored data is for the requested goal ID
    if (data.goalId !== goalId) {
      return null;
    }

    // Check if the data is not too old (24 hours)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (Date.now() - data.timestamp > maxAge) {
      clearGoalPrompt();
      return null;
    }

    return data.prompt;
  } catch (error) {
    console.warn('Failed to get goal prompt from session storage:', error);
    return null;
  }
}

/**
 * Clear the goal prompt from session storage
 */
export function clearGoalPrompt(): void {
  try {
    sessionStorage.removeItem(GOAL_PROMPT_KEY);
  } catch (error) {
    console.warn('Failed to clear goal prompt from session storage:', error);
  }
}

/**
 * Check if a goal prompt exists for the given goal ID
 */
export function hasGoalPrompt(goalId: string): boolean {
  return getGoalPrompt(goalId) !== null;
}
