import { beforeEach, describe, expect, it, vi } from 'vitest';
import { clearGoalPrompt, getGoalPrompt, hasGoalPrompt, saveGoalPrompt } from '../sessionStorage';

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

describe('sessionStorage utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('saveGoalPrompt', () => {
    it('should save goal prompt to session storage', () => {
      const goalId = 'test-goal-123';
      const prompt = 'I want to learn React';

      saveGoalPrompt(goalId, prompt);

      expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
        'skillpath_goal_prompt',
        expect.stringContaining(goalId)
      );
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
        'skillpath_goal_prompt',
        expect.stringContaining(prompt)
      );
    });

    it('should trim whitespace from prompt', () => {
      const goalId = 'test-goal-123';
      const prompt = '  I want to learn React  ';

      saveGoalPrompt(goalId, prompt);

      const savedData = JSON.parse(sessionStorageMock.setItem.mock.calls[0][1]);
      expect(savedData.prompt).toBe('I want to learn React');
    });

    it('should handle errors gracefully', () => {
      sessionStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      expect(() => saveGoalPrompt('test', 'prompt')).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save goal prompt to session storage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('getGoalPrompt', () => {
    it('should return null when no data exists', () => {
      sessionStorageMock.getItem.mockReturnValue(null);

      const result = getGoalPrompt('test-goal-123');

      expect(result).toBeNull();
    });

    it('should return null when goal ID does not match', () => {
      const storedData = {
        goalId: 'different-goal',
        prompt: 'Some prompt',
        timestamp: Date.now(),
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(storedData));

      const result = getGoalPrompt('test-goal-123');

      expect(result).toBeNull();
    });

    it('should return prompt when goal ID matches and data is fresh', () => {
      const goalId = 'test-goal-123';
      const prompt = 'I want to learn React';
      const storedData = {
        goalId,
        prompt,
        timestamp: Date.now(),
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(storedData));

      const result = getGoalPrompt(goalId);

      expect(result).toBe(prompt);
    });

    it('should return null and clear storage when data is too old', () => {
      const goalId = 'test-goal-123';
      const prompt = 'I want to learn React';
      const oldTimestamp = Date.now() - 25 * 60 * 60 * 1000; // 25 hours ago
      const storedData = {
        goalId,
        prompt,
        timestamp: oldTimestamp,
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(storedData));

      const result = getGoalPrompt(goalId);

      expect(result).toBeNull();
      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith('skillpath_goal_prompt');
    });

    it('should handle errors gracefully', () => {
      sessionStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const result = getGoalPrompt('test-goal-123');

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to get goal prompt from session storage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('clearGoalPrompt', () => {
    it('should remove goal prompt from session storage', () => {
      clearGoalPrompt();

      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith('skillpath_goal_prompt');
    });

    it('should handle errors gracefully', () => {
      sessionStorageMock.removeItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      expect(() => clearGoalPrompt()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to clear goal prompt from session storage:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('hasGoalPrompt', () => {
    it('should return true when goal prompt exists', () => {
      const goalId = 'test-goal-123';
      const storedData = {
        goalId,
        prompt: 'I want to learn React',
        timestamp: Date.now(),
      };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(storedData));

      const result = hasGoalPrompt(goalId);

      expect(result).toBe(true);
    });

    it('should return false when goal prompt does not exist', () => {
      sessionStorageMock.getItem.mockReturnValue(null);

      const result = hasGoalPrompt('test-goal-123');

      expect(result).toBe(false);
    });
  });
});
