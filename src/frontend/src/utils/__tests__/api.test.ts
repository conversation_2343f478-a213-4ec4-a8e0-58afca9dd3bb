import { describe, expect, it, vi } from 'vitest';
import { createLearningAgentPayload, streamLearningAgent } from '../api';

describe('API utilities', () => {
  describe('createLearningAgentPayload', () => {
    it('creates correct payload with default user ID', () => {
      const message = 'I want to learn React';
      const threadId = 'test-thread-123';

      const payload = createLearningAgentPayload(message, threadId);

      expect(payload).toEqual({
        message: 'I want to learn React',
        thread_id: 'test-thread-123',
        user_id: 'dainq11',
        stream_tokens: false,
      });
    });

    it('creates correct payload with custom user ID', () => {
      const message = 'I want to learn Vue.js';
      const threadId = 'test-thread-456';
      const userId = 'custom-user-789';

      const payload = createLearningAgentPayload(message, threadId, userId);

      expect(payload).toEqual({
        message: 'I want to learn Vue.js',
        thread_id: 'test-thread-456',
        user_id: 'custom-user-789',
        stream_tokens: false,
      });
    });

    it('trims whitespace from message', () => {
      const message = '  I want to learn Angular  ';
      const threadId = 'test-thread-789';

      const payload = createLearningAgentPayload(message, threadId);

      expect(payload.message).toBe('I want to learn Angular');
    });
  });

  describe('streamLearningAgent SSE parsing', () => {
    it('should parse SSE format and accumulate tokens', async () => {
      // Mock fetch response with SSE data in the expected format
      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi
              .fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(
                  'data: {"type": "message", "content": {"type": "ai", "content": "Hello"}}\n'
                ),
              })
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(
                  'data: {"type": "message", "content": {"type": "ai", "content": " world"}}\n'
                ),
              })
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(
                  'data: {"type": "message", "content": {"type": "ai", "content": "!"}}\n'
                ),
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined,
              }),
            releaseLock: vi.fn(),
          }),
        },
      };

      global.fetch = vi.fn().mockResolvedValue(mockResponse);

      const payload = createLearningAgentPayload('test message', 'test-thread');
      const result = await streamLearningAgent(payload);

      expect(result.success).toBe(true);
      expect(result.content).toBe('Hello world!');
    });
  });
});
