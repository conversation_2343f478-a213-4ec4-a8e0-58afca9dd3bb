# RoadmapChart Component

A React component for rendering interactive learning roadmaps using React Flow. This component visualizes learning paths with nodes representing topics and edges showing prerequisites and progression.

## Features

- **Interactive Visualization**: Built with React Flow for pan, zoom, and interactive exploration
- **Learning Levels**: Visual indicators for beginner, intermediate, and advanced topics
- **Progress Tracking**: Progress bars showing completion status
- **Prerequisites**: Clear visual connections between dependent topics
- **Responsive Design**: Adapts to different screen sizes
- **Mantine Integration**: Consistent styling with the application's design system

## Usage

### Direct Component Usage

```tsx
import { RoadmapChart } from '@/components/RoadmapChart';

const roadmapData = {
  nodes: [
    {
      id: 'react-basics',
      type: 'learningNode',
      position: { x: 100, y: 100 },
      data: {
        id: 'react-basics',
        label: 'React Basics',
        title: 'Learn React Fundamentals',
        description: 'Understanding components, props, and state',
        level: 'beginner',
        progress: 0,
        isCompleted: false,
        estimatedHours: 20,
        prerequisites: [],
      },
    },
  ],
  edges: [
    {
      id: 'e1-2',
      source: 'react-basics',
      target: 'react-hooks',
      type: 'learningEdge',
      data: { isUnlocked: true },
    },
  ],
};

<RoadmapChart data={roadmapData} title="React Learning Path" />
```

### Markdown Integration

The component is automatically integrated with MarkdownRenderer. Use roadmap_chart code blocks:

````markdown
```roadmap_chart
{
  "nodes": [
    {
      "id": "node1",
      "type": "learningNode",
      "position": {"x": 100, "y": 100},
      "data": {
        "id": "node1",
        "label": "React Basics",
        "title": "Learn React Fundamentals",
        "description": "Understanding components, props, and state",
        "level": "beginner",
        "progress": 0,
        "isCompleted": false,
        "estimatedHours": 20,
        "prerequisites": []
      }
    }
  ],
  "edges": []
}
```
````

## Data Structure

### RoadmapData
- `nodes`: Array of learning nodes
- `edges`: Array of connections between nodes

### Node Data
- `id`: Unique identifier
- `label`: Short display name
- `title`: Full title
- `description`: Detailed description
- `level`: 'beginner' | 'intermediate' | 'advanced'
- `progress`: Number (0-100)
- `isCompleted`: Boolean
- `estimatedHours`: Number
- `prerequisites`: Array of node IDs

### Edge Data
- `id`: Unique identifier
- `source`: Source node ID
- `target`: Target node ID
- `data.isUnlocked`: Boolean indicating if path is available
