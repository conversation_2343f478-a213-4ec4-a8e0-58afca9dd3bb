import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { RoadmapChart } from './RoadmapChart';

describe('RoadmapChart', () => {
  const mockRoadmapData = {
    nodes: [
      {
        id: 'node1',
        type: 'learningNode',
        position: { x: 100, y: 100 },
        data: {
          id: 'node1',
          label: 'React Basics',
          title: 'Learn React Fundamentals',
          description: 'Understanding components, props, and state',
          level: 'beginner' as const,
          progress: 0,
          isCompleted: false,
          estimatedHours: 20,
          prerequisites: [],
        },
      },
      {
        id: 'node2',
        type: 'learningNode',
        position: { x: 300, y: 100 },
        data: {
          id: 'node2',
          label: 'React Hooks',
          title: 'Master React Hooks',
          description: 'useState, useEffect, and custom hooks',
          level: 'intermediate' as const,
          progress: 50,
          isCompleted: false,
          estimatedHours: 15,
          prerequisites: ['node1'],
        },
      },
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'node1',
        target: 'node2',
        type: 'learningEdge',
        data: { isUnlocked: true },
      },
    ],
  };

  it('renders without crashing', () => {
    render(<RoadmapChart data={mockRoadmapData} />);
  });

  it('renders with custom title', () => {
    const { getByText } = render(<RoadmapChart data={mockRoadmapData} title="Custom Roadmap" />);
    expect(getByText('Custom Roadmap')).toBeInTheDocument();
  });

  it('renders default title when none provided', () => {
    const { getByText } = render(<RoadmapChart data={mockRoadmapData} />);
    expect(getByText('Learning Roadmap')).toBeInTheDocument();
  });
});
