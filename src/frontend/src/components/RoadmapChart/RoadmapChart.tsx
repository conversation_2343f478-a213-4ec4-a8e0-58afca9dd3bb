import { memo, useCallback, useMemo } from 'react';
import {
  Background,
  ConnectionMode,
  Controls,
  Edge,
  EdgeProps,
  EdgeText,
  getSmoothStepPath,
  Handle,
  MarkerType,
  MiniMap,
  Node,
  Panel,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';

import '@xyflow/react/dist/style.css';

import { Box, Text, Title } from '@mantine/core';

export interface RoadmapNode extends Node {
  data: {
    id: string;
    label: string;
    title: string;
    description: string;
    level: 'beginner' | 'intermediate' | 'advanced';
    progress: number;
    isCompleted: boolean;
    estimatedHours: number;
    prerequisites: string[];
  };
}

export interface RoadmapEdgeData {
  label?: string;
  isUnlocked: boolean;
  [key: string]: any; // Index signature for React Flow compatibility
}

export interface RoadmapEdge extends Edge {
  data?: RoadmapEdgeData;
}

export interface RoadmapData {
  nodes: RoadmapNode[];
  edges: RoadmapEdge[];
}

export interface RoadmapChartProps {
  data: RoadmapData;
  title?: string;
}

/**
 * Custom node component for learning roadmap visualization
 */
const LearningNode = ({ data }: { data: RoadmapNode['data'] }) => {
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'var(--mantine-color-green-6)';
      case 'intermediate':
        return 'var(--mantine-color-yellow-6)';
      case 'advanced':
        return 'var(--mantine-color-red-6)';
      default:
        return 'var(--mantine-color-gray-6)';
    }
  };

  return (
    <>
      <Handle type="target" position={Position.Top} />
      <Box
        style={{
          padding: '12px',
          borderRadius: '8px',
          border: `2px solid ${getLevelColor(data.level)}`,
          backgroundColor: data.isCompleted ? 'var(--mantine-color-green-0)' : 'white',
          minWidth: '200px',
          maxWidth: '250px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Text size="sm" fw={600} mb="xs" style={{ color: getLevelColor(data.level) }}>
          {data.label}
        </Text>
        <Text size="xs" c="dimmed" mb="xs">
          {data.level.charAt(0).toUpperCase() + data.level.slice(1)} • {data.estimatedHours}h
        </Text>
        <Text size="xs" style={{ lineHeight: 1.4 }}>
          {data.description}
        </Text>
        {data.progress > 0 && (
          <Box
            style={{
              marginTop: '8px',
              height: '4px',
              backgroundColor: 'var(--mantine-color-gray-2)',
              borderRadius: '2px',
              overflow: 'hidden',
            }}
          >
            <Box
              style={{
                height: '100%',
                width: `${data.progress}%`,
                backgroundColor: getLevelColor(data.level),
                transition: 'width 0.3s ease',
              }}
            />
          </Box>
        )}
      </Box>
      <Handle type="source" position={Position.Bottom} />
    </>
  );
};

/**
 * Custom edge component for learning roadmap visualization
 */
const LearningEdge = memo((props: EdgeProps) => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    data,
    markerEnd,
  } = props;
  const edgeData = data as RoadmapEdgeData;
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
  });

  const isUnlocked = edgeData?.isUnlocked ?? true;

  return (
    <>
      <path
        id={id as string}
        style={{
          stroke: isUnlocked ? 'var(--mantine-color-blue-6)' : 'var(--mantine-color-gray-4)',
          strokeWidth: isUnlocked ? 3 : 2,
          strokeDasharray: isUnlocked ? 'none' : '8,4',
          opacity: isUnlocked ? 0.9 : 0.6,
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {edgeData?.label && (
        <EdgeText
          x={labelX}
          y={labelY}
          label={edgeData.label}
          labelStyle={{
            fontSize: '13px',
            fontWeight: 600,
            fill: isUnlocked ? 'var(--mantine-color-blue-7)' : 'var(--mantine-color-gray-6)',
          }}
          labelBgStyle={{
            fill: 'var(--mantine-color-white)',
            fillOpacity: 0.95,
            stroke: isUnlocked ? 'var(--mantine-color-blue-3)' : 'var(--mantine-color-gray-3)',
            strokeWidth: 1,
          }}
          labelBgPadding={[6, 12]}
          labelBgBorderRadius={6}
        />
      )}
    </>
  );
});

LearningEdge.displayName = 'LearningEdge';

/**
 * RoadmapChart component for rendering learning roadmaps using React Flow
 */
export function RoadmapChart({ data, title = 'Learning Roadmap' }: RoadmapChartProps) {
  const nodeTypes = useMemo(() => ({ learningNode: LearningNode }), []);
  const edgeTypes = useMemo(() => ({ learningEdge: LearningEdge as any }), []);

  // Initialize nodes and edges with React Flow hooks
  const [nodes, , onNodesChange] = useNodesState(
    data.nodes.map((node) => ({
      ...node,
      type: 'learningNode',
    }))
  );
  const [edges, , onEdgesChange] = useEdgesState(
    data.edges.map((edge) => ({
      ...edge,
      type: 'learningEdge',
      markerEnd: { type: MarkerType.ArrowClosed },
    }))
  );

  const onConnect = useCallback(() => {
    // Disable connections in read-only mode
  }, []);

  return (
    <Box mb="md">
      <Box
        style={{
          height: '500px',
          border: '1px solid var(--mantine-color-gray-3)',
          borderRadius: 'var(--mantine-radius-md)',
          overflow: 'hidden',
        }}
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          connectionMode={ConnectionMode.Loose}
          fitView
          fitViewOptions={{
            padding: 0.1,
            includeHiddenNodes: false,
            minZoom: 0.5,
            maxZoom: 1.5,
          }}
          attributionPosition="bottom-left"
        >
          <Background color="#f1f3f4" />
          <Controls />
          <MiniMap
            nodeColor={(node) => {
              const data = node.data as any;
              if (data?.isCompleted) {
                return '#51cf66';
              }
              if (data?.progress > 0) {
                return '#ffd43b';
              }
              return '#868e96';
            }}
          />

          <Panel position="top-left">
            <Title order={3} size="h4" mb="xs">
              {title}
            </Title>
            <Text size="sm" c="dimmed">
              Interactive learning path visualization
            </Text>
          </Panel>
        </ReactFlow>
      </Box>
    </Box>
  );
}
