import { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Group, Text, Tooltip, UnstyledButton } from '@mantine/core';
import classes from './NavigationItem.module.css';

interface NavigationItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href: string;
  active?: boolean;
  collapsed?: boolean;
}

export function NavigationItem({
  icon: Icon,
  label,
  href,
  active,
  collapsed = false,
}: NavigationItemProps) {
  const buttonClassName = useMemo(
    () => `${classes.item} ${active ? classes.active : ''} ${collapsed ? classes.collapsed : ''}`,
    [active, collapsed]
  );

  const groupJustify = useMemo(() => (collapsed ? 'center' : 'flex-start'), [collapsed]);

  const button = useMemo(
    () => (
      <UnstyledButton component={Link} to={href} className={buttonClassName}>
        <Group gap="sm" justify={groupJustify}>
          <Icon size={20} className={classes.icon} />
          {!collapsed && (
            <Text size="sm" fw={500} className={classes.label}>
              {label}
            </Text>
          )}
        </Group>
      </UnstyledButton>
    ),
    [Icon, href, buttonClassName, groupJustify, collapsed, label]
  );

  if (collapsed) {
    return (
      <Tooltip label={label} position="right" withArrow>
        {button}
      </Tooltip>
    );
  }

  return button;
}
