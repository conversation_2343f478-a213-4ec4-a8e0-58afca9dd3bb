import { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Group, Text, Tooltip, UnstyledButton } from '@mantine/core';
import classes from './SettingsItem.module.css';

interface SettingsItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href?: string;
  onClick?: () => void;
  collapsed?: boolean;
}

export function SettingsItem({
  icon: Icon,
  label,
  href,
  onClick,
  collapsed = false,
}: SettingsItemProps) {
  const buttonClassName = useMemo(
    () => `${classes.item} ${collapsed ? classes.collapsed : ''}`,
    [collapsed]
  );

  const groupJustify = useMemo(() => (collapsed ? 'center' : 'flex-start'), [collapsed]);

  const buttonProps = useMemo(() => (href ? { to: href } : { onClick }), [href, onClick]);

  const button = useMemo(
    () => (
      <UnstyledButton
        component={href ? Link : ('button' as any)}
        {...buttonProps}
        className={buttonClassName}
      >
        <Group gap="sm" justify={groupJustify}>
          <Icon size={20} className={classes.icon} />
          {!collapsed && (
            <Text size="sm" className={classes.label}>
              {label}
            </Text>
          )}
        </Group>
      </UnstyledButton>
    ),
    [Icon, href, buttonProps, buttonClassName, groupJustify, collapsed, label]
  );

  if (collapsed) {
    return (
      <Tooltip label={label} position="right" withArrow>
        {button}
      </Tooltip>
    );
  }

  return button;
}
