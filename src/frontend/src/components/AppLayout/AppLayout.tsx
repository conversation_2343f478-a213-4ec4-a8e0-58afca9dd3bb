import { ReactNode, useMemo } from 'react';
import { AppShell } from '@mantine/core';
import { Sidebar } from './Sidebar/Sidebar';
import { SidebarProvider, useSidebar } from './Sidebar/SidebarContext';
import classes from './AppLayout.module.css';

interface AppLayoutProps {
  children: ReactNode;
}

function AppLayoutContent({ children }: AppLayoutProps) {
  const { isCollapsed } = useSidebar();

  const navbarConfig = useMemo(
    () => ({
      width: isCollapsed ? 60 : 280,
      breakpoint: 'sm' as const,
      collapsed: { mobile: false, desktop: false },
    }),
    [isCollapsed]
  );

  const navbarClassName = useMemo(
    () => `${classes.navbar} ${isCollapsed ? classes.collapsed : ''}`,
    [isCollapsed]
  );

  return (
    <AppShell navbar={navbarConfig} padding="md" className={classes.shell}>
      <AppShell.Navbar className={navbarClassName}>
        <Sidebar />
      </AppShell.Navbar>

      <AppShell.Main className={classes.main}>{children}</AppShell.Main>
    </AppShell>
  );
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <SidebarProvider>
      <AppLayoutContent>{children}</AppLayoutContent>
    </SidebarProvider>
  );
}
