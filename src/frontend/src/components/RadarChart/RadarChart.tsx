import { useEffect, useRef } from 'react';
import { Chart as ChartJS } from 'chart.js/auto';
import { Box, Text } from '@mantine/core';

export interface RadarChartDataPoint {
  skill: string;
  level: number;
  fullMark: number;
}

export interface RadarChartProps {
  data: RadarChartDataPoint[];
  title?: string;
  width?: number;
  height?: number;
}

/**
 * Reusable RadarChart component using Chart.js.
 * Renders skill assessment data in a radar chart format.
 */
export function RadarChart({ data, title, width = 400, height = 400 }: RadarChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chartRef = useRef<ChartJS | null>(null);

  // Calculate chart size using the same logic as the original SVG implementation
  const chartSize = Math.min(width, height) - 100;

  useEffect(() => {
    if (!canvasRef.current || data.length === 0) {
      return;
    }

    // Destroy existing chart if it exists
    if (chartRef.current) {
      chartRef.current.destroy();
    }

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) {
      return;
    }

    // Prepare data for Chart.js
    const labels = data.map((item) => item.skill);
    const currentLevels = data.map((item) => item.level);
    const targetLevels = data.map((item) => item.fullMark);
    const maxValue = Math.max(...data.map((item) => item.fullMark));

    // Create Chart.js radar chart
    chartRef.current = new ChartJS(ctx, {
      type: 'radar',
      data: {
        labels,
        datasets: [
          {
            label: 'Current Level',
            data: currentLevels,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            pointBackgroundColor: 'rgba(54, 162, 235, 1)',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
          },
          {
            label: 'Target Level',
            data: targetLevels,
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 2,
            borderDash: [5, 5],
            pointBackgroundColor: 'rgba(255, 99, 132, 1)',
            pointBorderColor: '#fff',
            pointBorderWidth: 1,
            pointRadius: 3,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            beginAtZero: true,
            max: maxValue,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)',
            },
            angleLines: {
              color: 'rgba(0, 0, 0, 0.1)',
            },
            pointLabels: {
              font: {
                size: 12,
                weight: 500,
              },
              color: '#495057',
            },
            ticks: {
              display: false,
            },
          },
        },
        plugins: {
          legend: {
            display: true, // We'll use custom legend
          },
          tooltip: {
            callbacks: {
              label(context) {
                return `${context.dataset.label}: ${context.parsed.r}`;
              },
            },
          },
        },
      },
    });

    // Cleanup function
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
        chartRef.current = null;
      }
    };
  }, [data, chartSize]);

  return (
    <Box
      style={{
        width: '100%',
        margin: '0 auto',
        padding: 'var(--mantine-spacing-md)',
        border: '1px solid var(--mantine-color-gray-3)',
        borderRadius: 'var(--mantine-radius-md)',
        backgroundColor: 'var(--mantine-color-gray-0)',
      }}
    >
      {title && (
        <Text size="lg" fw={600} ta="center" mb="md">
          {title}
        </Text>
      )}

      <Box style={{ display: 'flex', justifyContent: 'center' }}>
        <canvas
          ref={canvasRef}
          width={chartSize}
          height={chartSize}
          style={{ maxWidth: '100%', height: 'auto' }}
        />
      </Box>
    </Box>
  );
}
