import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Box, Table, Text, Title } from '@mantine/core';
import { RadarChart, RadarChartDataPoint } from '../RadarChart';
import { RoadmapChart, RoadmapData } from '../RoadmapChart';

export interface MarkdownRendererProps {
  children: string;
}

/**
 * Parse radar chart data from JSON string
 */
function parseRadarChartData(jsonString: string): RadarChartDataPoint[] | null {
  try {
    const data = JSON.parse(jsonString);

    // Validate that it's an array of objects with required properties
    if (
      Array.isArray(data) &&
      data.every(
        (item) =>
          typeof item === 'object' &&
          typeof item.skill === 'string' &&
          typeof item.level === 'number' &&
          typeof item.fullMark === 'number'
      )
    ) {
      return data as RadarChartDataPoint[];
    }

    return null;
  } catch {
    // Failed to parse JSON - return null to indicate invalid data
    return null;
  }
}

/**
 * Parse roadmap chart data from JSON string
 */
function parseRoadmapChartData(jsonString: string): RoadmapData | null {
  try {
    const data = JSON.parse(jsonString);

    // Validate that it has nodes and edges arrays
    if (
      typeof data === 'object' &&
      data !== null &&
      Array.isArray(data.nodes) &&
      Array.isArray(data.edges)
    ) {
      // Basic validation for node structure
      const validNodes = data.nodes.every(
        (node: any) =>
          typeof node === 'object' &&
          typeof node.id === 'string' &&
          typeof node.data === 'object' &&
          typeof node.data.label === 'string'
      );

      // Basic validation for edge structure
      const validEdges = data.edges.every(
        (edge: any) =>
          typeof edge === 'object' &&
          typeof edge.id === 'string' &&
          typeof edge.source === 'string' &&
          typeof edge.target === 'string'
      );

      if (validNodes && validEdges) {
        return data as RoadmapData;
      }
    }

    return null;
  } catch {
    // Failed to parse JSON - return null to indicate invalid data
    return null;
  }
}

/**
 * Reusable ReactMarkdown component with consistent styling and configuration.
 * Provides standardized markdown rendering across the application.
 * Supports custom radar_chart code blocks for rendering Chart.js radar charts.
 */
export function MarkdownRenderer({ children }: MarkdownRendererProps) {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        // Custom styling for markdown elements
        h1: ({ children }) => (
          <Title order={4} mb="xs">
            {children}
          </Title>
        ),
        h2: ({ children }) => (
          <Title order={5} mb="xs">
            {children}
          </Title>
        ),
        h3: ({ children }) => (
          <Title order={6} mb="xs">
            {children}
          </Title>
        ),
        p: ({ children }) => (
          <Text size="sm" mb="xs">
            {children}
          </Text>
        ),
        ul: ({ children }) => (
          <Box component="ul" style={{ paddingLeft: '1rem', marginBottom: '0.5rem' }}>
            {children}
          </Box>
        ),
        ol: ({ children }) => (
          <Box component="ol" style={{ paddingLeft: '1rem', marginBottom: '0.5rem' }}>
            {children}
          </Box>
        ),
        li: ({ children }) => (
          <Text component="li" size="sm" mb="xs">
            {children}
          </Text>
        ),
        table: ({ children }) => (
          <Box mb="md" style={{ overflowX: 'auto' }}>
            <Table striped highlightOnHover withTableBorder withColumnBorders>
              {children}
            </Table>
          </Box>
        ),
        thead: ({ children }) => <Table.Thead>{children}</Table.Thead>,
        tbody: ({ children }) => <Table.Tbody>{children}</Table.Tbody>,
        tr: ({ children }) => <Table.Tr>{children}</Table.Tr>,
        th: ({ children }) => <Table.Th style={{ fontWeight: 600 }}>{children}</Table.Th>,
        td: ({ children }) => <Table.Td>{children}</Table.Td>,
        code: ({ children, className }) => {
          console.log(className);
          // Check if this is a radar_chart code block
          if (className === 'language-radar_chart') {
            const chartData = parseRadarChartData(String(children));

            if (chartData) {
              return <RadarChart data={chartData} title="Skills Assessment" />;
            }

            // Fallback to regular code display if parsing fails
            return (
              <Box
                component="pre"
                style={{
                  backgroundColor: 'var(--mantine-color-red-0)',
                  padding: 'var(--mantine-spacing-sm)',
                  borderRadius: 'var(--mantine-radius-sm)',
                  border: '1px solid var(--mantine-color-red-3)',
                  marginBottom: '0.5rem',
                }}
              >
                <Text size="sm" c="red" mb="xs">
                  Error: Invalid radar chart data format
                </Text>
                <Text component="code" size="sm" style={{ fontFamily: 'monospace' }}>
                  {children}
                </Text>
              </Box>
            );
          }

          // Check if this is a roadmap_chart code block
          if (className === 'language-roadmap_chart') {
            const roadmapData = parseRoadmapChartData(String(children));

            if (roadmapData) {
              return <RoadmapChart data={roadmapData} title="Learning Roadmap" />;
            }

            // Fallback to regular code display if parsing fails
            return (
              <Box
                component="pre"
                style={{
                  backgroundColor: 'var(--mantine-color-red-0)',
                  padding: 'var(--mantine-spacing-sm)',
                  borderRadius: 'var(--mantine-radius-sm)',
                  border: '1px solid var(--mantine-color-red-3)',
                  marginBottom: '0.5rem',
                }}
              >
                <Text size="sm" c="red" mb="xs">
                  Error: Invalid roadmap chart data format
                </Text>
                <Text component="code" size="sm" style={{ fontFamily: 'monospace' }}>
                  {children}
                </Text>
              </Box>
            );
          }

          // Regular inline code
          return (
            <Text
              component="code"
              size="sm"
              style={{
                backgroundColor: 'var(--mantine-color-gray-1)',
                padding: '0.125rem 0.25rem',
                borderRadius: 'var(--mantine-radius-sm)',
                fontFamily: 'monospace',
              }}
            >
              {children}
            </Text>
          );
        },
        pre: ({ children }) => {
          // Check if this pre block contains a radar_chart code element
          const radarCodeElement = React.Children.toArray(children).find(
            (child: any) =>
              React.isValidElement(child) &&
              (child as any).props?.className === 'language-radar_chart'
          );

          if (radarCodeElement && React.isValidElement(radarCodeElement)) {
            const chartData = parseRadarChartData(String((radarCodeElement as any).props.children));

            if (chartData) {
              return (
                <Box mb="md">
                  <RadarChart data={chartData} title="Skills Assessment" />
                </Box>
              );
            }
          }

          // Check if this pre block contains a roadmap_chart code element
          const roadmapCodeElement = React.Children.toArray(children).find(
            (child: any) =>
              React.isValidElement(child) &&
              (child as any).props?.className === 'language-roadmap_chart'
          );

          if (roadmapCodeElement && React.isValidElement(roadmapCodeElement)) {
            const roadmapData = parseRoadmapChartData(
              String((roadmapCodeElement as any).props.children)
            );

            if (roadmapData) {
              return (
                <Box mb="md">
                  <RoadmapChart data={roadmapData} title="Learning Roadmap" />
                </Box>
              );
            }
          }

          // Regular pre block
          return (
            <Box
              component="pre"
              style={{
                backgroundColor: 'var(--mantine-color-gray-1)',
                padding: 'var(--mantine-spacing-sm)',
                borderRadius: 'var(--mantine-radius-sm)',
                overflow: 'auto',
                marginBottom: '0.5rem',
              }}
            >
              {children}
            </Box>
          );
        },
      }}
    >
      {children}
    </ReactMarkdown>
  );
}
