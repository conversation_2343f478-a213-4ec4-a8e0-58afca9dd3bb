import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AppLayout } from './components';
import AuthGuard from './hoc/AuthGuard';
import GuestOnly from './hoc/GuestOnly';
import { CoursesPage } from './pages/courses';
import { CVUploadPage } from './pages/cvUpload';
import { DashboardPage } from './pages/dashboard';
import { GoalDetailPage } from './pages/goalDetail';
import { GoalsPage } from './pages/goals';
import { HomePage } from './pages/home';
import { LearningPathsPage } from './pages/learningPaths';
import { ProgressPage } from './pages/progress';
import { SignInPage } from './pages/signIn';
import { SignUpPage } from './pages/signUp';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthGuard>
        <AppLayout>
          <HomePage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/sign-in',
    element: (
      <GuestOnly>
        <SignInPage />
      </GuestOnly>
    ),
  },
  {
    path: '/sign-up',
    element: (
      <GuestOnly>
        <SignUpPage />
      </GuestOnly>
    ),
  },
  {
    path: '/cv-upload',
    element: (
      <AuthGuard>
        <CVUploadPage />
      </AuthGuard>
    ),
  },
  {
    path: '/dashboard',
    element: (
      <AuthGuard>
        <AppLayout>
          <DashboardPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/goals',
    element: (
      <AuthGuard>
        <AppLayout>
          <GoalsPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/goals/:goalId',
    element: (
      <AuthGuard>
        <AppLayout>
          <GoalDetailPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/learning-paths',
    element: (
      <AuthGuard>
        <AppLayout>
          <LearningPathsPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/courses',
    element: (
      <AuthGuard>
        <AppLayout>
          <CoursesPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
  {
    path: '/progress',
    element: (
      <AuthGuard>
        <AppLayout>
          <ProgressPage />
        </AppLayout>
      </AuthGuard>
    ),
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
