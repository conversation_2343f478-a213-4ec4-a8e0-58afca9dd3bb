// Shared authentication types for frontend
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: 'user' | 'admin';
  createdAt: string;
  lastLoginAt: string;
  hasUploadedCV?: boolean;
  skills?: string[];
}

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  signIn: (credentials: AuthCredentials) => Promise<User>;
  register: (data: RegisterData) => Promise<User>;
  signOut: () => Promise<void>;
  refreshAuth: () => void;
  updateUserSkills: (userId: string) => Promise<User>;
}
