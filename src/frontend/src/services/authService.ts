// Service selector for authentication
import { AuthCredentials, RegisterData, User } from '../types/auth';
import { checkApiHealth } from './api';
import { mockAuthService } from './mockAuth';
import { realAuthService } from './realAuth';

// Common interface for both auth services
export interface AuthService {
  getStoredAuth: () => { isAuthenticated: boolean; user: User | null };
  signIn: (credentials: AuthCredentials) => Promise<User>;
  register: (data: RegisterData) => Promise<User>;
  signOut: () => Promise<void>;
  updateUserSkills: (userId: string) => Promise<User>;
  isDevelopmentMode: () => boolean;
}

// Configuration
const USE_MOCK_AUTH = import.meta.env.VITE_USE_MOCK_AUTH === 'true';
const IS_DEV = import.meta.env.DEV;

// Service selection logic
let selectedService: AuthService | null = null;
let serviceInitialized = false;

const initializeAuthService = async (): Promise<AuthService> => {
  if (serviceInitialized && selectedService) {
    return selectedService;
  }

  // Force mock auth if environment variable is set
  if (USE_MOCK_AUTH) {
    console.log('🔧 Using mock authentication service (forced by env var)');
    selectedService = mockAuthService;
    serviceInitialized = true;
    return selectedService;
  }

  // In development, try real API first, fallback to mock
  if (IS_DEV) {
    try {
      const isApiHealthy = await checkApiHealth();

      if (isApiHealthy) {
        console.log('✅ Backend API is available - using real authentication service');
        selectedService = realAuthService;
      } else {
        console.log('⚠️ Backend API not available - falling back to mock authentication service');
        selectedService = mockAuthService;
      }
    } catch (error) {
      console.warn(
        '⚠️ Failed to check API health - falling back to mock authentication service:',
        error
      );
      selectedService = mockAuthService;
    }
  } else {
    // In production, always use real service
    console.log('🚀 Production mode - using real authentication service');
    selectedService = realAuthService;
  }

  serviceInitialized = true;
  return selectedService;
};

// Get the selected auth service
export const getAuthService = async (): Promise<AuthService> => {
  return await initializeAuthService();
};

// Reset service selection (useful for testing)
export const resetAuthService = (): void => {
  selectedService = null;
  serviceInitialized = false;
};

// Export individual services for direct access if needed
export { mockAuthService, realAuthService };
