// Real Authentication Service for backend integration
import { AuthCredentials, RegisterData, User } from '../types/auth';
import { authApi, BackendAuthResponse, BackendUser, tokenStorage } from './api';

// Convert backend user to frontend user format
const convertBackendUser = (backendUser: BackendUser): User => {
  return {
    id: backendUser.id.toString(),
    email: backendUser.email,
    firstName: extractFirstName(backendUser.username),
    lastName: extractLastName(backendUser.username),
    avatar: backendUser.avatarUrl || undefined,
    role: backendUser.role as 'user' | 'admin',
    createdAt: backendUser.createdAt,
    lastLoginAt: new Date().toISOString(),
    hasUploadedCV: false, // Default for now
    skills: [], // Default for now - will be loaded separately
  };
};

// Helper to extract first name from username (temporary solution)
const extractFirstName = (username: string): string => {
  const parts = username.split(' ');
  return parts[0] || username;
};

// Helper to extract last name from username (temporary solution)
const extractLastName = (username: string): string => {
  const parts = username.split(' ');
  return parts.length > 1 ? parts.slice(1).join(' ') : '';
};

class RealAuthService {
  // Simulate network delay for consistency with mock service
  private async simulateNetworkDelay(ms: number = 500): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Get stored authentication state
  getStoredAuth(): { isAuthenticated: boolean; user: User | null } {
    try {
      const token = tokenStorage.getToken();
      const userData = tokenStorage.getUser();

      if (token && userData) {
        return {
          isAuthenticated: true,
          user: convertBackendUser(userData),
        };
      }
    } catch (error) {
      console.warn('Failed to parse stored auth data:', error);
    }

    return {
      isAuthenticated: false,
      user: null,
    };
  }

  // Store authentication state
  private storeAuth(response: BackendAuthResponse): User {
    tokenStorage.setToken(response.token);
    tokenStorage.setUser(response.user);
    return convertBackendUser(response.user);
  }

  // Clear authentication state
  private clearAuth(): void {
    tokenStorage.removeToken();
    tokenStorage.removeUser();
  }

  // Sign in with email and password
  async signIn(credentials: AuthCredentials): Promise<User> {
    await this.simulateNetworkDelay();

    try {
      const response = await authApi.login({
        email: credentials.email,
        password: credentials.password,
      });

      return this.storeAuth(response);
    } catch (error) {
      this.clearAuth();
      throw error;
    }
  }

  // Register new user
  async register(data: RegisterData): Promise<User> {
    await this.simulateNetworkDelay();

    try {
      // Create username from first and last name
      const username = `${data.firstName} ${data.lastName}`.trim();

      const response = await authApi.register({
        username,
        email: data.email,
        password: data.password,
      });

      return this.storeAuth(response);
    } catch (error) {
      this.clearAuth();
      throw error;
    }
  }

  // Update user CV upload status and skills (placeholder for now)
  async updateUserSkills(userId: string): Promise<User> {
    await this.simulateNetworkDelay();

    const storedAuth = this.getStoredAuth();
    if (!storedAuth.isAuthenticated || !storedAuth.user || storedAuth.user.id !== userId) {
      throw new Error('User not authenticated');
    }

    // For now, just update the hasUploadedCV status locally
    const updatedUser: User = {
      ...storedAuth.user,
      hasUploadedCV: true,
    };

    // Update stored user data
    const backendUser: BackendUser = {
      id: parseInt(updatedUser.id, 10),
      username: `${updatedUser.firstName} ${updatedUser.lastName}`,
      email: updatedUser.email,
      role: updatedUser.role,
      avatarUrl: updatedUser.avatar || null,
      oauthProvider: null,
      oauthId: null,
      createdAt: updatedUser.createdAt,
      updatedAt: new Date().toISOString(),
    };

    tokenStorage.setUser(backendUser);
    return updatedUser;
  }

  // Sign out
  async signOut(): Promise<void> {
    await this.simulateNetworkDelay(200);

    try {
      await authApi.logout();
    } finally {
      this.clearAuth();
    }
  }

  // Refresh user data from backend
  async refreshUserData(): Promise<User | null> {
    try {
      const token = tokenStorage.getToken();
      if (!token) {
        return null;
      }

      const backendUser = await authApi.getCurrentUser();
      const user = convertBackendUser(backendUser);

      // Update stored user data
      tokenStorage.setUser(backendUser);
      return user;
    } catch (error) {
      // If refresh fails, clear auth
      this.clearAuth();
      return null;
    }
  }

  // Check if this is a development environment
  isDevelopmentMode(): boolean {
    return import.meta.env.DEV || import.meta.env.MODE === 'development';
  }
}

export const realAuthService = new RealAuthService();
