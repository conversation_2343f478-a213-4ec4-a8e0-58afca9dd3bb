// API Service for backend communication
import axios, { AxiosError, AxiosInstance } from 'axios';

// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

// API Interfaces matching backend DTOs
export interface BackendUser {
  id: number;
  username: string;
  email: string;
  role: string;
  avatarUrl?: string | null;
  oauthProvider?: string | null;
  oauthId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface BackendAuthResponse {
  user: BackendUser;
  token: string;
}

export interface BackendRegisterDto {
  username: string;
  email: string;
  password: string;
}

export interface BackendLoginDto {
  email: string;
  password: string;
}

export interface ApiError {
  error: string;
  message?: string;
}

// Create axios instance with base configuration
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('skillpath_auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response) => response,
    (error: AxiosError<ApiError>) => {
      // Handle specific error cases
      if (error.response?.status === 401) {
        // Unauthorized - clear stored auth
        localStorage.removeItem('skillpath_auth_token');
        localStorage.removeItem('skillpath_user');

        // Only redirect if not already on auth pages
        if (
          !window.location.pathname.includes('/sign-') &&
          !window.location.pathname.includes('/auth')
        ) {
          window.location.href = '/sign-in';
        }
      }

      // Transform error to consistent format
      const apiError = new Error(
        error.response?.data?.error ||
          error.response?.data?.message ||
          error.message ||
          'An unexpected error occurred'
      );

      return Promise.reject(apiError);
    }
  );

  return client;
};

// API client instance
const apiClient = createApiClient();

// Authentication API methods
export const authApi = {
  register: async (data: BackendRegisterDto): Promise<BackendAuthResponse> => {
    const response = await apiClient.post<BackendAuthResponse>('/api/auth/register', data);
    return response.data;
  },

  login: async (data: BackendLoginDto): Promise<BackendAuthResponse> => {
    const response = await apiClient.post<BackendAuthResponse>('/api/auth/login', data);
    return response.data;
  },

  getCurrentUser: async (): Promise<BackendUser> => {
    const response = await apiClient.get<BackendUser>('/api/auth/me');
    return response.data;
  },

  logout: async (): Promise<void> => {
    // For this API, logout is client-side only (clear tokens)
    localStorage.removeItem('skillpath_auth_token');
    localStorage.removeItem('skillpath_user');
  },
};

// Health check API
export const healthApi = {
  check: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await apiClient.get<{ status: string; timestamp: string }>('/health');
    return response.data;
  },
};

// Token storage utilities
export const tokenStorage = {
  setToken: (token: string): void => {
    localStorage.setItem('skillpath_auth_token', token);
  },

  getToken: (): string | null => {
    return localStorage.getItem('skillpath_auth_token');
  },

  removeToken: (): void => {
    localStorage.removeItem('skillpath_auth_token');
  },

  setUser: (user: BackendUser): void => {
    localStorage.setItem('skillpath_user', JSON.stringify(user));
  },

  getUser: (): BackendUser | null => {
    const userData = localStorage.getItem('skillpath_user');
    return userData ? JSON.parse(userData) : null;
  },

  removeUser: (): void => {
    localStorage.removeItem('skillpath_user');
  },
};

// Utility to check if API is available
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    await healthApi.check();
    return true;
  } catch {
    return false;
  }
};

export { apiClient };
