// Mock Authentication Service
// This is a temporary authentication service for development purposes
// Replace this with real backend authentication when ready

import { AuthCredentials, RegisterData, User } from '../types/auth';

// Re-export types for compatibility (will be removed later)
export interface MockUser extends User {}
export interface MockAuthCredentials extends AuthCredentials {}
export interface MockRegisterData extends RegisterData {}

// Hardcoded test credentials for development
const MOCK_CREDENTIALS = {
  '<EMAIL>': {
    password: 'password123',
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user' as const,
      createdAt: '2023-01-01T00:00:00Z',
      lastLoginAt: new Date().toISOString(),
      hasUploadedCV: false,
      skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
    },
  },
  '<EMAIL>': {
    password: 'admin123',
    user: {
      id: 'user-2',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      role: 'admin' as const,
      createdAt: '2023-01-01T00:00:00Z',
      lastLoginAt: new Date().toISOString(),
      hasUploadedCV: false,
      skills: ['Project Management', 'Leadership', 'Strategy'],
    },
  },
  '<EMAIL>': {
    password: 'demo123',
    user: {
      id: 'user-3',
      email: '<EMAIL>',
      firstName: 'Demo',
      lastName: 'User',
      role: 'user' as const,
      createdAt: '2023-01-01T00:00:00Z',
      lastLoginAt: new Date().toISOString(),
      hasUploadedCV: false,
      skills: [],
    },
  },
};

const AUTH_STORAGE_KEY = 'skillpath_mock_auth';
const USER_STORAGE_KEY = 'skillpath_mock_user';

class MockAuthService {
  // Simulate network delay for realistic feel
  private async simulateNetworkDelay(ms: number = 1000): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Get stored authentication state
  getStoredAuth(): { isAuthenticated: boolean; user: MockUser | null } {
    try {
      const authData = localStorage.getItem(AUTH_STORAGE_KEY);
      const userData = localStorage.getItem(USER_STORAGE_KEY);

      if (authData === 'true' && userData) {
        return {
          isAuthenticated: true,
          user: JSON.parse(userData),
        };
      }
    } catch (error) {
      // Failed to parse stored auth data, will return default state
    }

    return {
      isAuthenticated: false,
      user: null,
    };
  }

  // Store authentication state
  private storeAuth(user: MockUser): void {
    localStorage.setItem(AUTH_STORAGE_KEY, 'true');
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
  }

  // Clear authentication state
  private clearAuth(): void {
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(USER_STORAGE_KEY);
  }

  // Sign in with email and password
  async signIn(credentials: MockAuthCredentials): Promise<MockUser> {
    await this.simulateNetworkDelay(1500);

    const mockCredential = MOCK_CREDENTIALS[credentials.email as keyof typeof MOCK_CREDENTIALS];

    if (!mockCredential || mockCredential.password !== credentials.password) {
      throw new Error('Invalid email or password');
    }

    // Update last login time
    const user = {
      ...mockCredential.user,
      lastLoginAt: new Date().toISOString(),
    };

    this.storeAuth(user);
    return user;
  }

  // Register new user (for demo purposes, just store in memory)
  async register(data: MockRegisterData): Promise<MockUser> {
    await this.simulateNetworkDelay(2000);

    // Check if email already exists
    if (MOCK_CREDENTIALS[data.email as keyof typeof MOCK_CREDENTIALS]) {
      throw new Error('Email already exists');
    }

    // Create new user
    const user: MockUser = {
      id: `user-${Date.now()}`,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      role: 'user',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      hasUploadedCV: false,
    };

    // In a real app, this would be stored in the backend
    // For demo purposes, we'll just store it locally
    this.storeAuth(user);
    return user;
  }

  // Update user CV upload status and skills
  async updateUserSkills(userId: string): Promise<MockUser> {
    await this.simulateNetworkDelay(1000);

    const storedAuth = this.getStoredAuth();
    if (!storedAuth.isAuthenticated || !storedAuth.user || storedAuth.user.id !== userId) {
      throw new Error('User not authenticated');
    }

    const updatedUser: MockUser = {
      ...storedAuth.user,
      hasUploadedCV: true,
    };

    this.storeAuth(updatedUser);
    return updatedUser;
  }

  // Sign out
  async signOut(): Promise<void> {
    await this.simulateNetworkDelay(500);
    this.clearAuth();
  }

  // Get available test credentials for development
  getTestCredentials(): Array<{ email: string; password: string; name: string }> {
    return [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'John Doe (User)',
      },
      {
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Jane Smith (Admin)',
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        name: 'Demo User',
      },
    ];
  }

  // Check if this is a development environment
  isDevelopmentMode(): boolean {
    return import.meta.env.DEV || import.meta.env.MODE === 'development';
  }
}

export const mockAuthService = new MockAuthService();
