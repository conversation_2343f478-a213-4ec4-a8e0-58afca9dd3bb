import { SignInFormData } from '../types';
import { sanitizeEmail, validateEmail, validatePassword, validateSignInForm } from './validation';

describe('signIn validation utilities', () => {
  test('validateEmail', () => {
    expect(validateEmail('')).toBe('Email is required');
    expect(validateEmail('invalid')).toBe('Please enter a valid email address');
    expect(validateEmail('<EMAIL>')).toBeNull();
  });

  test('validatePassword', () => {
    expect(validatePassword('')).toBe('Password is required');
    expect(validatePassword('123')).toBe('Password must be at least 6 characters long');
    expect(validatePassword('123456')).toBeNull();
  });

  test('validateSignInForm aggregates errors', () => {
    const form: SignInFormData = { email: '', password: '', rememberMe: false };
    const errors = validateSignInForm(form);
    expect(errors).toEqual([
      { field: 'email', message: 'Email is required' },
      { field: 'password', message: 'Password is required' },
    ]);
  });

  test('sanitizeEmail trims and lowercases', () => {
    expect(sanitizeEmail('  <EMAIL> ')).toBe('<EMAIL>');
  });
});
