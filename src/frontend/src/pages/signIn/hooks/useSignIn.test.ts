import { act, renderHook } from '@test-utils';
import { vi } from 'vitest';
import * as AuthContext from '@/contexts/AuthContext';
import { useSignIn } from './useSignIn';

const mockNavigate = vi.fn();
// useLocation returns a Location object. When mocking we only care about the
// `search` property, so cast a minimal object to `Location`.
const mockLocation = { search: '' } as unknown as Location;

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
  };
});

describe('useSignIn hook', () => {
  const mockSignIn = vi.fn();

  beforeEach(() => {
    mockNavigate.mockClear();
    mockSignIn.mockClear();
    mockLocation.search = '';
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      signIn: mockSignIn,
      register: vi.fn(),
      signOut: vi.fn(),
      refreshAuth: vi.fn(),
      updateUserSkills: vi.fn(),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('updates form fields correctly', () => {
    const { result } = renderHook(() => useSignIn());

    act(() => {
      result.current.handleEmailChange(' <EMAIL> ');
      result.current.handlePasswordChange('secret');
      result.current.handleRememberMeChange(true);
    });

    expect(result.current.formData).toEqual({
      email: '<EMAIL>',
      password: 'secret',
      rememberMe: true,
    });
  });

  test('successful submit navigates using redirect', async () => {
    mockSignIn.mockResolvedValue({});
    mockLocation.search = '?redirect=%2Fprofile';

    const { result } = renderHook(() => useSignIn());

    act(() => {
      result.current.handleEmailChange('<EMAIL>');
      result.current.handlePasswordChange('password123');
    });

    await act(async () => {
      await result.current.handleSubmit();
    });

    expect(mockSignIn).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
    });
    expect(mockNavigate).toHaveBeenCalledWith('/profile');
    expect(result.current.errors).toEqual([]);
    expect(result.current.isLoading).toBe(false);
  });

  test('handles authentication errors', async () => {
    mockSignIn.mockRejectedValue(new Error('Invalid email or password'));

    const { result } = renderHook(() => useSignIn());

    act(() => {
      result.current.handleEmailChange('<EMAIL>');
      result.current.handlePasswordChange('wrongpass');
    });

    await act(async () => {
      await result.current.handleSubmit();
    });

    expect(result.current.errors[0].message).toMatch(/invalid email or password/i);
    expect(mockNavigate).not.toHaveBeenCalled();
    expect(result.current.isLoading).toBe(false);
  });
});
