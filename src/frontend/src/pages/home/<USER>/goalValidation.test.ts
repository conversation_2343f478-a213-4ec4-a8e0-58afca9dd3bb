import { hasMinimumLength, isValidGoal, sanitizeGoalText } from './goalValidation';

describe('goalValidation utilities', () => {
  test('isValidGoal returns true for non-empty trimmed text', () => {
    expect(isValidGoal('Learn')).toBe(true);
    expect(isValidGoal('   Learn  ')).toBe(true);
  });

  test('isValidGoal returns false for empty or whitespace only', () => {
    expect(isValidGoal('')).toBe(false);
    expect(isValidGoal('   ')).toBe(false);
  });

  test('hasMinimumLength respects default and custom lengths', () => {
    expect(hasMinimumLength('1234567890')).toBe(true);
    expect(hasMinimumLength('short')).toBe(false);
    expect(hasMinimumLength('abc', 3)).toBe(true);
  });

  test('sanitizeGoalText trims whitespace', () => {
    expect(sanitizeGoalText('  spaced ')).toBe('spaced');
  });
});
