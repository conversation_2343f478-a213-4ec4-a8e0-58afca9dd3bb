import { useState } from 'react';
import { IconCheck } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { saveGoalPrompt } from '../../../utils/sessionStorage';
import { isValidGoal, sanitizeGoalText } from '../utils/goalValidation';

export function useGoalSubmission() {
  const [goalText, setGoalText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const handleGoalChange = (value: string) => {
    setGoalText(value);
  };

  const handleExampleClick = (description: string) => {
    setGoalText(description);
  };

  const handleSubmit = async () => {
    const sanitizedGoal = sanitizeGoalText(goalText);

    if (!isValidGoal(sanitizedGoal)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate a temporary goal ID for navigation
      // In a real implementation, this would come from the API response
      const goalId = `goal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Save the goal prompt to session storage before navigation
      saveGoalPrompt(goalId, sanitizedGoal);

      notifications.show({
        title: 'Learning Goal Submitted!',
        message: 'Your personalized learning path is being created...',
        color: 'green',
        icon: <IconCheck size={18} />,
        autoClose: 4000,
      });

      // Clear the input after submission
      setGoalText('');

      // Navigate to the goal detail page
      navigate(`/goals/${goalId}`);
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to submit your goal. Please try again.',
        color: 'red',
        autoClose: 4000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    goalText,
    isSubmitting,
    isValid: isValidGoal(goalText),
    handleGoalChange,
    handleExampleClick,
    handleSubmit,
  };
}
