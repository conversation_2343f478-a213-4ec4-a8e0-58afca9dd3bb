import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as sessionStorageUtils from '../../../../utils/sessionStorage';
import { useGoalSubmission } from '../useGoalSubmission';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

// Mock session storage utilities
vi.mock('../../../../utils/sessionStorage', () => ({
  saveGoalPrompt: vi.fn(),
}));

describe('useGoalSubmission integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should save goal prompt to session storage and navigate on submit', async () => {
    const { result } = renderHook(() => useGoalSubmission());

    // Set a valid goal text
    act(() => {
      result.current.handleGoalChange('I want to learn React and build web applications');
    });

    expect(result.current.goalText).toBe('I want to learn React and build web applications');
    expect(result.current.isValid).toBe(true);

    // Submit the goal
    await act(async () => {
      await result.current.handleSubmit();
    });

    // Verify session storage was called
    expect(sessionStorageUtils.saveGoalPrompt).toHaveBeenCalledWith(
      expect.stringMatching(/^goal-\d+-[a-z0-9]+$/),
      'I want to learn React and build web applications'
    );

    // Verify navigation was called
    expect(mockNavigate).toHaveBeenCalledWith(
      expect.stringMatching(/^\/goals\/goal-\d+-[a-z0-9]+$/)
    );

    // Verify goal text was cleared
    expect(result.current.goalText).toBe('');
  });

  it('should not submit invalid goals', async () => {
    const { result } = renderHook(() => useGoalSubmission());

    // Set an invalid goal text (empty)
    act(() => {
      result.current.handleGoalChange('   ');
    });

    expect(result.current.isValid).toBe(false);

    // Try to submit the goal
    await act(async () => {
      await result.current.handleSubmit();
    });

    // Verify session storage was not called
    expect(sessionStorageUtils.saveGoalPrompt).not.toHaveBeenCalled();

    // Verify navigation was not called
    expect(mockNavigate).not.toHaveBeenCalled();

    // Verify goal text was not cleared
    expect(result.current.goalText).toBe('   ');
  });

  it('should handle example goal clicks', () => {
    const { result } = renderHook(() => useGoalSubmission());

    const exampleDescription = 'I want to become a full-stack developer with React and Node.js';

    act(() => {
      result.current.handleExampleClick(exampleDescription);
    });

    expect(result.current.goalText).toBe(exampleDescription);
    expect(result.current.isValid).toBe(true);
  });
});
