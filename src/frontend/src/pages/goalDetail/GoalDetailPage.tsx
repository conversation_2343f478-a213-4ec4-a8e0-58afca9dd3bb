import { useEffect } from 'react';
import { IconArrowLeft } from '@tabler/icons-react';
import { useParams } from 'react-router-dom';
import { Button, Container, Group, Stack, Text, Title } from '@mantine/core';
import { getGoalPrompt } from '../../utils/sessionStorage';
import { ChatContainer } from './components';
import { useChat, useChatStreaming, useGoalDetail, useGoalNavigation } from './hooks';
import classes from './GoalDetailPage.module.css';

export function GoalDetailPage() {
  const { goalId } = useParams<{ goalId: string }>();

  // Custom hooks
  const { goalDetail } = useGoalDetail(goalId);
  const { handleBackToGoals, handleBackToHome } = useGoalNavigation();
  const chat = useChat();
  const chatStreaming = useChatStreaming({
    goalId: goalId || '',
    chat,
  });

  // Auto-start with initial prompt from session storage
  useEffect(() => {
    if (goalId) {
      const userPrompt = getGoalPrompt(goalId);
      if (userPrompt && chat.messageCount === 0) {
        // Add the initial user message and start the conversation
        chatStreaming.sendMessage(userPrompt);
      }
    }
  }, [goalId, chat.messageCount, chatStreaming]);

  return (
    <Container size="xl" py="sm" px="sm">
      <Stack gap="sm">
        <Group justify="space-between" align="flex-start">
          <div>
            <Title order={1} className={classes.title}>
              {goalDetail?.title || 'Goal Details'}
            </Title>
            <Text size="lg" c="dimmed">
              {goalDetail?.description || `Goal ID: ${goalId || 'Unknown'}`}
            </Text>
          </div>

          <Group gap="sm">
            <Button
              variant="light"
              leftSection={<IconArrowLeft size={16} />}
              onClick={handleBackToHome}
            >
              Back to Home
            </Button>
            <Button variant="outline" onClick={handleBackToGoals}>
              View All Goals
            </Button>
          </Group>
        </Group>

        <div className={classes.content}>
          {/* Chat Interface */}
          <ChatContainer
            messages={chat.messages}
            onSendMessage={chatStreaming.sendMessage}
            isLoading={chatStreaming.isStreaming}
            disabled={chatStreaming.isStreaming}
            height="calc(100vh - 200px)"
          />
        </div>
      </Stack>
    </Container>
  );
}
