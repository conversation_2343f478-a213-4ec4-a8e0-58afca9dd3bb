import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { GoalDetailPage } from './GoalDetailPage';

// Mock the chat hooks
vi.mock('./hooks/useChat', () => ({
  useChat: () => ({
    messages: [],
    addMessage: vi.fn(),
    updateMessage: vi.fn(),
    clearMessages: vi.fn(),
    getLastMessage: vi.fn(),
    messageCount: 0,
  }),
}));

vi.mock('./hooks/useChatStreaming', () => ({
  useChatStreaming: () => ({
    isStreaming: false,
    error: null,
    sendMessage: vi.fn(),
    reset: vi.fn(),
  }),
}));

// Mock session storage utility
vi.mock('../../utils/sessionStorage', () => ({
  getGoalPrompt: vi.fn(() => null),
}));

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ goalId: 'test-goal-123' }),
  };
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineProvider>
      <MemoryRouter>{component}</MemoryRouter>
    </MantineProvider>
  );
};

describe('GoalDetailPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders the page title and goal ID for unknown goal', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Unknown Goal')).toBeInTheDocument();
    expect(screen.getByText('Goal details not found.')).toBeInTheDocument();
  });

  it('displays chat interface', () => {
    renderWithProviders(<GoalDetailPage />);

    // Check for chat interface elements
    expect(screen.getByText('Start a conversation')).toBeInTheDocument();
    expect(
      screen.getByText('Ask questions about your learning goals and get personalized guidance')
    ).toBeInTheDocument();
  });

  it('shows navigation buttons', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Back to Home')).toBeInTheDocument();
    expect(screen.getByText('View All Goals')).toBeInTheDocument();
  });

  it('renders chat input component', () => {
    renderWithProviders(<GoalDetailPage />);

    // Verify the chat input is present
    expect(screen.getByPlaceholderText('Ask about your learning goals...')).toBeInTheDocument();
    expect(screen.getByText('Send')).toBeInTheDocument();
  });

  it('handles unknown goal gracefully', () => {
    renderWithProviders(<GoalDetailPage />);

    // Component should render fallback content for unknown goal
    expect(screen.getByText('Unknown Goal')).toBeInTheDocument();
    expect(screen.getByText('Goal details not found.')).toBeInTheDocument();
  });

  it('displays chat container with proper styling', () => {
    renderWithProviders(<GoalDetailPage />);

    // Verify the chat container is rendered
    expect(screen.getByText('Press Enter to send, Shift+Enter for new line')).toBeInTheDocument();
  });
});
