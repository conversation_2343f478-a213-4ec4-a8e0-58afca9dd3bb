import { useCallback, useEffect, useState } from 'react';
import { createLearningAgentPayload, streamLearningAgent } from '../../../utils/api';
import { getGoalPrompt } from '../../../utils/sessionStorage';
import { ResponseState } from '../components/AgentResponse/AgentResponse';

export interface LearningAgentState {
  state: ResponseState;
  error: string | null;
  message: string;
}

export interface UseLearningAgentReturn {
  analysisState: ResponseState;
  error: string | null;
  message: string;
  startAnalysis: () => Promise<void>;
  reset: () => void;
}

/**
 * Custom hook for managing learning supervisor agent API calls with streaming support
 * Handles the API call during goal detail page initialization
 */
export function useLearningAgent(goalId: string | undefined): UseLearningAgentReturn {
  const [state, setState] = useState<LearningAgentState>({
    state: ResponseState.IDLE,
    error: null,
    message: '',
  });

  /**
   * Starts the learning analysis by calling the streaming API
   */
  const startAnalysis = useCallback(async () => {
    if (!goalId) {
      setState((prev) => ({
        ...prev,
        state: ResponseState.ERROR,
        error: 'Goal ID is required',
      }));
      return;
    }

    // Get user prompt from session storage
    const userPrompt = getGoalPrompt(goalId);
    if (!userPrompt) {
      setState((prev) => ({
        ...prev,
        state: ResponseState.ERROR,
        error: 'No user prompt found in session storage',
      }));
      return;
    }

    // Reset state and start loading
    setState({
      state: ResponseState.LOADING,
      error: null,
      message: '',
    });

    try {
      // Create the API payload
      const payload = createLearningAgentPayload(userPrompt, goalId);

      // Start streaming
      setState((prev) => ({
        ...prev,
        state: ResponseState.LOADING,
      }));

      // Make the streaming API call
      const response = await streamLearningAgent(payload);

      if (response.success) {
        setState((prev) => ({
          ...prev,
          state: ResponseState.COMPLETE,
          message: response.content || '',
        }));
      } else {
        setState((prev) => ({
          ...prev,
          state: ResponseState.ERROR,
          error: response.error || 'Unknown error occurred',
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        state: ResponseState.ERROR,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }));
    }
  }, []);

  /**
   * Resets the hook state
   */
  const reset = useCallback(() => {
    setState({
      state: ResponseState.IDLE,
      error: null,
      message: '',
    });
  }, []);

  /**
   * Auto-start analysis when goalId changes and is available
   */
  useEffect(() => {
    if (goalId) {
      console.log('trigger call from useEffect');
      startAnalysis();
    }
  }, [goalId]);

  return {
    analysisState: state.state,
    error: state.error,
    message: state.message,
    startAnalysis,
    reset,
  };
}
