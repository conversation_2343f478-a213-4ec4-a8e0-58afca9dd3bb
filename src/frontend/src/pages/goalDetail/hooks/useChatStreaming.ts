import { useCallback, useState } from 'react';
import { createLearningAgentPayload, streamLearningAgent } from '../../../utils/api';
import { UseChatReturn } from './useChat';

export interface UseChatStreamingProps {
  goalId: string;
  chat: UseChatReturn;
}

export interface UseChatStreamingReturn {
  isStreaming: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  reset: () => void;
}

/**
 * Custom hook for handling streaming chat messages with the learning agent API
 * Integrates with the chat hook to manage message state during streaming
 */
export function useChatStreaming({ goalId, chat }: UseChatStreamingProps): UseChatStreamingReturn {
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(
    async (message: string) => {
      if (isStreaming) {
        return;
      }

      try {
        setIsStreaming(true);
        setError(null);

        // Add user message to chat
        chat.addMessage({
          type: 'user',
          content: message,
        });

        // Create API payload
        const payload = createLearningAgentPayload(message, goalId);

        // Add initial agent message (will be updated during streaming)
        const agentMessageId = chat.addMessage({
          type: 'agent',
          content: '',
          isStreaming: true,
        });

        // Start streaming
        const response = await streamLearningAgent(payload);

        if (response.success) {
          // Update agent message with final content
          chat.updateMessage(agentMessageId, {
            content: response.content || '',
            isStreaming: false,
          });
        } else {
          // Update agent message with error
          chat.updateMessage(agentMessageId, {
            content: `Error: ${response.error || 'Unknown error occurred'}`,
            isStreaming: false,
          });
          setError(response.error || 'Unknown error occurred');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);

        // Add error message to chat
        chat.addMessage({
          type: 'agent',
          content: `Error: ${errorMessage}`,
        });
      } finally {
        setIsStreaming(false);
      }
    },
    [goalId, chat, isStreaming]
  );

  const reset = useCallback(() => {
    setIsStreaming(false);
    setError(null);
  }, []);

  return {
    isStreaming,
    error,
    sendMessage,
    reset,
  };
}
