import { useCallback, useMemo, useState } from 'react';
import { ChatMessageProps } from '../components/Chat';

export interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface UseChatReturn {
  messages: ChatMessageProps[];
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => string;
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
  clearMessages: () => void;
  getLastMessage: () => ChatMessage | undefined;
  messageCount: number;
}

/**
 * Custom hook for managing chat state and message history
 * Provides methods for adding, updating, and managing chat messages
 */
export function useChat(): UseChatReturn {
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  const addMessage = useCallback((message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const id = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newMessage: ChatMessage = {
      ...message,
      id,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    return id;
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<ChatMessage>) => {
    setMessages((prev) => prev.map((msg) => (msg.id === id ? { ...msg, ...updates } : msg)));
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const getLastMessage = useCallback(() => {
    return messages[messages.length - 1];
  }, [messages]);

  const messageCount = useMemo(() => messages.length, [messages]);

  // Convert internal messages to ChatMessageProps format
  const chatMessages: ChatMessageProps[] = useMemo(
    () =>
      messages.map((msg) => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp,
        isStreaming: msg.isStreaming,
      })),
    [messages]
  );

  return {
    messages: chatMessages,
    addMessage,
    updateMessage,
    clearMessages,
    getLastMessage,
    messageCount,
  };
}
