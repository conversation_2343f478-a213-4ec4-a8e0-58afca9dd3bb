import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as sessionStorageUtils from '../../../../utils/sessionStorage';
import { useGoalDetail } from '../useGoalDetail';

// Mock session storage utilities
vi.mock('../../../../utils/sessionStorage', () => ({
  getGoalPrompt: vi.fn(),
}));

describe('useGoalDetail integration with session storage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return mock goal data for known goal IDs', () => {
    const { result } = renderHook(() => useGoalDetail('javascript-mastery'));

    expect(result.current.goalDetail).toEqual(
      expect.objectContaining({
        id: 'javascript-mastery',
        title: 'JavaScript Mastery',
        category: 'Programming',
      })
    );
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should return custom goal data from session storage for unknown goal IDs', () => {
    const goalId = 'custom-goal-123';
    const customPrompt = 'I want to learn React and build modern web applications';

    vi.mocked(sessionStorageUtils.getGoalPrompt).mockReturnValue(customPrompt);

    const { result } = renderHook(() => useGoalDetail(goalId));

    expect(sessionStorageUtils.getGoalPrompt).toHaveBeenCalledWith(goalId);
    expect(result.current.goalDetail).toEqual({
      id: goalId,
      title: 'Custom Learning Goal',
      description: customPrompt,
      category: 'Custom',
      difficulty: 'beginner',
      estimatedDuration: 'To be determined',
      progress: 0,
      isCompleted: false,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  it('should return fallback data when goal ID is unknown and no session storage data exists', () => {
    const goalId = 'unknown-goal-456';

    vi.mocked(sessionStorageUtils.getGoalPrompt).mockReturnValue(null);

    const { result } = renderHook(() => useGoalDetail(goalId));

    expect(sessionStorageUtils.getGoalPrompt).toHaveBeenCalledWith(goalId);
    expect(result.current.goalDetail).toEqual({
      id: goalId,
      title: 'Unknown Goal',
      description: 'Goal details not found.',
      category: 'Unknown',
      difficulty: 'beginner',
      estimatedDuration: '0 hours',
      progress: 0,
      isCompleted: false,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  it('should return null when goalId is undefined', () => {
    const { result } = renderHook(() => useGoalDetail(undefined));

    expect(result.current.goalDetail).toBeNull();
    expect(sessionStorageUtils.getGoalPrompt).not.toHaveBeenCalled();
  });

  it('should not call session storage for known mock goals', () => {
    const { result } = renderHook(() => useGoalDetail('react-development'));

    expect(result.current.goalDetail).toEqual(
      expect.objectContaining({
        id: 'react-development',
        title: 'React Development',
      })
    );
    expect(sessionStorageUtils.getGoalPrompt).not.toHaveBeenCalled();
  });
});
