import { useEffect, useRef } from 'react';
import { Box, ScrollArea, Stack, Text } from '@mantine/core';
import { ChatMessage, ChatMessageProps } from '../ChatMessage';

export interface ChatHistoryProps {
  messages: ChatMessageProps[];
  isLoading?: boolean;
  autoScroll?: boolean;
}

/**
 * Chat history component that displays a scrollable list of chat messages
 * with auto-scroll functionality and loading states
 */
export function ChatHistory({ messages, isLoading = false, autoScroll = true }: ChatHistoryProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && bottomRef.current) {
      bottomRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
    }
  }, [messages, autoScroll]);

  if (messages.length === 0 && !isLoading) {
    return (
      <Box
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '200px',
          textAlign: 'center',
        }}
      >
        <Stack gap="sm" align="center">
          <Text size="lg" c="dimmed">
            Start a conversation
          </Text>
          <Text size="sm" c="dimmed">
            Ask questions about your learning goals and get personalized guidance
          </Text>
        </Stack>
      </Box>
    );
  }

  return (
    <ScrollArea
      ref={scrollAreaRef}
      style={{
        flex: 1,
        height: '100%',
      }}
      scrollbarSize={6}
      offsetScrollbars
    >
      <Box
        style={{
          padding: 'var(--mantine-spacing-md)',
          minHeight: '100%',
        }}
      >
        <Stack gap="sm">
          {messages.map((message) => (
            <ChatMessage key={message.id} {...message} />
          ))}

          {/* Loading indicator for streaming messages */}
          {isLoading && (
            <Box
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                marginBottom: 'var(--mantine-spacing-md)',
              }}
            >
              <Box
                style={{
                  backgroundColor: 'var(--mantine-color-gray-1)',
                  padding: 'var(--mantine-spacing-sm) var(--mantine-spacing-md)',
                  borderRadius: 'var(--mantine-radius-md)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'var(--mantine-spacing-xs)',
                }}
              >
                <Box
                  style={{
                    display: 'flex',
                    gap: '4px',
                  }}
                >
                  {[1, 2, 3].map((dot) => (
                    <Box
                      key={dot}
                      style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        backgroundColor: 'var(--mantine-color-gray-6)',
                        animation: `typing-dot 1.4s infinite ease-in-out`,
                        animationDelay: `${(dot - 1) * 0.2}s`,
                      }}
                    />
                  ))}
                </Box>
                <Text size="sm" c="dimmed">
                  Agent is typing...
                </Text>
              </Box>
            </Box>
          )}

          {/* Invisible element for auto-scroll */}
          <div ref={bottomRef} />
        </Stack>
      </Box>

      <style>
        {`
          @keyframes typing-dot {
            0%, 80%, 100% {
              transform: scale(0.8);
              opacity: 0.5;
            }
            40% {
              transform: scale(1);
              opacity: 1;
            }
          }
        `}
      </style>
    </ScrollArea>
  );
}
