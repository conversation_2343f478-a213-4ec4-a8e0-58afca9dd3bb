import { KeyboardEvent, useCallback, useState } from 'react';
import { IconSend } from '@tabler/icons-react';
import { Box, Button, Group, Loader, Textarea } from '@mantine/core';

export interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
}

/**
 * Chat input component for sending user messages
 * Features auto-resize, keyboard shortcuts, and loading states
 */
export function ChatInput({
  onSendMessage,
  disabled = false,
  isLoading = false,
  placeholder = 'Type your message...',
}: ChatInputProps) {
  const [message, setMessage] = useState('');

  const handleSend = useCallback(() => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled && !isLoading) {
      onSendMessage(trimmedMessage);
      setMessage('');
    }
  }, [message, onSendMessage, disabled, isLoading]);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  const canSend = message.trim().length > 0 && !disabled && !isLoading;

  return (
    <Box
      style={{
        position: 'sticky',
        bottom: 0,
        backgroundColor: 'var(--mantine-color-body)',
        borderTop: '1px solid var(--mantine-color-gray-3)',
        padding: 'var(--mantine-spacing-md)',
        zIndex: 100,
      }}
    >
      <Group gap="sm" align="flex-end">
        <Textarea
          value={message}
          onChange={(event) => setMessage(event.currentTarget.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          autosize
          minRows={1}
          maxRows={4}
          style={{ flex: 1 }}
          styles={{
            input: {
              resize: 'none',
            },
          }}
        />

        <Button
          onClick={handleSend}
          disabled={!canSend}
          loading={isLoading}
          size="sm"
          leftSection={!isLoading ? <IconSend size={16} /> : undefined}
        >
          {isLoading ? <Loader size="xs" /> : 'Send'}
        </Button>
      </Group>

      <Box mt="xs">
        <small style={{ color: 'var(--mantine-color-dimmed)' }}>
          Press Enter to send, Shift+Enter for new line
        </small>
      </Box>
    </Box>
  );
}
