import { Box, Card } from '@mantine/core';
import { ChatHistory, ChatHistoryProps } from '../ChatHistory';
import { ChatInput, ChatInputProps } from '../ChatInput';

export interface ChatContainerProps {
  messages: ChatHistoryProps['messages'];
  onSendMessage: ChatInputProps['onSendMessage'];
  isLoading?: boolean;
  disabled?: boolean;
  height?: string | number;
}

/**
 * Main chat container that combines chat history and input
 * Provides a complete chat interface with responsive design
 */
export function ChatContainer({
  messages,
  onSendMessage,
  isLoading = false,
  disabled = false,
  height = '600px',
}: ChatContainerProps) {
  return (
    <Card
      shadow="sm"
      padding={0}
      radius="md"
      withBorder
      style={{
        height,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      {/* Chat History */}
      <Box style={{ flex: 1, overflow: 'hidden' }}>
        <ChatHistory messages={messages} isLoading={isLoading} autoScroll />
      </Box>

      {/* Chat Input */}
      <ChatInput
        onSendMessage={onSendMessage}
        disabled={disabled}
        isLoading={isLoading}
        placeholder="Ask about your learning goals..."
      />
    </Card>
  );
}
