import { IconRobot, IconUser } from '@tabler/icons-react';
import { Avatar, Box, Text } from '@mantine/core';
import { MarkdownRenderer } from '@/components';

export interface ChatMessageProps {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

/**
 * Individual chat message component that displays user or agent messages
 * with appropriate styling and formatting
 */
export function ChatMessage({ type, content, timestamp, isStreaming = false }: ChatMessageProps) {
  const isUser = type === 'user';

  return (
    <Box
      style={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        marginBottom: 'var(--mantine-spacing-md)',
      }}
    >
      <Box
        style={{
          maxWidth: '80%',
          display: 'flex',
          flexDirection: isUser ? 'row-reverse' : 'row',
          gap: 'var(--mantine-spacing-sm)',
          alignItems: 'flex-start',
        }}
      >
        {/* Avatar */}
        <Avatar size="sm" radius="xl" color={isUser ? 'blue' : 'green'} style={{ flexShrink: 0 }}>
          {isUser ? <IconUser size={16} /> : <IconRobot size={16} />}
        </Avatar>

        {/* Message Content */}
        <Box
          style={{
            backgroundColor: isUser ? 'var(--mantine-color-blue-6)' : 'var(--mantine-color-gray-1)',
            color: isUser ? 'white' : 'var(--mantine-color-dark-7)',
            padding: 'var(--mantine-spacing-sm) var(--mantine-spacing-md)',
            borderRadius: 'var(--mantine-radius-md)',
            position: 'relative',
            wordBreak: 'break-word',
          }}
        >
          {/* Message content */}
          {isUser ? (
            <Text size="sm">{content}</Text>
          ) : (
            <Box>
              <MarkdownRenderer>{content}</MarkdownRenderer>
              {isStreaming && (
                <Box
                  component="span"
                  style={{
                    display: 'inline-block',
                    width: '8px',
                    height: '16px',
                    backgroundColor: 'var(--mantine-color-gray-6)',
                    marginLeft: '2px',
                    animation: 'blink 1s infinite',
                  }}
                />
              )}
            </Box>
          )}

          {/* Timestamp */}
          <Text
            size="xs"
            c={isUser ? 'blue.1' : 'dimmed'}
            style={{
              marginTop: 'var(--mantine-spacing-xs)',
              textAlign: isUser ? 'right' : 'left',
            }}
          >
            {timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </Box>
      </Box>

      <style>
        {`
          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
          }
        `}
      </style>
    </Box>
  );
}
