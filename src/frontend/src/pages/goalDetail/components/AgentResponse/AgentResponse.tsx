import { useEffect, useState } from 'react';
import { Box, Card, Group, Loader, Stack, Text } from '@mantine/core';
import { MarkdownRenderer } from '@/components';

export enum ResponseState {
  IDLE = 'idle',
  LOADING = 'loading',
  COMPLETE = 'complete',
  ERROR = 'error',
}

export interface AgentResponseProps {
  state: ResponseState;
  error: string | null;
  message: string;
  isStreaming?: boolean;
}

/**
 * Component that displays agent responses in a chat-style format.
 * Shows loading animation while processing and renders markdown content when complete.
 */
export function AgentResponse({
  state,
  error,
  message,
  isStreaming: _isStreaming = false,
  // Ignoring streaming-related props for simplicity
}: AgentResponseProps) {
  const [dots, setDots] = useState('');

  // Chat-style typing animation
  useEffect(() => {
    if (state === ResponseState.LOADING) {
      const interval = setInterval(() => {
        setDots((prev) => {
          if (prev === '...') {
            return '';
          }
          return `${prev}.`;
        });
      }, 500);
      return () => clearInterval(interval);
    }
    setDots('');
  }, [state]);

  // Don't render the component when complete (let parent handle results display)
  if (state === ResponseState.COMPLETE) {
    console.log(message, state);
    return <MarkdownRenderer>{message}</MarkdownRenderer>;
  }

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack gap="md">
        {/* Simple loading message with chat-style animation */}
        {state === ResponseState.LOADING && (
          <Box
            style={{
              backgroundColor: 'var(--mantine-color-blue-0)',
              padding: 'var(--mantine-spacing-md)',
              borderRadius: 'var(--mantine-radius-md)',
              border: '1px solid var(--mantine-color-blue-3)',
              marginTop: 'var(--mantine-spacing-sm)',
            }}
          >
            <Group gap="sm" align="center">
              <Loader size="sm" />
              <Text size="sm" c="blue">
                Analyzing your skills and experience{dots}
              </Text>
            </Group>
          </Box>
        )}

        {/* Error state */}
        {error && (
          <Box
            style={{
              backgroundColor: 'var(--mantine-color-red-0)',
              padding: 'var(--mantine-spacing-sm)',
              borderRadius: 'var(--mantine-radius-sm)',
              border: '1px solid var(--mantine-color-red-3)',
            }}
          >
            <Text size="sm" c="red">
              Error: {error}
            </Text>
          </Box>
        )}
      </Stack>
    </Card>
  );
}
