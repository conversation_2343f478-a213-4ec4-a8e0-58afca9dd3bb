import { render, screen } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { AgentResponse, ResponseState } from '../AgentResponse';

const renderWithProviders = (component: React.ReactElement) => {
  return render(<MantineProvider>{component}</MantineProvider>);
};

describe('AgentResponse', () => {
  const defaultProps = {
    state: ResponseState.IDLE,
    error: null,
    message: '',
  };

  it('renders idle state correctly', () => {
    renderWithProviders(<AgentResponse {...defaultProps} />);

    // For idle state, the component should not render anything visible
    expect(screen.queryByText('Analyzing your skills and experience')).not.toBeInTheDocument();
  });

  it('renders loading state correctly', () => {
    renderWithProviders(<AgentResponse {...defaultProps} state={ResponseState.LOADING} />);

    expect(screen.getByText(/Analyzing your skills and experience/)).toBeInTheDocument();
  });

  it('renders complete state correctly', () => {
    const message = '# Skills Analysis\n\nYour skills have been analyzed successfully.';

    renderWithProviders(
      <AgentResponse {...defaultProps} state={ResponseState.COMPLETE} message={message} />
    );

    expect(screen.getByText('Skills Analysis')).toBeInTheDocument();
    expect(screen.getByText('Your skills have been analyzed successfully.')).toBeInTheDocument();
  });

  it('renders error state correctly', () => {
    const errorMessage = 'Failed to connect to server';

    renderWithProviders(
      <AgentResponse {...defaultProps} state={ResponseState.ERROR} error={errorMessage} />
    );

    expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument();
  });

  it('renders markdown content correctly', () => {
    const markdownContent =
      '# Skills Analysis\n\n## Current Skills\n- JavaScript\n- React\n\n## Recommended Learning Path\n1. Learn TypeScript\n2. Master React Hooks\n3. Study Next.js';

    renderWithProviders(
      <AgentResponse {...defaultProps} state={ResponseState.COMPLETE} message={markdownContent} />
    );

    // Check if markdown content is rendered
    expect(screen.getByText('Skills Analysis')).toBeInTheDocument();
    expect(screen.getByText('Current Skills')).toBeInTheDocument();
    expect(screen.getByText('Recommended Learning Path')).toBeInTheDocument();
    expect(screen.getByText('JavaScript')).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument();
  });
});
