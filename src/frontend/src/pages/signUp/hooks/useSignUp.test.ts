import { act, renderHook } from '@test-utils';
import { vi } from 'vitest';
import * as AuthContext from '@/contexts/AuthContext';
import { useSignUp } from './useSignUp';

const mockNavigate = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return { ...actual, useNavigate: () => mockNavigate };
});

describe('useSignUp hook', () => {
  const mockRegister = vi.fn();

  beforeEach(() => {
    mockNavigate.mockClear();
    mockRegister.mockClear();
    vi.spyOn(AuthContext, 'useAuth').mockReturnValue({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      signIn: vi.fn(),
      register: mockRegister,
      signOut: vi.fn(),
      refreshAuth: vi.fn(),
      updateUserSkills: vi.fn(),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('updates fields and calculates strength', () => {
    const { result } = renderHook(() => useSignUp());

    act(() => {
      result.current.handleFirstNameChange(' John ');
      result.current.handleLastNameChange(' Doe ');
      result.current.handleEmailChange(' <EMAIL> ');
      result.current.handlePasswordChange('Password1!');
      result.current.handleConfirmPasswordChange('Password1!');
      result.current.handleTermsChange(true);
    });

    expect(result.current.formData).toEqual({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'Password1!',
      confirmPassword: 'Password1!',
      acceptTerms: true,
    });
    expect(result.current.passwordStrength.score).toBeGreaterThan(0);
  });

  test('successful submit navigates to dashboard', async () => {
    mockRegister.mockResolvedValue({});

    const { result } = renderHook(() => useSignUp());

    act(() => {
      result.current.handleFirstNameChange('John');
      result.current.handleLastNameChange('Doe');
      result.current.handleEmailChange('<EMAIL>');
      result.current.handlePasswordChange('Password1!');
      result.current.handleConfirmPasswordChange('Password1!');
      result.current.handleTermsChange(true);
    });

    await act(async () => {
      await result.current.handleSubmit();
    });

    expect(mockRegister).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    expect(result.current.errors).toEqual([]);
  });

  test('registration error updates errors state', async () => {
    mockRegister.mockRejectedValue(new Error('Email exists'));

    const { result } = renderHook(() => useSignUp());

    act(() => {
      result.current.handleFirstNameChange('John');
      result.current.handleLastNameChange('Doe');
      result.current.handleEmailChange('<EMAIL>');
      result.current.handlePasswordChange('Password1!');
      result.current.handleConfirmPasswordChange('Password1!');
      result.current.handleTermsChange(true);
    });

    await act(async () => {
      await result.current.handleSubmit();
    });

    expect(result.current.errors[0]).toEqual({ field: 'general', message: 'Email exists' });
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});
