import {
  calculatePasswordStrength,
  getPasswordStrengthColor,
  getPasswordStrengthLabel,
} from './passwordStrength';

describe('passwordStrength utilities', () => {
  test('calculatePasswordStrength evaluates strength and feedback', () => {
    const empty = calculatePasswordStrength('');
    expect(empty.score).toBe(0);
    expect(empty.feedback).toContain('Password is required');

    const weak = calculatePasswordStrength('abc');
    expect(weak.score).toBe(1);
    expect(weak.feedback).toContain('Very weak password');

    const strong = calculatePasswordStrength('Abcd1234!');
    expect(strong.score).toBe(4);
    expect(strong.feedback).toContain('Strong password');
    expect(strong.hasNumber).toBe(true);
  });

  test('getPasswordStrengthColor returns palette values', () => {
    expect(getPasswordStrengthColor(1)).toBe('red');
    expect(getPasswordStrengthColor(2)).toBe('orange');
    expect(getPasswordStrengthColor(3)).toBe('yellow');
    expect(getPasswordStrengthColor(4)).toBe('green');
    expect(getPasswordStrengthColor(99)).toBe('gray');
  });

  test('getPasswordStrengthLabel returns readable labels', () => {
    expect(getPasswordStrengthLabel(0)).toBe('No password');
    expect(getPasswordStrengthLabel(1)).toBe('Very weak');
    expect(getPasswordStrengthLabel(2)).toBe('Weak');
    expect(getPasswordStrengthLabel(3)).toBe('Good');
    expect(getPasswordStrengthLabel(4)).toBe('Strong');
    expect(getPasswordStrengthLabel(99)).toBe('Unknown');
  });
});
