import { SignUpFormData } from '../types';
import {
  sanitizeEmail,
  sanitizeName,
  validateConfirmPassword,
  validateEmail,
  validateFirstName,
  validateLastName,
  validatePassword,
  validateSignUpForm,
  validateTermsAcceptance,
} from './validation';

describe('signUp validation utilities', () => {
  test('validateFirstName and validateLastName enforce rules', () => {
    expect(validateFirstName('')).toBe('First name is required');
    expect(validateFirstName('A')).toBe('First name must be at least 2 characters');
    expect(validateFirstName('John1')).toBe(
      'First name can only contain letters, spaces, hyphens, and apostrophes'
    );
    expect(validateFirstName('John')).toBeNull();

    expect(validateLastName('')).toBe('Last name is required');
    expect(validateLastName('B')).toBe('Last name must be at least 2 characters');
    expect(validateLastName('Doe1')).toBe(
      'Last name can only contain letters, spaces, hyphens, and apostrophes'
    );
    expect(validateLastName('Doe')).toBeNull();
  });

  test('validateEmail behaves like sign-in validation', () => {
    expect(validateEmail('')).toBe('Email is required');
    expect(validateEmail('bad')).toBe('Please enter a valid email address');
    expect(validateEmail('<EMAIL>')).toBeNull();
  });

  test('validatePassword uses password strength', () => {
    expect(validatePassword('')).toBe('Password is required');
    expect(validatePassword('short')).toBe('Password must be at least 8 characters long');
    expect(validatePassword('weakpass')).toBe(
      'Password is too weak. Please include uppercase, lowercase, numbers, and special characters'
    );
    expect(validatePassword('StrongPass1!')).toBeNull();
  });

  test('validateConfirmPassword and terms acceptance', () => {
    expect(validateConfirmPassword('a', '')).toBe('Please confirm your password');
    expect(validateConfirmPassword('a', 'b')).toBe('Passwords do not match');
    expect(validateConfirmPassword('a', 'a')).toBeNull();
    expect(validateTermsAcceptance(false)).toBe('You must accept the terms and conditions');
    expect(validateTermsAcceptance(true)).toBeNull();
  });

  test('validateSignUpForm aggregates errors', () => {
    const data: SignUpFormData = {
      firstName: '',
      lastName: '',
      email: 'bad',
      password: 'short',
      confirmPassword: 'mismatch',
      acceptTerms: false,
    };
    const errors = validateSignUpForm(data);
    expect(errors).toHaveLength(6);
  });

  test('sanitize helpers', () => {
    expect(sanitizeEmail(' <EMAIL> ')).toBe('<EMAIL>');
    expect(sanitizeName(' John   Doe ')).toBe('John Doe');
  });
});
