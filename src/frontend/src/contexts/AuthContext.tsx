import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { getAuthService } from '../services/authService';
import { AuthContextType, AuthCredentials, RegisterData, User } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize authentication state from storage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const authService = await getAuthService();
        const storedAuth = authService.getStoredAuth();
        setIsAuthenticated(storedAuth.isAuthenticated);
        setUser(storedAuth.user);
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        // Failed to initialize auth, reset to default state
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (credentials: AuthCredentials): Promise<User> => {
    try {
      const authService = await getAuthService();
      const user = await authService.signIn(credentials);
      setIsAuthenticated(true);
      setUser(user);
      return user;
    } catch (error) {
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const register = async (data: RegisterData): Promise<User> => {
    try {
      const authService = await getAuthService();
      const user = await authService.register(data);
      setIsAuthenticated(true);
      setUser(user);
      return user;
    } catch (error) {
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      const authService = await getAuthService();
      await authService.signOut();
    } finally {
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const refreshAuth = async () => {
    try {
      const authService = await getAuthService();
      const storedAuth = authService.getStoredAuth();
      setIsAuthenticated(storedAuth.isAuthenticated);
      setUser(storedAuth.user);
    } catch (error) {
      console.error('Failed to refresh auth:', error);
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const updateUserSkills = async (userId: string): Promise<User> => {
    const authService = await getAuthService();
    const updatedUser = await authService.updateUserSkills(userId);
    setUser(updatedUser);
    return updatedUser;
  };

  const value: AuthContextType = {
    isAuthenticated,
    user,
    isLoading,
    signIn,
    register,
    signOut,
    refreshAuth,
    updateUserSkills,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
