from dataclasses import dataclass

from langgraph.pregel import Pregel

from agents.learning_supervisor_agent import learning_supervisor_agent
from agents.resume_rag_agent import resume_rag_agent
from agents.roadmap_agent import roadmap_agent
from schema import AgentInfo

DEFAULT_AGENT = "learning-supervisor-agent"


@dataclass
class Agent:
    description: str
    graph: Pregel


agents: dict[str, Agent] = {
    "hr-assistance": Agent(
        description="A resume RAG agent that can search through resume data to answer questions about people's skills, experience, and projects",
        graph=resume_rag_agent,
    ),
    # "command-agent": Agent(description="A command agent.", graph=command_agent),
    # "bg-task-agent": Agent(description="A background task agent.", graph=bg_task_agent),
    # Temporarily disable langgraph_supervisor_agent due to import issues
    # "langgraph-supervisor-agent": Agent(
    #     description="A langgraph supervisor agent", graph=langgraph_supervisor_agent
    # ),
    "learning-supervisor-agent": Agent(
        description="Supervisor agent coordinating HR skill sync, gap skill and roadmap generation",
        graph=learning_supervisor_agent,
    ),
    "learning-roadmap-agent": Agent(
        description="AI learning coach that creates personalized learning roadmaps based on skill gaps and time constraints",
        graph=roadmap_agent,
    ),
}


def get_agent(agent_id: str) -> Pregel:
    return agents[agent_id].graph


def get_all_agent_info() -> list[AgentInfo]:
    return [
        AgentInfo(key=agent_id, description=agent.description) for agent_id, agent in agents.items()
    ]


if __name__ == "__main__":
    print("hello")
