from typing import List, Optional
from pydantic import BaseModel, Field
from .roadmap_constants import Module, Course

# Create the list of courses
courses = [
    Course(
        title="Complete Web Development Bootcamp",
        description="Comprehensive web development course covering HTML, CSS, JavaScript, Node.js, React, and more.",
        category="Software Development",
        rating=4.8,
        prerequisites=["No prior programming experience required"],
        modules=[
            Module(
                name="Introduction to HTML5",
                duration=5.0,
                topics=["Basic HTML Structure", "HTML Tags and Elements", "Forms and Input Elements", 
                        "Semantic HTML", "HTML5 New Features"]
            ),
            Module(
                name="CSS Fundamentals",
                duration=8.0,
                topics=["CSS Selectors", "Box Model", "Flexbox Layout", "CSS Grid", "Responsive Design"]
            ),
            Module(
                name="JavaScript Basics",
                duration=12.0,
                topics=["Variables and Data Types", "Control Flow", "Functions", 
                        "DOM Manipulation", "ES6+ Features"]
            )
        ]
    ),
    
    Course(
        title="Python for Data Science and Machine Learning",
        description="Learn Python for data analysis, visualization, and machine learning algorithms.",
        category="Software Development",
        rating=4.7,
        prerequisites=["Basic programming knowledge"],
        modules=[
            Module(
                name="Python Fundamentals",
                duration=6.0,
                topics=["Python Installation and Setup", "Basic Data Types", 
                        "Control Flow and Functions", "Object-Oriented Programming"]
            ),
            Module(
                name="Data Analysis with Pandas",
                duration=10.0,
                topics=["Series and DataFrames", "Data Cleaning", "Data Transformation", "Time Series Analysis"]
            ),
            Module(
                name="Machine Learning with Scikit-Learn",
                duration=14.0,
                topics=["Supervised Learning", "Unsupervised Learning", "Model Evaluation", 
                        "Neural Networks and Deep Learning"]
            )
        ]
    ),
    
    Course(
        title="PMP Certification Exam Prep",
        description="Complete preparation for the Project Management Professional (PMP) certification exam.",
        category="Project Management",
        rating=4.8,
        prerequisites=["Project management experience"],
        modules=[
            Module(
                name="Project Integration Management",
                duration=6.0,
                topics=["Develop Project Charter", "Develop Project Management Plan", 
                        "Direct and Manage Project Work", "Monitor and Control Project Work", 
                        "Perform Integrated Change Control"]
            ),
            Module(
                name="Project Scope Management",
                duration=5.0,
                topics=["Plan Scope Management", "Collect Requirements", "Define Scope", 
                        "Create WBS", "Validate and Control Scope"]
            ),
            Module(
                name="Project Schedule Management",
                duration=5.0,
                topics=["Plan Schedule Management", "Define Activities", "Sequence Activities", 
                        "Estimate Activity Durations", "Develop and Control Schedule"]
            )
        ]
    ),
    
    Course(
        title="Complete Spanish Language Course: Zero to Fluency",
        description="Comprehensive Spanish course taking you from complete beginner to fluent speaker.",
        category="Foreign Language",
        rating=4.9,
        prerequisites=["None"],
        modules=[
            Module(
                name="Spanish Foundations",
                duration=10.0,
                topics=["Pronunciation and Alphabet", "Basic Greetings and Introductions", 
                        "Numbers, Colors, and Basic Nouns", "Present Tense of Regular Verbs"]
            ),
            Module(
                name="Intermediate Conversation",
                duration=15.0,
                topics=["Past Tenses", "Future and Conditional Tenses", "Daily Routines and Activities", 
                        "Shopping and Dining Vocabulary", "Travel and Directions"]
            ),
            Module(
                name="Advanced Spanish",
                duration=12.0,
                topics=["Subjunctive Mood", "Idiomatic Expressions", "Business Spanish", "Cultural Nuances"]
            )
        ]
    ),
    
    Course(
        title="React - The Complete Guide",
        description="Dive into React, Redux, React Hooks, and build powerful web applications.",
        category="Software Development",
        rating=4.8,
        prerequisites=["Basic JavaScript knowledge"],
        modules=[
            Module(
                name="React Fundamentals",
                duration=10.0,
                topics=["Introduction to React", "Components and Props", "State and Lifecycle", 
                        "Handling Events", "Conditional Rendering"]
            ),
            Module(
                name="Advanced React Concepts",
                duration=15.0,
                topics=["React Hooks", "Context API", "Error Boundaries", 
                        "Refs and the DOM", "Performance Optimization"]
            ),
            Module(
                name="Redux and State Management",
                duration=12.0,
                topics=["Redux Basics", "Actions and Reducers", "Redux Middleware", 
                        "Redux Toolkit", "Redux with React Hooks"]
            )
        ]
    ),
    
    Course(
        title="Java Programming Masterclass",
        description="Learn Java programming from basics to advanced concepts, including OOP and multithreading.",
        category="Software Development",
        rating=4.6,
        prerequisites=["No programming experience required"],
        modules=[
            Module(
                name="Java Basics",
                duration=15.0,
                topics=["Setting Up Development Environment", "Variables and Data Types", 
                        "Control Flow Statements", "Methods and Functions", "Arrays and Lists"]
            ),
            Module(
                name="Object-Oriented Programming",
                duration=20.0,
                topics=["Classes and Objects", "Inheritance", "Polymorphism", 
                        "Encapsulation and Abstraction", "Interfaces and Abstract Classes"]
            ),
            Module(
                name="Advanced Java Concepts",
                duration=25.0,
                topics=["Collections Framework", "Generics", "Multithreading", 
                        "Lambda Expressions", "Stream API"]
            )
        ]
    ),
    
    Course(
        title="Agile Scrum Master Certification",
        description="Comprehensive Scrum Master training to prepare for PSM certification.",
        category="Project Management",
        rating=4.7,
        prerequisites=["Basic project management knowledge"],
        modules=[
            Module(
                name="Agile Fundamentals",
                duration=5.0,
                topics=["Agile Manifesto and Principles", "Agile vs. Traditional Methods", 
                        "Agile Frameworks Overview", "Agile Team Roles", "Agile Culture and Mindset"]
            ),
            Module(
                name="Scrum Framework",
                duration=10.0,
                topics=["Scrum Theory", "Scrum Roles", "Scrum Events", 
                        "Scrum Artifacts", "Definition of Done", "Scaled Scrum"]
            ),
            Module(
                name="Scrum Master Role",
                duration=8.0,
                topics=["Servant Leadership", "Facilitation Techniques", "Coaching and Mentoring", 
                        "Impediment Removal", "Metrics and Reporting"]
            )
        ]
    ),
    
    Course(
        title="Japanese for Beginners: The Complete Course",
        description="Learn Japanese from scratch, including hiragana, katakana, basic kanji, and everyday conversations.",
        category="Foreign Language",
        rating=4.8,
        prerequisites=["None"],
        modules=[
            Module(
                name="Japanese Writing Systems",
                duration=12.0,
                topics=["Hiragana Mastery", "Katakana Mastery", "Basic Kanji (N5 Level)"]
            ),
            Module(
                name="Basic Grammar and Vocabulary",
                duration=15.0,
                topics=["Sentence Structure", "Particles", "Verbs and Verb Conjugation", 
                        "Adjectives", "Basic Vocabulary"]
            ),
            Module(
                name="Practical Conversations",
                duration=18.0,
                topics=["Greetings and Introductions", "Shopping and Numbers", "Asking for Directions", 
                        "Restaurant and Food", "Travel Phrases", "Daily Life Conversations"]
            )
        ]
    ),
    
    Course(
        title="Full Stack Development with MERN",
        description="Build full stack web applications using MongoDB, Express, React, and Node.js.",
        category="Software Development",
        rating=4.7,
        prerequisites=["Basic knowledge of JavaScript"],
        modules=[
            Module(
                name="Backend Development with Node.js",
                duration=12.0,
                topics=["Node.js Fundamentals", "Express Framework", 
                        "MongoDB and Mongoose", "RESTful API Development"]
            ),
            Module(
                name="Frontend Development with React",
                duration=15.0,
                topics=["React Fundamentals", "React Router", "State Management with Redux", 
                        "React Hooks", "Component Libraries"]
            ),
            Module(
                name="Full Stack Integration",
                duration=13.0,
                topics=["Authentication and Authorization", "File Upload", 
                        "Real-time Features with Socket.io", "Deployment", "Performance Optimization"]
            )
        ]
    ),
    
    Course(
        title="Lean Six Sigma Green Belt Certification",
        description="Learn Lean Six Sigma methodologies to improve business processes and prepare for certification.",
        category="Project Management",
        rating=4.6,
        prerequisites=["Basic business process knowledge"],
        modules=[
            Module(
                name="Lean Six Sigma Foundations",
                duration=8.0,
                topics=["History and Evolution", "DMAIC Methodology", "Lean Principles", 
                        "Six Sigma Principles", "Roles and Responsibilities"]
            ),
            Module(
                name="Define and Measure Phases",
                duration=12.0,
                topics=["Project Charter", "Voice of Customer", "Process Mapping", 
                        "Data Collection Plan", "Measurement System Analysis"]
            ),
            Module(
                name="Analyze, Improve, Control Phases",
                duration=15.0,
                topics=["Root Cause Analysis", "Statistical Analysis", "Solution Generation", 
                        "Implementation Planning", "Control Plans and Sustainability"]
            )
        ]
    )
]
