"""
Learning Roadmap Agent - AI-powered system for generating personalized learning paths.

This module implements a dual-mode agent (Simple and Advisor) that helps users
create structured, actionable learning roadmaps based on their skill gaps and
time constraints.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union, cast

from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from pydantic import BaseModel, Field

from core import get_model
from schema.models import OpenAIModelName

from .roadmap_constants import (
    AgentMode, Skill, Course, LearningConstraints, LearningRoadmap, RoadmapState,
    DEFAULT_LEARNING_CONSTRAINTS, SYSTEM_PROMPT,
    CLARIFY_CONSTRAINTS_PROMPT
)
from .roadmap_tools import (
    extract_missing_skills_tool, extract_learning_constraints_tool,
    get_courses_for_skills_tool, generate_roadmap_tool, format_roadmap_tool
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LearningRoadmapAgent:
    """
    Learning Roadmap Agent that generates personalized learning paths.
    
    This agent analyzes a user's missing skills and learning constraints,
    retrieves relevant courses, and generates a comprehensive roadmap with
    both high-level phases and detailed plans.
    """

    def __init__(
            self,
            model_name: str = OpenAIModelName.GPT_41_MINI
    ):
        """
        Initialize the Learning Roadmap Agent.
        
        Args:
            model_name: Name of the language model to use
        """
        self.model_name = model_name
        self.llm = get_model(model_name)
        self.graph = self._create_roadmap_graph()

    def _create_roadmap_graph(self) -> StateGraph:
        """
        Create the LangGraph state graph for the roadmap agent.
        
        Returns:
            StateGraph: The compiled state graph
        """
        # Initialize the state graph
        graph_builder = StateGraph(RoadmapState)
          # Add nodes for different agent states
        graph_builder.add_node("process_input", self._process_input)
        graph_builder.add_node("clarify_constraints", self._clarify_constraints)
        graph_builder.add_node("generate_roadmap", self._generate_roadmap)
        
        # Add edges to define the flow
        graph_builder.add_edge(START, "process_input")
        graph_builder.add_conditional_edges(
            "process_input",
            self._route_after_process,
            {
                "needs_constraint_clarification": "clarify_constraints",
                "ready_for_roadmap": "generate_roadmap",
                "end": END
            }
        )
        graph_builder.add_edge("clarify_constraints", "generate_roadmap")
        graph_builder.add_edge("generate_roadmap", END)
        
        # Compile with memory
        memory = MemorySaver()
        return graph_builder.compile(checkpointer=memory)

    def _process_input(self, state: RoadmapState) -> RoadmapState:
        """
        Process the user's input to extract skills and learning constraints.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            messages = state["messages"]
            
            # Get the last user message
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_input = last_message.content
            else:
                user_input = str(last_message)
                
            logger.info(f"Processing input: {user_input}")
            
            # Extract skills from user input
            skills_result = extract_missing_skills_tool(self.llm, user_input)
            skills = [Skill(**skill) for skill in skills_result["skills"]]
            
            # Extract learning constraints from user input
            constraints_result = extract_learning_constraints_tool(self.llm, user_input)
            constraint_status = constraints_result["status"]
            constraint_data = LearningConstraints(**constraints_result["constraint_data"])
              # Update the state
            new_state = state.copy()
            new_state["missing_skills"] = skills
            new_state["learning_constraints"] = constraint_data
            
            # Set clarification flags based on mode and extracted data
            if state["mode"] == AgentMode.ADVISOR:
                # In advisor mode, we ask clarifying questions if needed
                new_state["needs_skill_clarification"] = False  # Skills clarification step removed
                new_state["needs_constraint_clarification"] = (constraint_status == "Incomplete")
            else:
                # In simple mode, we use defaults and never ask clarifying questions
                new_state["needs_skill_clarification"] = False
                new_state["needs_constraint_clarification"] = False
                
                # If we're in simple mode and have no skills, use a generic one
                if len(skills) == 0:
                    new_state["missing_skills"] = [
                        Skill(name="Programming", category="Software Development")
                    ]
            
            return new_state
            
        except Exception as e:
            logger.error(f"Error in process_input: {str(e)}")
            # Return a minimal state on error
            return {
                **state,
                "missing_skills": [Skill(name="Programming", category="Software Development")],
                "learning_constraints": DEFAULT_LEARNING_CONSTRAINTS,
                "needs_skill_clarification": False,
                "needs_constraint_clarification": False            }

    def _clarify_constraints(self, state: RoadmapState) -> RoadmapState:
        """
        Ask clarifying questions about learning constraints.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state with clarification message
        """
        try:
            messages = state["messages"]
            
            # Get the last user message for context
            last_message = None
            for message in reversed(messages):
                if isinstance(message, HumanMessage):
                    last_message = message
                    break
                    
            user_input = last_message.content if last_message else "your request"
            
            # Format the identified constraints
            constraints = state.get("learning_constraints", DEFAULT_LEARNING_CONSTRAINTS)
            identified_constraints = f"""
- Hours per day: {constraints.available_hours_per_day if constraints else 'Not specified'}
- Days per week: {constraints.available_days_per_week if constraints else 'Not specified'}
- Total weeks: {constraints.total_available_weeks if constraints else 'Not specified'}
            """
            
            # Create the clarification prompt
            prompt_template = CLARIFY_CONSTRAINTS_PROMPT
            clarification_message = prompt_template.format(
                user_input=user_input,
                identified_constraints=identified_constraints
            )
            
            # Add the clarification message to the state
            new_state = state.copy()
            new_state["messages"] = new_state["messages"] + [AIMessage(content=clarification_message)]
            
            return new_state
            
        except Exception as e:
            logger.error(f"Error in clarify_constraints: {str(e)}")
            # Return a basic clarification message on error
            new_state = state.copy()
            new_state["messages"] = new_state["messages"] + [
                AIMessage(content="Could you please specify your learning time constraints? How many hours per day, days per week, and total weeks can you dedicate to learning?")
            ]
            return new_state

    def _generate_roadmap(self, state: RoadmapState) -> RoadmapState:
        """
        Generate a learning roadmap based on the extracted information.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state with generated roadmap
        """
        try:
            # Get the skills and constraints from the state
            skills = state.get("missing_skills", [])
            constraints = state.get("learning_constraints", DEFAULT_LEARNING_CONSTRAINTS)
            
            # Get courses for the skills
            courses_result = get_courses_for_skills_tool(skills)
            courses = courses_result["courses"]
            
            # Generate the roadmap
            roadmap_result = generate_roadmap_tool(self.llm, skills, constraints, courses)
            roadmap = LearningRoadmap(**roadmap_result["roadmap"])
            
            # Format the roadmap for display
            format_result = format_roadmap_tool(self.llm, roadmap)
            formatted_roadmap = format_result["formatted_roadmap"]
            
            # Add the roadmap to the state
            new_state = state.copy()
            new_state["courses"] = courses
            new_state["roadmap"] = roadmap
            new_state["messages"] = new_state["messages"] + [AIMessage(content=formatted_roadmap)]
            
            return new_state
            
        except Exception as e:
            logger.error(f"Error in generate_roadmap: {str(e)}")
            # Return an error message on failure
            new_state = state.copy()
            new_state["messages"] = new_state["messages"] + [
                AIMessage(content="I'm sorry, but I encountered an error while generating your learning roadmap. Please try again with more specific information about the skills you want to learn and your time constraints.")
            ]
            return new_state    
    def _route_after_process(self, state: RoadmapState) -> str:
        """
        Determine the next node after processing input.
        
        Args:
            state: Current agent state
            
        Returns:
            Name of the next node
        """
        # Check if the last message was from the user
        messages = state["messages"]
        last_message = messages[-1] if messages else None
        
        # If the last message was from the AI, we're done (follow-up queries handled in continue_conversation)
        if isinstance(last_message, AIMessage):
            return "end"
            
        # Check if we need to clarify constraints
        if not state.get("needs_constraint_clarification", False):
            return "needs_constraint_clarification"
        
        return "ready_for_roadmap"
        # If we have all the information, generate the roadmap        return "ready_for_roadmap"

    def generate_roadmap(
        self, 
        user_input: str, 
        mode: AgentMode = AgentMode.ADVISOR,
        thread_id: str = "default"
    ) -> Dict[str, Any]:
        """
        Generate a learning roadmap based on user input.
        
        Args:
            user_input: User's request for a learning roadmap
            mode: Agent operation mode (SIMPLE or ADVISOR)
            thread_id: Thread ID for conversation memory
            
        Returns:
            Response containing messages and generated roadmap
        """
        try:
            logger.info(f"Generating roadmap in {mode} mode: {user_input}")
              # Create initial state
            initial_state = {
                "messages": [HumanMessage(content=user_input)],
                "mode": mode,
                "missing_skills": None,
                "learning_constraints": None,
                "courses": None,
                "roadmap": None,
                "needs_skill_clarification": False,  # Always false as this step is removed
                "needs_constraint_clarification": False
            }
            
            # Set up the config with thread ID
            config = {"configurable": {"thread_id": thread_id}}
            
            # Invoke the graph
            result = self.graph.invoke(initial_state, config=config)
            
            # Extract the roadmap and messages
            response = {
                "messages": result["messages"],
                "roadmap": result.get("roadmap", None)
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating roadmap: {str(e)}")
            # Return a basic response on error
            return {
                "messages": [
                    HumanMessage(content=user_input),
                    AIMessage(content=f"I'm sorry, but I encountered an error while generating your learning roadmap: {str(e)}")
                ],
                "roadmap": None
            }

    def continue_conversation(
        self, 
        user_input: str, 
        thread_id: str = "default"
    ) -> Dict[str, Any]:
        """
        Continue an existing conversation by processing a follow-up message.
        
        Args:
            user_input: User's follow-up message
            thread_id: Thread ID for conversation memory
            
        Returns:
            Response containing messages and updated roadmap if available
        """
        try:
            logger.info(f"Continuing conversation: {user_input}")
            
            # Set up the config with thread ID
            config = {"configurable": {"thread_id": thread_id}}
            
            # Get the current state from memory
            state = self.graph.get_state(thread_id)
            
            # If we don't have a state yet, start a new conversation
            if state is None:
                return self.generate_roadmap(user_input, AgentMode.ADVISOR, thread_id)
                
            # Add the new message to the state
            new_state = state.copy()
            new_state["messages"] = new_state["messages"] + [HumanMessage(content=user_input)]
            
            # Reset clarification flags for new input
            new_state["needs_skill_clarification"] = False
            new_state["needs_constraint_clarification"] = False
            
            # Invoke the graph with the updated state
            result = self.graph.invoke(new_state, config=config)
            
            # Extract the roadmap and messages
            response = {
                "messages": result["messages"],
                "roadmap": result.get("roadmap", None)
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error continuing conversation: {str(e)}")
            # Return a basic response on error
            return {
                "messages": [
                    HumanMessage(content=user_input),
                    AIMessage(content=f"I'm sorry, but I encountered an error while processing your message: {str(e)}")
                ],
                "roadmap": None
            }


# Create a LangGraph-compatible wrapper for integration with agents.py
def create_roadmap_agent_graph():
    """
    Create a LangGraph Pregel object for the Learning Roadmap Agent.

    This function creates a LangGraph-compatible agent that can be integrated
    into the main agent system in agents.py.

    Returns:
        Pregel: A compiled LangGraph agent
    """
    from typing import TypedDict, Annotated
    from langgraph.graph import StateGraph, add_messages, START, END
    from langgraph.checkpoint.memory import MemorySaver
    from langchain_core.messages import AIMessage

    class RoadmapAgentState(TypedDict):
        """State for Roadmap agent."""
        messages: Annotated[list, add_messages]

    # Initialize the Roadmap Agent instance
    roadmap_agent = LearningRoadmapAgent()

    def roadmap_agent_node(state: RoadmapAgentState) -> RoadmapAgentState:
        """
        Process user messages using the Roadmap Agent.

        Args:
            state: Current state containing messages

        Returns:
            Updated state with agent response
        """
        messages = state["messages"]
        if not messages:
            return {"messages": [
                AIMessage(content="Hello! I can help you create a personalized learning roadmap. What skills would you like to learn, and what are your time constraints?")]}

        # Get the last human message
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            user_query = last_message.content
        else:
            user_query = str(last_message)

        try:
            # Use the thread_id "default" since this state will be managed by the larger agent system
            response = roadmap_agent.continue_conversation(user_query, thread_id="default")
            # Extract the last message from the response
            if response and "messages" in response and response["messages"]:
                ai_message = response["messages"][-1]
                return {"messages": [AIMessage(content=ai_message.content)]}
            return {"messages": [AIMessage(content="I couldn't generate a proper response.")]}
        except Exception as e:
            logger.error(f"Error in roadmap agent node: {str(e)}")
            return {"messages": [AIMessage(content=f"Sorry, I encountered an error: {str(e)}")]}

    # Build the graph
    graph_builder = StateGraph(RoadmapAgentState)
    graph_builder.add_node("roadmap_agent", roadmap_agent_node)
    graph_builder.add_edge(START, "roadmap_agent")
    graph_builder.add_edge("roadmap_agent", END)

    # Compile with memory
    memory = MemorySaver()
    return graph_builder.compile(checkpointer=memory)


# Create the graph instance for export
roadmap_agent = create_roadmap_agent_graph()


def demo_roadmap_agent():
    """Run a demo of the roadmap agent."""
    agent = LearningRoadmapAgent()
    
    print("=== SIMPLE MODE DEMO ===")
    response = agent.generate_roadmap(
        "I want to become a full stack developer. I'm currently a front-end developer with 2 years of experience in React. I can spend about 2 hours per day, 5 days a week studying.",
        mode=AgentMode.SIMPLE
    )
    
    print("USER: I want to become a full stack developer. I'm currently a front-end developer with 2 years of experience in React. I can spend about 2 hours per day, 5 days a week studying.")
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"AGENT: {response['messages'][-1].content}")
    
    print("\n=== ADVISOR MODE DEMO ===")
    thread_id = "demo-advisor"
    response = agent.generate_roadmap(
        "I want to learn machine learning",
        mode=AgentMode.ADVISOR,
        thread_id=thread_id
    )
    
    print("USER: I want to learn machine learning")
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"AGENT: {response['messages'][-1].content}")
    
    # Continue the conversation
    response = agent.continue_conversation(
        "I can study 3 hours per day on weekends, and 1 hour on weekdays. I want to complete the learning in 3 months.",
        thread_id=thread_id
    )
    
    print("\nUSER: I can study 3 hours per day on weekends, and 1 hour on weekdays. I want to complete the learning in 3 months.")
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"AGENT: {response['messages'][-1].content}")


if __name__ == "__main__":
    demo_roadmap_agent()
