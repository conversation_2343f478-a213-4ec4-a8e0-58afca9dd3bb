"""
Job Description RAG System Implementation

This module implements a comprehensive Retrieval Augmented Generation (RAG) system
for job descriptions using LangChain and PGVector. It processes job description files,
stores them in a vector database, and provides semantic search capabilities.

Features:
- Document loading and text splitting for job description files
- Embedding generation and storage in PGVector
- Custom retriever implementation for similarity search
- RAG chain setup combining retrieval and generation
- Comprehensive error handling and logging

Author: AI Assistant
Date: 2024
"""

import json
import logging
import os
import traceback
from typing import List, Optional

import openai
import psycopg2
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from langchain_core.prompts import ChatPromptTemplate

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI

from core.settings import settings
from core import get_model
from agents.job_description_retriever import JobDescriptionRetriever
from schema.models import OpenAIModelName

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
EMBEDDING_MODEL = "text-embedding-3-small"
VECTOR_DIM = 1536
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200


class JobDescriptionRAG:
    """
    Main RAG system class for job descriptions.
    
    This class handles the complete RAG pipeline from document processing
    to query answering, following the LangChain RAG tutorial structure.
    """
    
    def __init__(self, db_url: Optional[str] = None):
        """
        Initialize the Job Description RAG system.
        
        Args:
            db_url: PostgreSQL connection URL. If None, uses settings.get_postgresql_url()
        """
        self.db_url = db_url or settings.get_postgresql_url()
        self.openai_client = None
        self.retriever = None
        self.rag_chain = None
        
        # Initialize components
        self._initialize_openai_client()
        self._initialize_retriever()
        self._setup_rag_chain()
    
    def _initialize_openai_client(self) -> None:
        """
        Initialize OpenAI client for embeddings.
        Follows error handling patterns from existing codebase.
        """
        try:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            
            self.openai_client = openai.OpenAI(api_key=api_key)
            logger.info("OpenAI client initialized successfully")
            
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Failed to initialize OpenAI client: {e}\n{error_details}")
            raise
    
    def _initialize_retriever(self) -> None:
        """
        Initialize the job description retriever.
        """
        try:
            self.retriever = JobDescriptionRetriever(
                db_url=self.db_url,
                k=5,
                similarity_threshold=0.1  # Lower threshold to get more results
            )
            logger.info("Job description retriever initialized successfully")
            
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Failed to initialize retriever: {e}\n{error_details}")
            raise
    
    def _setup_rag_chain(self) -> None:
        """
        Set up the RAG chain combining retrieval and generation components.
        Follows the official LangChain RAG tutorial structure.
        """
        try:
            try:
                llm = get_model(OpenAIModelName.GPT_4O_MINI)
            except Exception as e:
                logger.warning(f"Failed to initialize gpt-4o-mini, falling back to ChatOpenAI: {e}")
                llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

            # Create the prompt template
            prompt = ChatPromptTemplate.from_template("""
            You are an expert HR assistant specializing in job description analysis and candidate matching.
            
            Use the following job description context to answer the user's question:
            
            Context:
            {context}
            
            Question: {question}
            
            Provide a comprehensive and accurate answer based on the job description information provided.
            If the context doesn't contain enough information to fully answer the question, 
            clearly state what information is missing.
            
            Answer:
            """)
            
            # Format documents function
            def format_docs(docs):
                """Format retrieved documents for the prompt context."""
                formatted_docs = []
                for doc in docs:
                    metadata = doc.metadata
                    job_info = f"Job: {metadata.get('job_title', 'Unknown')} - {metadata.get('job_level', 'Unknown')}"
                    section_info = f"Section: {metadata.get('section_type', 'Unknown')}"
                    content = f"{job_info}\n{section_info}\nContent: {doc.page_content}"
                    formatted_docs.append(content)
                return "\n\n".join(formatted_docs)

            # Store format_docs function for later use
            self.format_docs = format_docs

            # Create the basic RAG chain (will be customized in query method)
            self.rag_chain = (
                prompt
                | llm
                | StrOutputParser()
            )
            
            logger.info("RAG chain setup completed successfully")
            
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Failed to setup RAG chain: {e}\n{error_details}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding vector for the given text using OpenAI API.
        
        Args:
            text: Text to embed
            
        Returns:
            List of float values representing the embedding vector
        """
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model=EMBEDDING_MODEL
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise
    
    def load_and_process_documents(self, file_paths: List[str]) -> List[Document]:
        """
        Load and process job description documents from files.
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            List of processed Document objects
        """
        documents = []
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE,
            chunk_overlap=CHUNK_OVERLAP,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )
        
        for file_path in file_paths:
            try:
                logger.info(f"Processing file: {file_path}")
                
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # Parse JSON content
                job_data = json.loads(content)
                
                # Extract filename for metadata
                filename = os.path.basename(file_path)
                
                # Process each job role in the file
                for job_title, job_info in job_data.items():
                    # Process each section of the job description
                    for section_name, section_content in job_info.items():
                        if isinstance(section_content, list):
                            # Handle list sections (like skills, experiences)
                            for item in section_content:
                                if isinstance(item, dict):
                                    # Create content text from item
                                    content_parts = []
                                    if 'item' in item:
                                        content_parts.append(f"Item: {item['item']}")
                                    if 'explanation' in item:
                                        content_parts.append(f"Explanation: {item['explanation']}")
                                    if 'value' in item:
                                        content_parts.append(f"Value: {item['value']}")
                                    
                                    content_text = "\n".join(content_parts)
                                    
                                    # Split content if too long
                                    chunks = text_splitter.split_text(content_text)
                                    
                                    for chunk in chunks:
                                        doc = Document(
                                            page_content=chunk,
                                            metadata={
                                                'job_title': job_title,
                                                'section_type': section_name,
                                                'section_name': item.get('item', ''),
                                                'source_file': filename,
                                                'job_level': self._extract_job_level(job_title)
                                            }
                                        )
                                        documents.append(doc)
                
                logger.info(f"Successfully processed {file_path}")
                
            except Exception as e:
                error_details = traceback.format_exc()
                logger.error(f"Error processing file {file_path}: {e}\n{error_details}")
                continue
        
        logger.info(f"Total documents processed: {len(documents)}")
        return documents
    
    def _extract_job_level(self, job_title: str) -> str:
        """
        Extract job level from job title.
        
        Args:
            job_title: Job title string
            
        Returns:
            Extracted job level
        """
        job_title_lower = job_title.lower()
        
        if 'associate' in job_title_lower:
            return 'Associate'
        elif 'senior' in job_title_lower:
            return 'Senior'
        elif 'lead' in job_title_lower or 'tech lead' in job_title_lower:
            return 'Lead'
        elif 'architect' in job_title_lower:
            return 'Architect'
        else:
            return 'Standard'
    
    def store_documents(self, documents: List[Document]) -> bool:
        """
        Store processed documents in the PGVector database.
        
        Args:
            documents: List of Document objects to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Storing {len(documents)} documents in database...")
            
            with psycopg2.connect(self.db_url, connect_timeout=30) as conn:
                with conn.cursor() as cursor:
                    for doc in documents:
                        try:
                            # Generate embedding for the document
                            embedding = self.get_embedding(doc.page_content)
                            
                            # Insert document into database
                            insert_sql = """
                            INSERT INTO job_descriptions 
                            (job_title, job_level, section_type, section_name, content, embedding, metadata)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """
                            
                            cursor.execute(insert_sql, (
                                doc.metadata.get('job_title', ''),
                                doc.metadata.get('job_level', ''),
                                doc.metadata.get('section_type', ''),
                                doc.metadata.get('section_name', ''),
                                doc.page_content,
                                embedding,
                                json.dumps(doc.metadata)
                            ))
                            
                        except Exception as e:
                            logger.error(f"Error storing document: {e}")
                            continue
                    
                    conn.commit()
            
            logger.info("Documents stored successfully in database")
            return True
            
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Failed to store documents: {e}\n{error_details}")
            return False
    
    def query(
        self,
        question: str,
        job_title: Optional[str] = None,
        job_level: Optional[str] = None,
        section_type: Optional[str] = None
    ) -> str:
        """
        Query the RAG system with a question and optional filters.

        Args:
            question: Question to ask about job descriptions
            job_title: Optional filter by job title (e.g., "Senior Software Engineer")
            job_level: Optional filter by job level (e.g., "Senior", "Associate")
            section_type: Optional filter by section type (e.g., "Skills", "Responsibilities")

        Returns:
            Generated answer based on retrieved context
        """
        try:
            if not self.rag_chain:
                raise ValueError("RAG chain not initialized")

            logger.info(f"Processing query: {question[:100]}...")
            if job_title:
                logger.info(f"Filtering by job_title: {job_title}")
            if job_level:
                logger.info(f"Filtering by job_level: {job_level}")
            if section_type:
                logger.info(f"Filtering by section_type: {section_type}")

            # Retrieve relevant documents with filters
            retrieved_docs = self.retriever.get_relevant_documents(
                question,
                job_title=job_title,
                job_level=job_level,
                section_type=section_type
            )

            # Format the retrieved documents
            context = self.format_docs(retrieved_docs)

            # Create input for the RAG chain
            chain_input = {
                "context": context,
                "question": question
            }

            # Generate answer using the RAG chain
            answer = self.rag_chain.invoke(chain_input)
            logger.info("Query processed successfully")

            return answer

        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Error processing query: {e}\n{error_details}")
            return f"Error processing query: {str(e)}"

    def query_by_job_title(self, question: str, job_title: str) -> str:
        """
        Convenience method to query with job title filter.

        Args:
            question: Question to ask
            job_title: Job title to filter by (e.g., "Senior Software Engineer")

        Returns:
            Generated answer filtered by job title
        """
        return self.query(question, job_title=job_title)

    def query_by_job_level(self, question: str, job_level: str) -> str:
        """
        Convenience method to query with job level filter.

        Args:
            question: Question to ask
            job_level: Job level to filter by (e.g., "Senior", "Associate")

        Returns:
            Generated answer filtered by job level
        """
        return self.query(question, job_level=job_level)

    def get_job_titles_in_database(self) -> List[str]:
        """
        Get all unique job titles stored in the database.

        Returns:
            List of unique job titles
        """
        try:
            with psycopg2.connect(self.db_url, connect_timeout=10) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT DISTINCT job_title FROM job_descriptions ORDER BY job_title")
                    results = cursor.fetchall()
                    return [row[0] for row in results]
        except Exception as e:
            logger.error(f"Error getting job titles: {e}")
            return []

def demo_rag_system():
    rag_system = JobDescriptionRAG()

    query = "What skills are needed for a Solution Architect role?"

    logger.info(f"\nQuery: {query}")
    answer = rag_system.query(query)
    logger.info(f"Answer: {answer[:200]}...")

def embedding_data():
    """
    Main function to demonstrate the RAG system functionality.
    """
    try:
        logger.info("Initializing Job Description RAG System...")
        
        # Initialize RAG system
        rag_system = JobDescriptionRAG()
        
        # Define job description file paths
        job_description_files = [
            "docs/job_descriptions/developer.txt",
            "docs/job_descriptions/sa.txt"
        ]
        
        # Check if files exist
        existing_files = [f for f in job_description_files if os.path.exists(f)]
        if not existing_files:
            logger.error("No job description files found!")
            return
        
        logger.info(f"Found {len(existing_files)} job description files")
        
        # Load and process documents
        documents = rag_system.load_and_process_documents(existing_files)
        
        if not documents:
            logger.error("No documents were processed!")
            return
        
        # Store documents in database
        success = rag_system.store_documents(documents)
        
        if not success:
            logger.error("Failed to store documents in database!")
            return
        
        # Test the RAG system with sample queries
        test_queries = [
            "What are the requirements for a Senior Software Engineer?",
            "What skills are needed for a Solution Architect role?",
            "What is the difference between Associate and Senior level positions?"
        ]
        
        logger.info("Testing RAG system with sample queries...")
        
        for query in test_queries:
            logger.info(f"\nQuery: {query}")
            answer = rag_system.query(query)
            logger.info(f"Answer: {answer[:200]}...")
        
        logger.info("Job Description RAG System demonstration completed successfully!")
        
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Error in main function: {e}\n{error_details}")


if __name__ == "__main__":
    demo_rag_system()
