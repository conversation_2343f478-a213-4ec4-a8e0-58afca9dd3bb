"""
Learning Supervisor Agent System

A multi-agent workflow that helps users create personalized learning paths by:
1. Analyzing their current state (resume/CV analysis)
2. Defining goals and identifying gaps (WHAT needs to be achieved)
3. Generating actionable roadmaps (HOW to achieve goals)

The supervisor agent routes user queries to specialized sub-agents based on context.
"""

import os
import sys
import uuid
import json
from textwrap import dedent

from agents.prompt_lib import (
    SUPERVISOR_PROMPT,
    LEARNING_ROADMAP_PROMPT,
    GOAL_ANALYSIS_PROMPT,
    RESUME_ANALYSIS_PROMPT,
)

path = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src"))
print(f"path: {path}")
sys.path.append(path)
from typing import TypedDict, Annotated

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.prebuilt import create_react_agent

from agents.define_goal_service import process_goal_definition
from agents.resume_rag_agent import ResumeRAGAgent
from langgraph.store.memory import InMemoryStore
from agents.roadmap_agent.roadmap_agent import LearningRoadmapAgent
from agents.resume_retriever import search_goal_information
from agents.prompt_lib import GOAL_DESCRIPTION_INSTRUCTIONS, GOAL_DESCRIPTION_OUTPUT_MD
from langchain_core.prompts import PromptTemplate
from agents.job_description_retriever import JobDescriptionRetriever

resume_agent = ResumeRAGAgent()
roadmap_ai = LearningRoadmapAgent()
model = ChatOpenAI(model="gpt-4o")


class ConversationState(TypedDict, total=False):
    """
    State management for the learning supervisor workflow.

    Attributes:
        messages: Conversation history with automatic message merging
        next: Routing key for conditional edges between agent nodes
        resume_agent_called: Flag to track if resume agent has been invoked
    """

    messages: Annotated[list[AIMessage | HumanMessage], add_messages]
    next: str | None  # For routing between nodes
    resume_agent_called: bool  # Track if resume agent has been called


def internet_search_tool(query: str):
    """
    Search the internet for learning roadmaps and career guidance context.

    Provides additional context and information when internal knowledge
    is insufficient. Returns search results for the roadmap agent to use.

    Args:
        query: Search query for learning roadmaps or career paths

    Returns:
        String containing search results and context information
    """
    try:
        # Use web search for current learning resources and roadmaps
        from langchain_community.tools import DuckDuckGoSearchRun

        search = DuckDuckGoSearchRun()
        search_results = search.run(f"learning roadmap career path {query} 2024")

        return f"""Internet Search Results for "{query}":

{search_results}

This information provides current industry trends, popular learning paths, and up-to-date resources for {query}."""

    except Exception as e:
        return f"Internet search unavailable. Proceeding with available knowledge for {query}."


def resume_rag_tool(query: str):
    """
    Resume analysis tool for extracting current skills and experience.

    Processes queries about someone's CV/resume using RAG to find relevant
    information about their current capabilities, experience, and background.
    This helps establish the "current state" for learning path planning.

    Args:
        query: User query about resume/CV content

    Returns:
        AIMessage containing relevant resume information or request for more info
    """
    try:
        response = resume_agent.query_agent(query)

        # Check if no meaningful CV information was found
        if (
            not response
            or "no information" in response.lower()
            or "not found" in response.lower()
            or len(response.strip()) < 50
        ):
            return AIMessage(
                content="""I don't have enough information about your background and experience. To provide you with a personalized learning path, could you please share:

1. Your current role or field of work
2. Your educational background
3. Key skills you already have
4. Years of experience in your field
5. Any specific technologies or tools you've worked with

This will help me create a more targeted learning roadmap for your goals."""
            )

        return AIMessage(content=response)
    except Exception as e:
        return AIMessage(
            content="""I encountered an issue accessing your resume information. To help you better, could you please provide some details about:

1. Your current professional background
2. Your key skills and experience
3. What technologies or tools you're familiar with
4. Your educational background

This will allow me to create a personalized learning path for you."""
        )


def roadmap_tool(query: str):
    """
    Learning roadmap generation tool with internet search fallback.

    Creates structured learning paths based on goals and current state.
    Uses internet search for additional context when needed.

    Args:
        query: Context about learning goals and requirements

    Returns:
        AIMessage containing structured learning roadmap
    """
    try:
        # First attempt with internal roadmap generation
        result = roadmap_ai.generate_roadmap(query)

        # Handle different return types from roadmap_ai
        if isinstance(result, dict):
            # Extract content from dictionary (common keys: 'content', 'roadmap', 'result')
            result = (
                result.get("content")
                or result.get("roadmap")
                or result.get("result")
                or str(result)
            )
        elif result is None:
            result = ""
        else:
            result = str(result)

        # Check if result is None, empty, or unclear
        if (
            not result
            or result.strip() == ""
            or "sorry" in result.lower()
            or "error" in result.lower()
        ):
            # Get internet search context and regenerate
            print(f"Roadmap AI returned unclear result: {result}. Using internet search context.")
            internet_context = internet_search_tool(query)
            enhanced_query = f"{query}\n\nAdditional Context:\n{internet_context}"
            # courses_context = COURSES_LIST
            enhanced_result = roadmap_ai.generate_roadmap(enhanced_query)

            # Handle enhanced result type
            if isinstance(enhanced_result, dict):
                enhanced_result = (
                    enhanced_result.get("content")
                    or enhanced_result.get("roadmap")
                    or enhanced_result.get("result")
                    or str(enhanced_result)
                )
            elif enhanced_result is None:
                enhanced_result = ""
            else:
                enhanced_result = str(enhanced_result)

            # If still no good result, return internet-enhanced response
            if not enhanced_result or enhanced_result.strip() == "":
                return AIMessage(
                    content=f"Based on internet research for {query}:\n\n{internet_context}\n\nPlease review the above information to create your learning path."
                )
            else:
                result = enhanced_result

        # Check if the roadmap lacks detail (too short or generic)
        elif len(result.strip()) < 200:
            # Enhance with internet search context
            print(
                f"Roadmap result too short ({len(result.strip())} chars). Enhancing with internet search."
            )
            internet_context = internet_search_tool(query)
            enhanced_query = f"{query}\n\nCurrent roadmap: {result}\n\nAdditional Context from Internet:\n{internet_context}"
            enhanced_result = roadmap_ai.generate_roadmap(enhanced_query)

            # Handle enhanced result type
            if isinstance(enhanced_result, dict):
                enhanced_result = (
                    enhanced_result.get("content")
                    or enhanced_result.get("roadmap")
                    or enhanced_result.get("result")
                    or str(enhanced_result)
                )
            elif enhanced_result is None:
                enhanced_result = ""
            else:
                enhanced_result = str(enhanced_result)

            if enhanced_result and len(enhanced_result.strip()) > len(result.strip()):
                result = enhanced_result
            else:
                result = f"{result}\n\n**Additional Context from Internet Research:**\n{internet_context}"

        return AIMessage(content=result)

    except Exception as e:
        print(f"Error in roadmap generation: {e}. Using internet search context.")
        # Fallback to internet search context
        internet_context = internet_search_tool(query)
        return AIMessage(
            content=f"I encountered an issue generating your roadmap. Here's current information from internet research:\n\n{internet_context}\n\nBased on this information, consider starting with fundamental concepts and progressing through intermediate to advanced topics."
        )


# Sub-agent definitions with specialized prompts
resume_rag_agent = create_react_agent(
    model=model, tools=[resume_rag_tool], name="resume_rag_agent", prompt=RESUME_ANALYSIS_PROMPT
)

goal_agent = create_react_agent(
    model=model,
    tools=[],
    name="goal_agent",
    prompt=dedent(GOAL_ANALYSIS_PROMPT),
)

roadmap_agent = create_react_agent(
    model=model,
    tools=[roadmap_tool, internet_search_tool],
    name="roadmap_agent",
    prompt=dedent(LEARNING_ROADMAP_PROMPT)
    + "\n\nIf you cannot find specific information or if the roadmap is unclear, use the internet_search_tool to find current industry-standard learning paths and roadmaps.",
)


def supervisor_agent_node(state: ConversationState) -> dict:
    """
    Intelligent routing supervisor that directs queries to appropriate sub-agents.

    Uses LLM-based analysis to determine which specialized agent should handle
    the user's request based on conversation context and intent.

    Routing logic:
    - resume_rag_agent: For queries about someone's CV, skills, or background
    - goal_agent: For goal definition and gap analysis (WHAT to achieve)
    - roadmap_agent: For learning path creation (HOW to achieve goals)

    Args:
        state: Current conversation state with message history

    Returns:
        Dict with 'next' key indicating which agent node to route to
    """
    router_prompt = dedent(SUPERVISOR_PROMPT)
    
    # Get only the last human message from the conversation
    human_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    last_human_message = human_messages[-1] if human_messages else None
    
    # Build messages with only SystemMessage and last HumanMessage
    messages = [SystemMessage(content=router_prompt)]
    if last_human_message:
        messages.append(last_human_message)
    
    router_response = model.invoke(messages)
    agent_name = router_response.content.strip().lower()
    
    if "goal_agent" in agent_name:
        return {"next": "goal_agent"}
    if "roadmap_agent" in agent_name:
        return {"next": "roadmap_agent"}
    return {"next": "resume_rag_agent"}


def resume_rag_agent_node(state: ConversationState) -> ConversationState:
    """
    Resume analysis node that processes CV/background queries.

    Analyzes user queries about resumes, current skills, and professional
    background to establish the "current state" for learning planning.

    Args:
        state: Current conversation state

    Returns:
        Updated state with resume analysis response
    """
    result = resume_rag_agent.invoke(state)
    return {"messages": result["messages"][-1]}


def goal_flow_node(state: ConversationState) -> ConversationState:
    """
    Goal agent node.
    
    Checks if the learning goal is already declared in the database.
    If yes, uses additional information about the goal/job for enhanced analysis.
    If not, searches the internet for information about the goal.
    Then calls goal_agent with enhanced context.

    Args:
        state: Current conversation state

    Returns:
        Updated state with enhanced goal analysis
    """
    # Extract the goal/career target from the latest user message
    latest_message = state["messages"][-1] if state["messages"] else None
    user_query = (
        latest_message.content if latest_message and hasattr(latest_message, "content") else ""
    )

    # Search for goal information in database using the new function
    try:
        # Initialize job description retriever
        job_retriever = JobDescriptionRetriever(k=3, similarity_threshold=0.3)
        
        # Search for relevant job descriptions based on user query
        job_documents = job_retriever.get_relevant_documents(
            query=user_query,
            job_title=None,  # Let it search broadly first
            job_level=None,
            section_type=None
        )
        
        if job_documents:
            # Format the retrieved job description content
            job_content = []
            for doc in job_documents:
                metadata = doc.metadata
                content_summary = f"**{metadata.get('job_title', 'Unknown Role')}** ({metadata.get('job_level', 'Unknown Level')})\n"
                content_summary += f"*{metadata.get('section_type', 'General')}*: {doc.page_content[:200]}..."
                job_content.append(content_summary)
            
            goal_search_result = {
                "found": True,
                "content": "\n\n".join(job_content),
                "sources": [doc.metadata.get('job_title', 'Unknown') for doc in job_documents],
                "total_results": len(job_documents)
            }
        else:
            goal_search_result = {"found": False, "content": "", "sources": [], "total_results": 0}
            
    except Exception as e:
        print(f"Error retrieving job descriptions: {e}")
        goal_search_result = {"found": False, "content": "", "sources": [], "total_results": 0}

    # TODO: Remove this once the search_goal_information function is working
    # goal_search_result = {"found": False, "content": "", "sources": [], "total_results": 0}
    additional_info = ""

    # Check if we found goal information in database
    if goal_search_result["found"] and goal_search_result["content"]:
        additional_info = f"""Database Information about the goal:
{goal_search_result["content"]}

Sources found: {goal_search_result["total_results"]} relevant documents from database.

"""
    else:
        # Search internet for goal information
        try:
            internet_info = internet_search_tool(
                f"career requirements skills learning path {user_query}"
            )
            additional_info = f"Internet Research about the goal:\n{internet_info}\n\n"
        except Exception as e:
            additional_info = "No additional goal information available.\n\n"

    # Create enhanced state with additional goal information
    enhanced_query = f"{user_query}\n\nAdditional Context:\n{additional_info}"
    prompt_template = PromptTemplate.from_template(template=GOAL_DESCRIPTION_INSTRUCTIONS)
    # Only pass output_json and input_context, with output_json always valid JSON
    # output_json = GOAL_DESCRIPTION_OUTPUT_JSON.replace("Role", "unknown")
    formatted_prompt = prompt_template.format(
        output_md=GOAL_DESCRIPTION_OUTPUT_MD,
        input_context=enhanced_query,
    )
    enhanced_message = HumanMessage(content=formatted_prompt)
    
    # Add enhanced context to state
    enhanced_state = state.copy()
    enhanced_state["messages"] = state["messages"] + [enhanced_message]

    # Now call goal_agent with enhanced context
    result = goal_agent.invoke(enhanced_state)
    return {"messages": result["messages"][-1], "next": "goal_radar_chart"}


def goal_radar_chart_node(state: ConversationState) -> ConversationState:
    """
    Goal radar chart generation node.
    
    Creates a radar chart JSON data structure based on the last AI message and conversation context.
    Evaluates user skills across 6 critical categories and returns recharts-compatible JSON format.

    Args:
        state: Current conversation state

    Returns:
        Updated state with radar chart JSON data for recharts
    """
    # Get the last AI message which should contain the goal analysis
    last_ai_message = None
    for message in reversed(state["messages"]):
        if isinstance(message, AIMessage):
            last_ai_message = message
            break
    
    if not last_ai_message:
        # Return default chart data if no analysis found
        default_chart_data = [
            {"skill": "Roles & Responsibilities", "level": 0, "fullMark": 60},
            {"skill": "Experiences & Contributions", "level": 0, "fullMark": 70},
            {"skill": "Solution Architecture Skills", "level": 0, "fullMark": 75},
            {"skill": "Foreign Language & Certificates", "level": 0, "fullMark": 50},
            {"skill": "Non-Engineering and Softskills", "level": 0, "fullMark": 80},
            {"skill": "Application Software Engineering Skills", "level": 0, "fullMark": 85}
        ]
        chart_content = f"Based on your knowledge the gap chart is created for you\n\n{json.dumps(default_chart_data, indent=2)}\n\nFinish the gap chart"
        return {"messages": AIMessage(content=chart_content)}
    
    # Collect all conversation context for comprehensive analysis
    conversation_context = "\n".join([
        f"Message: {msg.content}" for msg in state["messages"] 
        if hasattr(msg, 'content') and msg.content
    ])
    
    # System prompt for radar chart JSON generation
    radar_chart_prompt = """You are a skill assessment specialist that evaluates user capabilities and target requirements based on conversation context and goal analysis.

Analyze the provided conversation context and goal analysis to assess the user's current skill levels AND target requirements across these 6 critical categories:

1. **Roles & Responsibilities** - Leadership, management, project oversight, team coordination
2. **Experiences & Contributions** - Work history, achievements, project contributions, career progression
3. **Solution Architecture Skills** - System design, technical architecture, scalability planning
4. **Foreign Language & Certificates** - Language proficiency, certifications, formal qualifications
5. **Non-Engineering and Softskills** - Communication, teamwork, problem-solving, adaptability
6. **Application Software Engineering Skills** - Programming languages, frameworks, development tools

For each category, determine:
- **Current Level (level)**: User's current proficiency (0-100)
- **Target Level (fullMark)**: Required proficiency for their goal (0-100)

Scoring guidelines:
- 0-20: No evidence/beginner
- 21-40: Basic/Limited
- 41-60: Intermediate
- 61-80: Advanced/Experienced
- 81-100: Expert/Senior

The target level (fullMark) should be based on:
- The user's stated career goals
- Industry requirements for their target role
- Level of responsibility they aim to achieve
- Complexity of their target work environment

Return ONLY a valid JSON array in this exact format (no additional text):
[
  {"skill": "Roles & Responsibilities", "level": [current_score], "fullMark": [target_score]},
  {"skill": "Experiences & Contributions", "level": [current_score], "fullMark": [target_score]},
  {"skill": "Solution Architecture Skills", "level": [current_score], "fullMark": [target_score]},
  {"skill": "Foreign Language & Certificates", "level": [current_score], "fullMark": [target_score]},
  {"skill": "Non-Engineering and Softskills", "level": [current_score], "fullMark": [target_score]},
  {"skill": "Application Software Engineering Skills", "level": [current_score], "fullMark": [target_score]}
]

Base scores on evidence from conversation. If target goal information is limited, estimate reasonable targets for a professional growth path."""

    # Create messages for radar chart generation
    chart_messages = [
        SystemMessage(content=radar_chart_prompt),
        HumanMessage(content=f"Conversation Context:\n{conversation_context}\n\nLatest Goal Analysis:\n{last_ai_message.content}\n\nPlease assess the user's skill levels and return the JSON chart data.")
    ]
    
    # Generate radar chart JSON response
    chart_response = model.invoke(chart_messages)
    
    # Extract and validate JSON from response
    try:
        # Try to parse the response as JSON directly
        response_content = chart_response.content.strip()
        
        # Remove any markdown code blocks if present
        if response_content.startswith("```"):
            lines = response_content.split("\n")
            response_content = "\n".join([line for line in lines if not line.startswith("```")])
        
        # Parse JSON
        chart_data = json.loads(response_content)
        
        # Validate the structure
        if isinstance(chart_data, list) and len(chart_data) == 6:
            # Ensure all required fields are present
            for item in chart_data:
                if not all(key in item for key in ["skill", "level", "fullMark"]):
                    raise ValueError("Invalid chart data structure")
            
            # Return validated JSON with opening and ending messages
            chart_content = f"\n ```radar_chart \n\n{json.dumps(chart_data, indent=2)}\n\n ```"
            return {"messages": AIMessage(content=chart_content)}
        else:
            raise ValueError("Invalid chart data format")
            
    except (json.JSONDecodeError, ValueError) as e:
        # Fallback to default data if parsing fails
        default_chart_data = [
            {"skill": "Roles & Responsibilities", "level": 40, "fullMark": 70},
            {"skill": "Experiences & Contributions", "level": 45, "fullMark": 75},
            {"skill": "Solution Architecture Skills", "level": 25, "fullMark": 80},
            {"skill": "Foreign Language & Certificates", "level": 30, "fullMark": 55},
            {"skill": "Non-Engineering and Softskills", "level": 55, "fullMark": 85},
            {"skill": "Application Software Engineering Skills", "level": 50, "fullMark": 90}
        ]
        chart_content = f"\n ```radar_chart \n\n{json.dumps(default_chart_data, indent=2)}\n\n```"
        return {"messages": AIMessage(content=chart_content)}


def goal_router_node(state: ConversationState) -> ConversationState:
    """
    Goal routing node.
    
    Analyzes the conversation state to determine routing for goal-related requests.
    
    Uses the model with a system prompt to analyze the entire conversation state
    to determine if user information is available. Routes accordingly:
    - If no user info exists, routes to resume_rag_agent
    - If user info is available, routes to goal_flow

    Args:
        state: Current conversation state

    Returns:
        Updated state with routing decision based on user information availability
    """
    # System prompt for analyzing conversation state for user information
    system_prompt = """You are a routing agent that analyzes conversation history to determine if any user information is available for goal analysis.

Your task is to examine the entire conversation state and determine if there is ANY information about the user, even if brief or minimal.

Look for ANY mention of:
- Current skills, technical abilities, or tools they know
- Professional experience, job role, or industry
- Educational background or qualifications
- Domain knowledge or expertise
- Career history, current position, or work experience
- Personal projects or accomplishments
- Learning background or certifications

Analyze ALL messages in the conversation history, not just the most recent one.

IMPORTANT: Be LENIENT with your assessment. Even short or brief information about the user should be considered sufficient.

Examples of sufficient information (route to goal_flow):
- "I'm a software engineer"
- "I know Python"
- "I work in marketing"
- "I have 2 years experience"
- "I studied computer science"
- "I'm a beginner in programming"
- "I currently work as a data analyst"

Based on your analysis, respond with EXACTLY ONE of these options:
- "resume_rag_agent" - ONLY if there is absolutely NO user information available at all
- "goal_flow" - if there is ANY user information provided, even if brief or minimal

Be generous in your assessment. If the user has provided even minimal information about their background, skills, experience, or role, route to goal_flow.

Respond with only the routing decision, no additional explanation."""

    # Create messages for model analysis
    analysis_messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content="Analyze the following conversation history and determine if sufficient user information is available for goal analysis:")
    ]
    
    # Add all conversation messages for analysis
    analysis_messages.extend(state["messages"])
    
    # Get routing decision from model
    routing_response = model.invoke(analysis_messages)
    routing_decision = routing_response.content.strip().lower()
    
    # Validate and set routing decision
    if "resume_rag_agent" in routing_decision:
        return {"next": "resume_rag_agent"}
    else:
        return {"next": "goal_flow"}


def roadmap_router_node(state: ConversationState) -> ConversationState:
    """
    Roadmap routing node.
    
    Analyzes the conversation state to determine if gap analysis has been completed.
    Routes to goal_router_node if gap analysis is missing, otherwise proceeds to roadmap_agent.
    
    Uses the model to analyze conversation history for evidence of:
    - Goal definition and analysis
    - Skill gap identification
    - Career target specification
    
    Args:
        state: Current conversation state

    Returns:
        Updated state with routing decision based on gap analysis availability
    """
    # System prompt for analyzing conversation state for gap analysis completion
    system_prompt = """You are a routing agent that analyzes conversation history to determine if goal definition and gap analysis have been completed.

Your task is to examine the entire conversation state and determine if there is sufficient goal and gap analysis information for roadmap generation.

Look for evidence of:
- Clear goal or career target definition
- Skill gap analysis or identification of missing skills
- Comparison between current skills and target requirements
- Any previous goal analysis or gap assessment output

Analyze ALL messages in the conversation history, not just the most recent one.

Based on your analysis, respond with EXACTLY ONE of these options:
- "goal_router_node" - if goal definition or gap analysis is missing or incomplete
- "roadmap_agent" - if sufficient goal and gap analysis information is available for roadmap generation

Be thorough in your assessment. If there's any doubt about whether sufficient analysis has been completed, route to goal_router_node to ensure proper foundation for roadmap creation.

Respond with only the routing decision, no additional explanation."""

    # Create messages for model analysis
    analysis_messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content="Analyze the following conversation history and determine if sufficient goal definition and gap analysis has been completed for roadmap generation:")
    ]
    
    # Add all conversation messages for analysis
    analysis_messages.extend(state["messages"])
    
    # Get routing decision from model
    routing_response = model.invoke(analysis_messages)
    routing_decision = routing_response.content.strip().lower()
    
    # Validate and set routing decision
    if "goal_router_node" in routing_decision:
        return {"next": "goal_router_node"}
    else:
        return {"next": "roadmap_agent"}


def roadmap_agent_node(state: ConversationState) -> ConversationState:
    """
    Learning roadmap generation node.

    Creates structured, actionable learning paths that bridge identified
    gaps and guide users toward their goals (HOW to achieve them).

    Args:
        state: Current conversation state

    Returns:
        Updated state with generated learning roadmap
    """
    result = roadmap_agent.invoke(state)
    return {"messages": result["messages"][-1]}


def roadmap_json_node(state: ConversationState) -> ConversationState:
    """
    Post-roadmap JSON generation node.

    Analyzes the conversation context and roadmap to generate structured JSON data
    for React Flow visualization with nodes and edges representing the learning path.

    Args:
        state: Current conversation state

    Returns:
        Updated state with JSON formatted learning roadmap for React Flow
    """
    # Get the last AI message which should contain the roadmap
    last_ai_message = None
    for message in reversed(state["messages"]):
        if isinstance(message, AIMessage):
            last_ai_message = message
            break
    
    if not last_ai_message:
        # Return default JSON structure if no roadmap found
        default_json = {
            "nodes": [
                {
                    "id": "1",
                    "type": "learningNode",
                    "position": {"x": 500, "y": 100},
                    "data": {
                        "id": "1",
                        "label": "Getting Started",
                        "title": "Begin Your Learning Journey",
                        "description": "Start with foundational concepts and build from there.",
                        "level": "beginner",
                        "progress": 0,
                        "isCompleted": False,
                        "estimatedHours": 10,
                        "prerequisites": []
                    }
                }
            ],
            "edges": []
        }
        json_content = f"\n```roadmap_chart \n{json.dumps(default_json, indent=2)}\n```"
        return {"messages": AIMessage(content=json_content)}
    
    # Collect all conversation context for comprehensive analysis
    conversation_context = "\n".join([
        f"{type(msg).__name__}: {msg.content}" for msg in state["messages"] 
        if hasattr(msg, 'content') and msg.content
    ])
    
    # System prompt for generating React Flow JSON
    json_generation_prompt = """You are a learning roadmap visualization specialist that converts textual learning roadmaps into structured JSON data for React Flow visualization.

Analyze the provided conversation context and roadmap to create a comprehensive learning path with nodes and edges.

**Node Structure Requirements:**
- Each node represents a learning milestone/topic
- Progressive difficulty levels: beginner → intermediate → advanced
- Realistic time estimates (estimatedHours)
- Clear prerequisites relationships
- Strategic positioning for visual flow

**Edge Structure Requirements:**
- Connect prerequisite relationships
- Show learning progression paths
- Include unlocked/locked states based on prerequisites

**Positioning Guidelines:**
- Start at top (y: 100-200) for beginner topics
- Progress downward (y increases by 250-300 per level)
- Spread horizontally (x: 150-850) for parallel topics
- Center important convergence points

**Level Guidelines:**
- beginner: 0-20 hours, basic concepts
- intermediate: 20-40 hours, practical application
- advanced: 40+ hours, complex integration

Return ONLY a valid JSON object in this exact format:

{
  "nodes": [
    {
      "id": "unique_id",
      "type": "learningNode",
      "position": {"x": number, "y": number},
      "data": {
        "id": "same_as_node_id",
        "label": "Short Node Name",
        "title": "Descriptive Title",
        "description": "Detailed description of what will be learned",
        "level": "beginner|intermediate|advanced",
        "progress": 0,
        "isCompleted": false,
        "estimatedHours": number,
        "prerequisites": ["id1", "id2"]
      }
    }
  ],
  "edges": [
    {
      "id": "e{source}-{target}",
      "source": "source_node_id",
      "target": "target_node_id", 
      "type": "learningEdge",
      "markerEnd": {"type": "ArrowClosed"},
      "data": {"isUnlocked": true|false}
    }
  ]
}

Create 5-8 nodes representing the complete learning journey from the roadmap, with logical prerequisite connections and realistic progression."""

    # Create messages for JSON generation
    json_messages = [
        SystemMessage(content=json_generation_prompt),
        HumanMessage(content=f"Conversation Context and Roadmap:\n{conversation_context}\n\nPlease generate the React Flow JSON structure for this learning roadmap.")
    ]
    
    # Generate JSON response
    json_response = model.invoke(json_messages)
    
    # Extract and validate JSON from response
    try:
        # Try to parse the response as JSON directly
        response_content = json_response.content.strip()
        
        # Remove any markdown code blocks if present
        if response_content.startswith("```"):
            lines = response_content.split("\n")
            # Find the JSON content between code blocks
            json_lines = []
            in_json = False
            for line in lines:
                if line.startswith("\n```roadmap_chart") or line.startswith("```"):
                    in_json = not in_json
                    continue
                if in_json:
                    json_lines.append(line)
            response_content = "\n".join(json_lines)
        
        # Parse and validate JSON
        roadmap_data = json.loads(response_content)
        
        # Validate the structure
        if isinstance(roadmap_data, dict) and "nodes" in roadmap_data and "edges" in roadmap_data:
            # Ensure nodes have required structure
            if isinstance(roadmap_data["nodes"], list) and len(roadmap_data["nodes"]) > 0:
                # Return validated JSON in code block format
                json_content = f"\n```roadmap_chart\n{json.dumps(roadmap_data, indent=2)}\n```"
                return {"messages": AIMessage(content=json_content)}
        
        raise ValueError("Invalid roadmap JSON structure")
        
    except (json.JSONDecodeError, ValueError) as e:
        # Fallback to default roadmap if parsing fails
        fallback_json = {
            "nodes": [
                {
                    "id": "1",
                    "type": "learningNode", 
                    "position": {"x": 500, "y": 100},
                    "data": {
                        "id": "1",
                        "label": "Fundamentals",
                        "title": "Core Concepts",
                        "description": "Master the fundamental concepts and terminology.",
                        "level": "beginner",
                        "progress": 0,
                        "isCompleted": False,
                        "estimatedHours": 20,
                        "prerequisites": []
                    }
                },
                {
                    "id": "2",
                    "type": "learningNode",
                    "position": {"x": 300, "y": 400},
                    "data": {
                        "id": "2", 
                        "label": "Practical Skills",
                        "title": "Hands-on Practice",
                        "description": "Apply concepts through practical exercises and projects.",
                        "level": "intermediate",
                        "progress": 0,
                        "isCompleted": False,
                        "estimatedHours": 30,
                        "prerequisites": ["1"]
                    }
                },
                {
                    "id": "3",
                    "type": "learningNode",
                    "position": {"x": 700, "y": 400},
                    "data": {
                        "id": "3",
                        "label": "Advanced Topics",
                        "title": "Specialized Knowledge", 
                        "description": "Dive deep into advanced concepts and specialized areas.",
                        "level": "advanced",
                        "progress": 0,
                        "isCompleted": False,
                        "estimatedHours": 40,
                        "prerequisites": ["1"]
                    }
                },
                {
                    "id": "4",
                    "type": "learningNode",
                    "position": {"x": 500, "y": 700},
                    "data": {
                        "id": "4",
                        "label": "Integration",
                        "title": "Complete Projects",
                        "description": "Build comprehensive projects integrating all learned concepts.",
                        "level": "advanced",
                        "progress": 0,
                        "isCompleted": False,
                        "estimatedHours": 50,
                        "prerequisites": ["2", "3"]
                    }
                }
            ],
            "edges": [
                {
                    "id": "e1-2",
                    "source": "1",
                    "target": "2",
                    "type": "learningEdge",
                    "markerEnd": {"type": "ArrowClosed"},
                    "data": {"isUnlocked": True}
                },
                {
                    "id": "e1-3", 
                    "source": "1",
                    "target": "3",
                    "type": "learningEdge",
                    "markerEnd": {"type": "ArrowClosed"},
                    "data": {"isUnlocked": True}
                },
                {
                    "id": "e2-4",
                    "source": "2", 
                    "target": "4",
                    "type": "learningEdge",
                    "markerEnd": {"type": "ArrowClosed"},
                    "data": {"isUnlocked": False}
                },
                {
                    "id": "e3-4",
                    "source": "3",
                    "target": "4", 
                    "type": "learningEdge",
                    "markerEnd": {"type": "ArrowClosed"},
                    "data": {"isUnlocked": False}
                }
            ]
        }
        json_content = f"\n```roadmap_chart\n{json.dumps(fallback_json, indent=2)}\n```"
        return {"messages": AIMessage(content=json_content)}


# Build the graph
builder = StateGraph(ConversationState)
builder.add_node("supervisor_agent", supervisor_agent_node)
builder.add_node("resume_rag_agent", resume_rag_agent_node)
builder.add_node("goal_router", goal_router_node)
builder.add_node("goal_flow", goal_flow_node)
builder.add_node("roadmap_router", roadmap_router_node)
builder.add_node("roadmap_agent", roadmap_agent_node)
builder.add_node("roadmap_json_node", roadmap_json_node)
builder.add_node("goal_radar_chart", goal_radar_chart_node)
builder.add_edge(START, "supervisor_agent")
builder.add_conditional_edges(
    "supervisor_agent",
    lambda state: state["next"],
    {
        "resume_rag_agent": "resume_rag_agent",
        "goal_agent": "goal_router",
        "roadmap_agent": "roadmap_router",
    },
)
builder.add_conditional_edges(
    "goal_router",
    lambda state: state.get("next", END),
    {"goal_flow": "goal_flow", "resume_rag_agent": "resume_rag_agent"},
)
builder.add_conditional_edges(
    "goal_flow",
    lambda state: state.get("next", END),
    {"goal_radar_chart": "goal_radar_chart"},
)
builder.add_conditional_edges(
    "roadmap_router",
    lambda state: state.get("next", END),
    {"goal_router_node": "goal_router", "roadmap_agent": "roadmap_agent"},
)
builder.add_edge("resume_rag_agent", END)
builder.add_edge("goal_radar_chart", END)
builder.add_edge("roadmap_agent", "roadmap_json_node")
builder.add_edge("roadmap_json_node", END)

learning_supervisor_agent = builder.compile(checkpointer=MemorySaver(), store=InMemoryStore())

print(learning_supervisor_agent.get_graph().draw_mermaid())


if __name__ == "__main__":
    print(learning_supervisor_agent.get_graph().draw_mermaid())


def get_last_message(result):
    """
    Extract the most recent message from agent result.

    Utility function to retrieve only the last message from the agent's
    response, useful for displaying clean output in UI applications.

    Args:
        result: Agent invocation result containing messages list

    Returns:
        Last message object or None if no messages exist
    """
    if result["messages"]:
        return result["messages"][-1]
    return None


def streamlit_invoke(user_input, session_state):
    """
    Streamlit-compatible agent invocation with persistent conversation state.

    Manages thread-based conversation persistence for Streamlit applications,
    ensuring the learning supervisor agent maintains context across user
    interactions within the same session.

    Args:
        user_input: User's message/query string
        session_state: Streamlit session state object (st.session_state)

    Returns:
        Agent result with conversation response

    Example:
        ```python
        import streamlit as st
        from src.agents.learning_supervisor_agent import streamlit_invoke

        user_input = st.text_input("Ask about learning paths:")
        if user_input:
            result = streamlit_invoke(user_input, st.session_state)
            last_msg = get_last_message(result)
            if last_msg:
                st.write(last_msg.content)
        ```
    """
    from langchain_core.messages import HumanMessage

    if "thread_id" not in session_state:
        session_state["thread_id"] = str(uuid.uuid4())
    thread_id = session_state["thread_id"]
    result = learning_supervisor_agent.invoke(
        {"messages": [HumanMessage(content=user_input)]},
        config={"configurable": {"thread_id": thread_id}},
    )
    return result