# Build frontend from source
FROM --platform=linux/amd64 node:22-alpine AS build
WORKDIR /app

# Install system dependencies required for native modules
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    libc6-compat \
    vips-dev

# Create necessary directories and set permissions
RUN mkdir -p /tmp/.yarn && chmod 777 /tmp/.yarn

# Set environment variables for better Docker compatibility
ENV NPM_CONFIG_LOGLEVEL=info
ENV YARN_CACHE_FOLDER=/tmp/.yarn
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Install yarn specifically (ensure latest version) - force overwrite existing
RUN npm install -g yarn@latest --force
RUN yarn --version

# Copy package files first for layer caching
COPY src/frontend/package.json ./
COPY src/frontend/yarn.lock ./

# Verify files are copied correctly
RUN ls -la ./

# Clear yarn cache and install dependencies with explicit configuration
RUN yarn cache clean && \
    yarn config set network-timeout 600000 && \
    yarn config set registry https://registry.npmjs.org/ && \
    yarn install --verbose

# Copy application source files explicitly
COPY src/frontend/src ./src
COPY src/frontend/public ./public
COPY src/frontend/index.html ./
COPY src/frontend/vite.config.mjs ./
COPY src/frontend/tsconfig.json ./
COPY src/frontend/tsconfig.build.json ./
COPY src/frontend/postcss.config.cjs ./
COPY src/frontend/.prettierrc.mjs ./
COPY src/frontend/eslint.config.js ./
COPY src/frontend/vitest.setup.mjs ./

# Verify all files are copied
RUN ls -la ./

# Build the frontend
RUN yarn build

# Production stage
FROM nginx:alpine AS production

# Create nginx temp directories
RUN mkdir -p /tmp/nginx/client_temp \
    /tmp/nginx/proxy_temp \
    /tmp/nginx/fastcgi_temp \
    /tmp/nginx/uwsgi_temp \
    /tmp/nginx/scgi_temp \
    && chmod -R 755 /tmp/nginx

COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
