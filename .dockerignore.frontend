# Frontend-specific .dockerignore
# Only exclude what's absolutely necessary for frontend builds

# Git and version control
.git
.gitignore

# Docker files not needed in frontend
Dockerfile*
docker-compose*.yml

# Python-specific (not needed for frontend)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Python testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# IDEs and OS
.vscode/
.idea/
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# CI/CD
.github/
.gitlab-ci.yml

# Large data files and docs (keep frontend docs)
data/
examples/
media/
docs/

# Python requirements (not needed for frontend)
requirements*.txt
pyproject.toml
uv.lock

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Logs
*.log

# Keep frontend source and config files - DO NOT exclude them
# The following should NOT be excluded for frontend builds:
# src/frontend/
# *.json (needed for package.json, tsconfig.json, etc.)
# *.xml (might be needed for configs)
# *.md (might be needed for component docs)
