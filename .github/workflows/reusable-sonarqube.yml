name: PathForge AI - SonarQube Analysis

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      uv-version:
        description: 'UV version to use'
        required: false
        type: string
        default: '0.5.11'
      node-version:
        description: 'Node version to use'
        required: false
        type: string
        default: '20'
      scan-python:
        description: 'Whether to scan Python agent service'
        required: false
        type: boolean
        default: false
      scan-frontend:
        description: 'Whether to scan TypeScript frontend'
        required: false
        type: boolean
        default: false
      scan-backend:
        description: 'Whether to scan TypeScript backend'
        required: false
        type: boolean
        default: false
    secrets:
      SONAR_TOKEN:
        required: true

env:
  SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
  CI: true

jobs:
  sonarqube:
    runs-on: ubuntu-latest
    name: SonarQube Analysis
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    # Python Agent Service Setup and Testing
    - name: Set up Python (if scanning Python)
      if: inputs.scan-python == true
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv (if scanning Python)
      if: inputs.scan-python == true
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ inputs.uv-version }}

    - name: Install Python dependencies (if scanning Python)
      if: inputs.scan-python == true
      run: |
        uv sync --frozen --group dev

    - name: Run Python tests with coverage (if scanning Python)
      if: inputs.scan-python == true
      run: |
        uv run pytest tests/ \
          --cov=src \
          --cov-report=xml:coverage.xml \
          --cov-report=term-missing \
          --junitxml=pytest-junit.xml
      continue-on-error: true

    # TypeScript Frontend Setup and Testing
    - name: Set up Node.js (if scanning frontend or backend)
      if: inputs.scan-frontend == true || inputs.scan-backend == true
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          src/frontend/package-lock.json
          src/backend/package-lock.json

    - name: Install frontend dependencies (if scanning frontend)
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: npm ci

    - name: Run frontend tests with coverage (if scanning frontend)
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        npm run vitest:coverage
      continue-on-error: true

    # TypeScript Backend Setup and Testing
    - name: Install backend dependencies (if scanning backend)
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: npm ci

    - name: Run backend tests with coverage (if scanning backend)
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        npm run test:coverage
      continue-on-error: true

    - name: Install required tools
      run: |
        echo "Installing required tools for SonarQube analysis..."
        sudo apt-get update -q
        sudo apt-get install -y jq curl unzip
        echo "✅ Required tools installed"

    # SonarQube Analysis with enhanced error handling and debugging
    - name: Clear SonarQube cache to prevent hash mismatches
      run: |
        echo "Clearing potentially corrupted SonarQube cache..."
        rm -rf ~/.sonar/cache
        mkdir -p ~/.sonar/cache
        echo "✅ SonarQube cache cleared"

    - name: Cache SonarQube scanner
      uses: actions/cache@v4
      with:
        path: |
          ~/.sonar/cache
          /opt/sonar-scanner
        key: ${{ runner.os }}-sonar-scanner-v3-${{ hashFiles('**/sonar-project.properties') }}
        restore-keys: |
          ${{ runner.os }}-sonar-scanner-v3-
          ${{ runner.os }}-sonar-scanner-

    - name: Install SonarScanner CLI with retry logic
      run: |
        echo "Installing SonarScanner CLI with retry logic..."
        
        # Check if SonarScanner is already cached
        if [ -x "/opt/sonar-scanner/bin/sonar-scanner" ]; then
          echo "✅ SonarScanner already cached, skipping download"
          sudo ln -sf /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
          sonar-scanner --version
          exit 0
        fi
        
        # Function to download with retry
        download_with_retry() {
          local url=$1
          local output=$2
          local attempts=5
          local wait_time=10
          
          for i in $(seq 1 $attempts); do
            echo "Attempt $i/$attempts: Downloading SonarScanner..."
            if wget -q --timeout=60 --tries=3 -O "$output" "$url"; then
              echo "✅ Successfully downloaded SonarScanner"
              return 0
            else
              echo "❌ Failed to download SonarScanner (attempt $i/$attempts)"
              rm -f "$output"
              if [ $i -lt $attempts ]; then
                echo "⏳ Waiting ${wait_time} seconds before retry..."
                sleep $wait_time
                wait_time=$((wait_time * 2))  # Exponential backoff
              fi
            fi
          done
          echo "💥 Failed to download SonarScanner after $attempts attempts"
          return 1
        }
        
        # Use a more recent version of SonarScanner
        SCANNER_VERSION="6.2.1.4610"
        
        # Download with retry logic
        if download_with_retry \
          "https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SCANNER_VERSION}-linux-x64.zip" \
          "sonarscanner.zip"; then
          
          echo "Extracting and installing SonarScanner..."
          unzip -q sonarscanner.zip
          sudo mv "sonar-scanner-${SCANNER_VERSION}-linux-x64" /opt/sonar-scanner
          sudo ln -sf /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
          sonar-scanner --version
          echo "✅ SonarScanner CLI installed successfully"
        else
          echo "💥 Failed to install SonarScanner CLI"
          exit 1
        fi
      continue-on-error: false

    - name: Cache and pre-download SonarQube plugins with enhanced retry logic
      run: |
        echo "Pre-downloading required plugins to avoid runtime failures..."
        mkdir -p ~/.sonar/cache/plugins
        
        # Function to download with enhanced retry and validation
        download_plugin_with_retry() {
          local url=$1
          local output=$2
          local plugin_name=$3
          local attempts=5
          local wait_time=10
          
          # Check if plugin already exists and is valid
          if [ -f "$output" ] && [ -s "$output" ]; then
            echo "✅ Plugin $plugin_name already cached and valid"
            return 0
          fi
          
          for i in $(seq 1 $attempts); do
            echo "Attempt $i/$attempts: Downloading $plugin_name..."
            if wget -q --timeout=60 --tries=3 -O "$output" "$url"; then
              # Verify the downloaded file is a valid JAR
              if file "$output" | grep -q "Java archive"; then
                echo "✅ Successfully downloaded and verified $plugin_name"
                return 0
              else
                echo "❌ Downloaded file is not a valid JAR for $plugin_name"
                rm -f "$output"
              fi
            else
              echo "❌ Failed to download $plugin_name (attempt $i/$attempts)"
              rm -f "$output"
            fi
            
            if [ $i -lt $attempts ]; then
              echo "⏳ Waiting ${wait_time} seconds before retry..."
              sleep $wait_time
              wait_time=$((wait_time * 2))  # Exponential backoff
            fi
          done
          echo "⚠️ Failed to download $plugin_name after $attempts attempts, will try during scan"
          return 1
        }
        
        # Download plugins with enhanced retry logic
        download_plugin_with_retry \
          "https://binaries.sonarsource.com/Distribution/sonar-python-plugin/sonar-python-plugin-4.21.0.11165.jar" \
          "~/.sonar/cache/plugins/sonar-python-plugin.jar" \
          "Python plugin"
          
        download_plugin_with_retry \
          "https://binaries.sonarsource.com/Distribution/sonar-javascript-plugin/sonar-javascript-plugin-10.17.0.25226.jar" \
          "~/.sonar/cache/plugins/sonar-javascript-plugin.jar" \
          "JavaScript plugin"
        
        # List successfully cached plugins
        echo "Successfully cached plugins:"
        ls -la ~/.sonar/cache/plugins/ 2>/dev/null || echo "No plugins cached yet"
      continue-on-error: true

    - name: Verify SonarQube configuration
      run: |
        echo "Verifying SonarQube environment setup..."
        echo "SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}"
        echo "SONAR_TOKEN configured: ${{ secrets.SONAR_TOKEN != '' }}"
        echo "Project key from sonar-project.properties:"
        grep "sonar.projectKey" sonar-project.properties || echo "No project key found"
        
        # Test connectivity to SonarQube server with better error handling
        echo "Testing connectivity to SonarQube server..."
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" --max-time 30 "${{ env.SONAR_HOST_URL }}/api/system/status" || echo "000")
        echo "HTTP response code: $HTTP_CODE"
        
        if [ "$HTTP_CODE" -ge 200 ] && [ "$HTTP_CODE" -lt 300 ]; then
          echo "✅ Successfully connected to SonarQube server"
        else
          echo "⚠️ Could not reach SonarQube server (HTTP $HTTP_CODE)"
        fi

    - name: SonarQube Scan with Enhanced Configuration
      run: |
        echo "Starting SonarQube scan with enhanced error handling..."
        
        # Function to run SonarQube scan with retry on hash mismatch
        run_sonar_scan() {
          local attempt=$1
          echo "🔄 SonarQube scan attempt $attempt"
          
          # Clear cache on retry attempts
          if [ $attempt -gt 1 ]; then
            echo "Clearing SonarQube cache due to previous failure..."
            rm -rf ~/.sonar/cache
            mkdir -p ~/.sonar/cache
          fi
          
          # Set Java options for better memory management
          export JAVA_OPTS="-Xmx3072m -XX:+UseG1GC -XX:MaxMetaspaceSize=512m"
          export SONAR_SCANNER_OPTS="$JAVA_OPTS"
          
          # Run the scan
          sonar-scanner \
            -Dsonar.projectKey="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" \
            -Dsonar.projectName="CodePluse Platform" \
            -Dsonar.projectVersion="1.0" \
            -Dsonar.organization="namnhcntt" \
            -Dsonar.sources=src \
            -Dsonar.tests=tests \
            -Dsonar.host.url="${{ env.SONAR_HOST_URL }}" \
            -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
            -Dsonar.python.coverage.reportPaths=coverage.xml \
            -Dsonar.javascript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
            -Dsonar.typescript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
            -Dsonar.exclusions="**/migrations/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/dist/**,**/build/**,**/*.pyc,**/vendor/**,**/target/**,**/streamlit*/**,src/streamlit_app.py" \
            -Dsonar.test.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/*.test.ts,**/*.spec.ts,**/*.test.tsx,**/*.spec.tsx" \
            -Dsonar.coverage.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/migrations/**,**/venv/**,src/frontend/src/**/*.test.ts,src/frontend/src/**/*.spec.ts,src/frontend/src/**/*.test.tsx,src/frontend/src/**/*.spec.tsx,src/backend/tests/**,src/backend/**/*.test.ts,src/backend/**/*.spec.ts" \
            -Dsonar.sourceEncoding=UTF-8 \
            -Dsonar.python.version=3.12 \
            -Dsonar.javascript.environments=node \
            -Dsonar.typescript.tsconfigPath=src/frontend/tsconfig.json,src/backend/tsconfig.json \
            -Dsonar.qualitygate.wait=true \
            -Dsonar.log.level=INFO \
            -Dsonar.scanner.force.timeout=true
        }
        
        # Retry logic for hash mismatch errors
        MAX_ATTEMPTS=3
        ATTEMPT=1
        
        while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
          if run_sonar_scan $ATTEMPT; then
            echo "✅ SonarQube scan completed successfully on attempt $ATTEMPT"
            break
          else
            EXIT_CODE=$?
            echo "❌ SonarQube scan failed on attempt $ATTEMPT with exit code $EXIT_CODE"
            
            # Check if it's a hash mismatch error
            if [ $ATTEMPT -lt $MAX_ATTEMPTS ]; then
              echo "⏳ Waiting 30 seconds before retry..."
              sleep 30
              ATTEMPT=$((ATTEMPT + 1))
            else
              echo "💥 All retry attempts exhausted. Failing the job."
              exit $EXIT_CODE
            fi
          fi
        done
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
      timeout-minutes: 45

    - name: SonarQube Quality Gate Check (Fallback)
      if: always()
      run: |
        echo "Checking SonarQube Quality Gate status..."
        # Wait a bit for the analysis to complete
        sleep 30
        
        # Get the task status
        TASK_URL="${{ env.SONAR_HOST_URL }}/api/ce/task?id=$(cat .scannerwork/report-task.txt | grep 'ceTaskId=' | cut -d'=' -f2)"
        echo "Task URL: $TASK_URL"
        
        # Check quality gate status via API
        ANALYSIS_ID=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$TASK_URL" | jq -r '.task.analysisId // empty')
        if [ -n "$ANALYSIS_ID" ]; then
          QUALITY_GATE_URL="${{ env.SONAR_HOST_URL }}/api/qualitygates/project_status?analysisId=$ANALYSIS_ID"
          echo "Quality Gate URL: $QUALITY_GATE_URL"
          QUALITY_GATE_STATUS=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$QUALITY_GATE_URL" | jq -r '.projectStatus.status // "ERROR"')
          echo "Quality Gate Status: $QUALITY_GATE_STATUS"
          
          if [ "$QUALITY_GATE_STATUS" != "OK" ] && [ "$QUALITY_GATE_STATUS" != "WARN" ]; then
            echo "Quality gate failed with status: $QUALITY_GATE_STATUS"
            exit 1
          fi
        else
          echo "Could not retrieve analysis ID, skipping quality gate check"
        fi
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
      continue-on-error: true

    # Upload coverage reports as artifacts for debugging
    - name: Upload Python coverage report
      if: inputs.scan-python == true
      uses: actions/upload-artifact@v4
      with:
        name: python-coverage-report
        path: coverage.xml

    - name: Upload frontend coverage report
      if: inputs.scan-frontend == true
      uses: actions/upload-artifact@v4
      with:
        name: frontend-coverage-report
        path: src/frontend/coverage/

    - name: Upload backend coverage report
      if: inputs.scan-backend == true
      uses: actions/upload-artifact@v4
      with:
        name: backend-coverage-report
        path: src/backend/coverage/
