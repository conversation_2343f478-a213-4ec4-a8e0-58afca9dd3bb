name: Manual SonarQube Scan

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to scan (leave empty for current branch)'
        required: false
        type: string
      scan-python:
        description: 'Scan Python agent service'
        required: false
        type: boolean
        default: true
      scan-frontend:
        description: 'Scan TypeScript frontend'
        required: false
        type: boolean
        default: true
      scan-backend:
        description: 'Scan TypeScript backend'
        required: false
        type: boolean
        default: true
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      node-version:
        description: 'Node.js version to use'
        required: false
        type: string
        default: '20'
      skip-quality-gate:
        description: 'Skip SonarQube Quality Gate check'
        required: false
        type: boolean
        default: false

env:
  PYTHON_VERSION: ${{ inputs.python-version }}
  UV_VERSION: "0.5.11"
  NODE_VERSION: ${{ inputs.node-version }}
  SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
  CI: true

jobs:
  manual-sonar-scan:
    runs-on: ubuntu-latest
    name: Manual SonarQube Scan
    
    steps:
    - name: Display scan parameters
      run: |
        echo "🔍 Manual SonarQube Scan Configuration:"
        echo "Branch: ${{ inputs.branch || github.ref_name }}"
        echo "Scan Python: ${{ inputs.scan-python }}"
        echo "Scan Frontend: ${{ inputs.scan-frontend }}"
        echo "Scan Backend: ${{ inputs.scan-backend }}"
        echo "Python Version: ${{ inputs.python-version }}"
        echo "Node.js Version: ${{ inputs.node-version }}"
        echo "Skip Quality Gate: ${{ inputs.skip-quality-gate }}"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ inputs.branch || github.ref }}
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    # Python Agent Service Setup and Testing
    - name: Set up Python
      if: inputs.scan-python == true
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv
      if: inputs.scan-python == true
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ env.UV_VERSION }}

    - name: Install Python dependencies
      if: inputs.scan-python == true
      run: |
        echo "📦 Installing Python dependencies..."
        uv sync --frozen --group dev

    - name: Run Python linting
      if: inputs.scan-python == true
      run: |
        echo "🔍 Running Python linting..."
        uv run ruff check --output-format=text . > ruff-report.txt || true
        echo "✅ Python linting completed"
      continue-on-error: true

    - name: Run Python tests with coverage
      if: inputs.scan-python == true
      run: |
        echo "🧪 Running Python tests with coverage..."
        uv run pytest tests/ \
          --cov=src \
          --cov-report=xml:coverage.xml \
          --cov-report=term-missing \
          --junitxml=pytest-junit.xml \
          --tb=short
        echo "✅ Python tests completed"
      continue-on-error: true

    # TypeScript Frontend and Backend Setup
    - name: Set up Node.js
      if: inputs.scan-frontend == true || inputs.scan-backend == true
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          src/frontend/package-lock.json
          src/backend/package-lock.json

    # Frontend Testing
    - name: Install frontend dependencies
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "📦 Installing frontend dependencies..."
        npm ci
        echo "✅ Frontend dependencies installed"

    - name: Run frontend linting
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "🔍 Running frontend linting..."
        npm run lint || true
        echo "✅ Frontend linting completed"
      continue-on-error: true

    - name: Run frontend tests with coverage
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "🧪 Running frontend tests with coverage..."
        npm run vitest:coverage
        echo "✅ Frontend tests completed"
      continue-on-error: true

    # Backend Testing
    - name: Install backend dependencies
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "📦 Installing backend dependencies..."
        npm ci
        echo "✅ Backend dependencies installed"

    - name: Run backend linting
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "🔍 Running backend linting..."
        npm run lint || true
        echo "✅ Backend linting completed"
      continue-on-error: true

    - name: Run backend tests with coverage
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "🧪 Running backend tests with coverage..."
        npm run test:coverage
        echo "✅ Backend tests completed"
      continue-on-error: true

    - name: Install required tools
      run: |
        echo "Installing required tools for SonarQube analysis..."
        sudo apt-get update -q
        sudo apt-get install -y jq curl unzip
        echo "✅ Required tools installed"

    # SonarQube Analysis with enhanced error handling
    - name: Cache SonarQube scanner and plugins
      uses: actions/cache@v4
      with:
        path: |
          ~/.sonar/cache
          /opt/sonar-scanner
        key: ${{ runner.os }}-sonar-manual-v3-${{ hashFiles('**/sonar-project.properties') }}
        restore-keys: |
          ${{ runner.os }}-sonar-manual-v3-
          ${{ runner.os }}-sonar-manual-

    - name: Install SonarScanner CLI with retry logic
      run: |
        echo "Installing SonarScanner CLI with retry logic..."
        
        # Check if SonarScanner is already cached
        if [ -x "/opt/sonar-scanner/bin/sonar-scanner" ]; then
          echo "✅ SonarScanner already cached, skipping download"
          sudo ln -sf /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
          sonar-scanner --version
          exit 0
        fi
        
        # Function to download with retry
        download_with_retry() {
          local url=$1
          local output=$2
          local attempts=5
          local wait_time=10
          
          for i in $(seq 1 $attempts); do
            echo "Attempt $i/$attempts: Downloading SonarScanner..."
            if curl -L --connect-timeout 30 --max-time 600 --retry 3 --retry-delay 10 \
                    -o "$output" "$url"; then
              # Verify the downloaded file
              if [ -f "$output" ] && [ -s "$output" ]; then
                local file_size=$(stat -c%s "$output" 2>/dev/null || stat -f%z "$output" 2>/dev/null || echo "0")
                if [ "$file_size" -gt 10000000 ]; then  # Should be > 10MB
                  echo "✅ Successfully downloaded SonarScanner (size: $file_size bytes)"
                  return 0
                else
                  echo "❌ Downloaded file too small (size: $file_size bytes)"
                fi
              else
                echo "❌ Downloaded file is empty or missing"
              fi
            else
              echo "❌ Failed to download SonarScanner (attempt $i/$attempts)"
            fi
            
            rm -f "$output"
            if [ $i -lt $attempts ]; then
              echo "⏳ Waiting ${wait_time} seconds before retry..."
              sleep $wait_time
              wait_time=$((wait_time * 2))  # Exponential backoff
            fi
          done
          echo "💥 Failed to download SonarScanner after $attempts attempts"
          return 1
        }
        
        # Use a more recent version of SonarScanner
        SCANNER_VERSION="6.2.1.4610"
        
        # Try multiple mirror sources
        DOWNLOAD_URLS=(
          "https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SCANNER_VERSION}-linux-x64.zip"
          "https://github.com/SonarSource/sonar-scanner-cli/releases/download/${SCANNER_VERSION}/sonar-scanner-cli-${SCANNER_VERSION}-linux-x64.zip"
        )
        
        # Try downloading from different sources
        DOWNLOADED=false
        for url in "${DOWNLOAD_URLS[@]}"; do
          echo "Trying to download from: $url"
          if download_with_retry "$url" "sonarscanner.zip"; then
            DOWNLOADED=true
            break
          fi
        done
        
        if [ "$DOWNLOADED" = true ]; then
          echo "Extracting and installing SonarScanner..."
          unzip -q sonarscanner.zip
          sudo mv "sonar-scanner-${SCANNER_VERSION}-linux-x64" /opt/sonar-scanner
          sudo ln -sf /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
          sonar-scanner --version
          echo "✅ SonarScanner CLI installed successfully"
        else
          echo "💥 Failed to install SonarScanner CLI from all sources"
          echo "Falling back to package manager installation..."
          # Fallback to apt installation
          wget -qO- https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SCANNER_VERSION}-linux-x64.zip -O /tmp/sonar.zip || \
          curl -L https://repo1.maven.org/maven2/org/sonarsource/scanner/cli/sonar-scanner-cli/${SCANNER_VERSION}/sonar-scanner-cli-${SCANNER_VERSION}.jar -o /tmp/sonar.jar
          echo "If fallback fails, the workflow will attempt to continue with existing tools"
        fi
      continue-on-error: false

    - name: Cache and pre-download SonarQube plugins with enhanced retry logic
      run: |
        echo "Pre-downloading required plugins to avoid runtime failures..."
        mkdir -p ~/.sonar/cache/plugins
        
        # Function to download with enhanced retry and validation
        download_plugin_with_retry() {
          local url=$1
          local output_file=$2
          local plugin_name=$3
          local attempts=5
          local wait_time=10
          
          # Expand tilde to actual home directory
          local expanded_output=$(eval echo "$output_file")
          
          # Check if plugin already exists and is valid
          if [ -f "$expanded_output" ] && [ -s "$expanded_output" ]; then
            echo "✅ Plugin $plugin_name already cached and valid"
            return 0
          fi
          
          for i in $(seq 1 $attempts); do
            echo "Attempt $i/$attempts: Downloading $plugin_name..."
            # Create directory if it doesn't exist
            mkdir -p "$(dirname "$expanded_output")"
            
            if curl -L --connect-timeout 30 --max-time 300 --retry 3 --retry-delay 5 \
                    -o "$expanded_output" "$url"; then
              # Verify the downloaded file is a valid JAR and has reasonable size
              if [ -f "$expanded_output" ] && [ -s "$expanded_output" ] && \
                 file "$expanded_output" | grep -q "Java archive\|Zip archive"; then
                local file_size=$(stat -c%s "$expanded_output" 2>/dev/null || stat -f%z "$expanded_output" 2>/dev/null || echo "0")
                if [ "$file_size" -gt 1000 ]; then  # JAR should be > 1KB
                  echo "✅ Successfully downloaded and verified $plugin_name (size: $file_size bytes)"
                  return 0
                else
                  echo "❌ Downloaded file too small for $plugin_name (size: $file_size bytes)"
                fi
              else
                echo "❌ Downloaded file is not a valid JAR for $plugin_name"
              fi
              rm -f "$expanded_output"
            else
              echo "❌ Failed to download $plugin_name (attempt $i/$attempts)"
              rm -f "$expanded_output"
            fi
            
            if [ $i -lt $attempts ]; then
              echo "⏳ Waiting ${wait_time} seconds before retry..."
              sleep $wait_time
              wait_time=$((wait_time * 2))  # Exponential backoff
            fi
          done
          echo "⚠️ Failed to download $plugin_name after $attempts attempts, will try during scan"
          return 1
        }
        
        # Download plugins with enhanced retry logic - Skip plugin pre-download to let SonarQube handle it
        echo "Skipping plugin pre-download to let SonarQube scanner handle plugin management"
        echo "This approach is more reliable as SonarQube has better error handling for plugins"
        
        # List existing cached plugins if any
        echo "Existing cached plugins:"
        ls -la ~/.sonar/cache/plugins/ 2>/dev/null || echo "No plugins cached yet"
      continue-on-error: true

    - name: Verify SonarQube configuration
      run: |
        echo "Verifying SonarQube environment setup..."
        echo "SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}"
        echo "SONAR_TOKEN configured: ${{ secrets.SONAR_TOKEN != '' }}"
        echo "Branch to scan: ${{ inputs.branch || github.ref_name }}"
        echo "Project key from sonar-project.properties:"
        grep "sonar.projectKey" sonar-project.properties || echo "No project key found"
        
        # Test connectivity to SonarQube server with retry
        echo "Testing connectivity to SonarQube server..."
        ATTEMPTS=3
        SUCCESS=false
        
        for i in $(seq 1 $ATTEMPTS); do
          echo "Connection attempt $i/$ATTEMPTS..."
          if curl -s --connect-timeout 30 --max-time 60 "${{ env.SONAR_HOST_URL }}/api/system/status"; then
            echo "✅ Successfully connected to SonarQube server"
            SUCCESS=true
            break
          else
            echo "❌ Could not reach SonarQube server (attempt $i/$ATTEMPTS)"
            if [ $i -lt $ATTEMPTS ]; then
              sleep 10
            fi
          fi
        done
        
        if [ "$SUCCESS" = false ]; then
          echo "⚠️ Warning: Could not establish connection to SonarQube server after $ATTEMPTS attempts"
          echo "The scan will proceed but may fail if connectivity issues persist"
        fi
        
        # Test authentication
        echo "Testing SonarQube authentication..."
        AUTH_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
          -H "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" \
          "${{ env.SONAR_HOST_URL }}/api/authentication/validate" || echo "failed")
        
        if echo "$AUTH_RESPONSE" | grep -q '"valid":true'; then
          echo "✅ SonarQube authentication is valid"
        else
          echo "⚠️ Warning: Could not validate SonarQube authentication"
          echo "Response: $AUTH_RESPONSE"
        fi

    - name: Run SonarQube analysis with debug logging
      run: |
        echo "Starting SonarQube scan with enhanced configuration and debug logging..."
        
        # Set Java options for better network handling and memory management
        export JAVA_OPTS="-Xmx3072m -XX:+UseG1GC -XX:+UseStringDeduplication"
        export SONAR_SCANNER_OPTS="-Xmx3072m -XX:+UseG1GC"
        
        # Add network timeout settings
        export SONAR_SCANNER_JAVA_OPTS="-Dsonar.ws.timeout=300 -Dhttp.socketTimeout=600000 -Dhttp.connectionTimeout=60000"
        
        # Test connectivity first
        echo "Testing SonarQube server connectivity..."
        if ! curl -s --connect-timeout 30 --max-time 60 "${{ env.SONAR_HOST_URL }}/api/system/status" > /dev/null; then
          echo "⚠️ Warning: Could not reach SonarQube server, but continuing with scan"
        else
          echo "✅ SonarQube server is reachable"
        fi
        
        # Run the scanner with enhanced timeout and retry logic
        sonar-scanner \
          -Dsonar.projectKey="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" \
          -Dsonar.projectName="CodePluse Platform" \
          -Dsonar.projectVersion="1.0" \
          -Dsonar.organization="namnhcntt" \
          -Dsonar.sources=src \
          -Dsonar.tests=tests \
          -Dsonar.host.url="${{ env.SONAR_HOST_URL }}" \
          -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
          -Dsonar.branch.name="${{ inputs.branch || github.ref_name }}" \
          -Dsonar.python.coverage.reportPaths=coverage.xml \
          -Dsonar.javascript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
          -Dsonar.typescript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
          -Dsonar.exclusions="**/migrations/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/dist/**,**/build/**,**/*.pyc,**/vendor/**,**/target/**,**/streamlit*/**,src/streamlit_app.py" \
          -Dsonar.test.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/*.test.ts,**/*.spec.ts,**/*.test.tsx,**/*.spec.tsx" \
          -Dsonar.coverage.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/migrations/**,**/venv/**,src/frontend/src/**/*.test.ts,src/frontend/src/**/*.spec.ts,src/frontend/src/**/*.test.tsx,src/frontend/src/**/*.spec.tsx,src/backend/tests/**,src/backend/**/*.test.ts,src/backend/**/*.spec.ts" \
          -Dsonar.sourceEncoding=UTF-8 \
          -Dsonar.python.version=3.12 \
          -Dsonar.javascript.environments=node \
          -Dsonar.typescript.tsconfigPath=src/frontend/tsconfig.json,src/backend/tsconfig.json \
          -Dsonar.qualitygate.wait=true \
          -Dsonar.ws.timeout=300 \
          -Dhttp.socketTimeout=600000 \
          -Dhttp.connectionTimeout=60000 \
          -Dsonar.log.level=INFO \
          -X || {
            echo "❌ SonarQube scan failed with exit code $?"
            echo "Checking for common issues..."
            
            # Check if it's a network timeout
            if [ -f ".scannerwork/sonar-scanner.log" ]; then
              echo "Last few lines of scanner log:"
              tail -20 .scannerwork/sonar-scanner.log || echo "Could not read scanner log"
            fi
            
            # Re-run with minimal configuration if the full scan failed
            echo "Attempting simplified scan without quality gate..."
            sonar-scanner \
              -Dsonar.projectKey="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" \
              -Dsonar.projectName="CodePluse Platform" \
              -Dsonar.sources=src \
              -Dsonar.host.url="${{ env.SONAR_HOST_URL }}" \
              -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
              -Dsonar.branch.name="${{ inputs.branch || github.ref_name }}" \
              -Dsonar.qualitygate.wait=false \
              -Dsonar.ws.timeout=300
          }
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
        JAVA_OPTS: "-Xmx3072m -XX:+UseG1GC"
        SONAR_SCANNER_OPTS: "-Xmx3072m -XX:+UseG1GC"
      timeout-minutes: 30

    - name: SonarQube Quality Gate Check (Enhanced)
      if: inputs.skip-quality-gate == false
      run: |
        echo "Checking SonarQube Quality Gate status with enhanced error handling..."
        # Wait a bit for the analysis to complete
        sleep 30
        
        # Get the task status
        if [ -f ".scannerwork/report-task.txt" ]; then
          TASK_URL="${{ env.SONAR_HOST_URL }}/api/ce/task?id=$(cat .scannerwork/report-task.txt | grep 'ceTaskId=' | cut -d'=' -f2)"
          echo "Task URL: $TASK_URL"
          
          # Check quality gate status via API
          ANALYSIS_ID=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$TASK_URL" | jq -r '.task.analysisId // empty')
          if [ -n "$ANALYSIS_ID" ]; then
            QUALITY_GATE_URL="${{ env.SONAR_HOST_URL }}/api/qualitygates/project_status?analysisId=$ANALYSIS_ID"
            echo "Quality Gate URL: $QUALITY_GATE_URL"
            QUALITY_GATE_STATUS=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$QUALITY_GATE_URL" | jq -r '.projectStatus.status // "ERROR"')
            echo "Quality Gate Status: $QUALITY_GATE_STATUS"
            
            if [ "$QUALITY_GATE_STATUS" != "OK" ] && [ "$QUALITY_GATE_STATUS" != "WARN" ]; then
              echo "Quality gate failed with status: $QUALITY_GATE_STATUS"
              exit 1
            fi
          else
            echo "Could not retrieve analysis ID, falling back to quality gate action"
            # Fallback to original action
            echo "SONAR_TOKEN=${{ secrets.SONAR_TOKEN }}" >> $GITHUB_ENV
            echo "SONAR_HOST_URL=${{ env.SONAR_HOST_URL }}" >> $GITHUB_ENV
          fi
        else
          echo "No report task file found, quality gate check skipped"
        fi
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
      continue-on-error: true

    - name: Skip Quality Gate message
      if: inputs.skip-quality-gate == true
      run: |
        echo "⚠️ SonarQube Quality Gate check was skipped as requested"

    # Upload coverage reports and logs as artifacts for debugging
    - name: Upload Python coverage report
      if: inputs.scan-python == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: python-coverage-report-${{ inputs.branch || github.ref_name }}
        path: |
          coverage.xml
          pytest-junit.xml
          ruff-report.txt

    - name: Upload frontend coverage report
      if: inputs.scan-frontend == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: frontend-coverage-report-${{ inputs.branch || github.ref_name }}
        path: src/frontend/coverage/

    - name: Upload backend coverage report
      if: inputs.scan-backend == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: backend-coverage-report-${{ inputs.branch || github.ref_name }}
        path: src/backend/coverage/

    - name: Display scan summary
      if: always()
      run: |
        echo "📊 SonarQube Scan Summary:"
        echo "Branch scanned: ${{ inputs.branch || github.ref_name }}"
        echo "Components scanned:"
        echo "  - Python Agent: ${{ inputs.scan-python }}"
        echo "  - Frontend: ${{ inputs.scan-frontend }}"
        echo "  - Backend: ${{ inputs.scan-backend }}"
        echo "Quality Gate: ${{ inputs.skip-quality-gate == true && 'Skipped' || 'Executed' }}"
        echo ""
        echo "🔗 View results in SonarQube:"
        echo "${{ env.SONAR_HOST_URL }}/dashboard?id=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
