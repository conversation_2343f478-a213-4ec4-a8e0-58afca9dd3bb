name: Heroku Direct Push - Alternative Method

on:
  workflow_call:
    inputs:
      service:
        description: 'Service to build and push'
        required: true
        type: string
      version:
        description: 'Version tag'
        required: true
        type: string
      force-push:
        description: 'Force push even if image exists'
        required: false
        type: boolean
        default: false
    secrets:
      HEROKU_API_KEY:
        required: true
      HEROKU_EMAIL:
        required: true

jobs:
  heroku-push:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver: docker-container
        driver-opts: |
          network=host

    - name: Login to Heroku Container Registry
      run: |
        for i in {1..3}; do
          echo "${{ secrets.HEROKU_API_KEY }}" | docker login --username=${{ secrets.HEROKU_EMAIL }} --password-stdin registry.heroku.com && break
          echo "Heroku login failed. Retrying attempt $i..."
          sleep $((i * 5))
        done

    - name: Set image variables
      id: vars
      run: |
        case "${{ inputs.service }}" in
          "frontend")
            PROCESS_TYPE="web"
            DOCKERFILE="docker/Dockerfile.frontend"
            CONTEXT="."
            ;;
          "agent_service")
            PROCESS_TYPE="agent"
            DOCKERFILE="docker/Dockerfile.agent"
            CONTEXT="."
            ;;
          "streamlit_app")
            PROCESS_TYPE="streamlit"
            DOCKERFILE="docker/Dockerfile.streamlit"
            CONTEXT="."
            ;;
          "backend")
            PROCESS_TYPE="api"
            DOCKERFILE="docker/Dockerfile.backend"
            CONTEXT="."
            ;;
          *)
            echo "Unknown service: ${{ inputs.service }}"
            exit 1
            ;;
        esac
        
        IMAGE_NAME="registry.heroku.com/pathforge-ai/$PROCESS_TYPE:${{ inputs.version }}"
        
        echo "PROCESS_TYPE=$PROCESS_TYPE" >> $GITHUB_OUTPUT
        echo "DOCKERFILE=$DOCKERFILE" >> $GITHUB_OUTPUT
        echo "CONTEXT=$CONTEXT" >> $GITHUB_OUTPUT
        echo "IMAGE_NAME=$IMAGE_NAME" >> $GITHUB_OUTPUT

    - name: Setup dockerignore for build
      run: |
        if [ "${{ inputs.service }}" = "agent_service" ]; then
          cp docker/.dockerignore.service .dockerignore
        elif [ "${{ inputs.service }}" = "frontend" ]; then
          cp docker/.dockerignore.frontend .dockerignore
        elif [ "${{ inputs.service }}" = "backend" ]; then
          cp docker/.dockerignore.service .dockerignore
        else
          cp docker/.dockerignore.app .dockerignore
        fi

    - name: Build Docker image
      run: |
        echo "🏗️ Building Docker image for ${{ inputs.service }}..."
        echo "Image: ${{ steps.vars.outputs.IMAGE_NAME }}"
        echo "Dockerfile: ${{ steps.vars.outputs.DOCKERFILE }}"
        echo "Context: ${{ steps.vars.outputs.CONTEXT }}"
        
        docker build \
          --platform linux/amd64 \
          --provenance=false \
          --file "${{ steps.vars.outputs.DOCKERFILE }}" \
          --tag "${{ steps.vars.outputs.IMAGE_NAME }}" \
          "${{ steps.vars.outputs.CONTEXT }}"

    - name: Push to Heroku Container Registry
      run: |
        echo "🚀 Pushing to Heroku Container Registry..."
        
        # Push with retry logic
        for i in {1..5}; do
          echo "Push attempt $i/5..."
          if docker push "${{ steps.vars.outputs.IMAGE_NAME }}"; then
            echo "✅ Successfully pushed ${{ steps.vars.outputs.IMAGE_NAME }}"
            break
          else
            echo "❌ Push attempt $i failed"
            if [ $i -eq 5 ]; then
              echo "❌ All push attempts failed"
              exit 1
            fi
            echo "Waiting 10 seconds before retry..."
            sleep 10
          fi
        done

    - name: Verify image in registry
      run: |
        echo "🔍 Verifying image was pushed successfully..."
        
        # Use Heroku API to check if image exists
        RESPONSE=$(curl -s -w "%{http_code}" \
          -H "Authorization: Bearer ${{ secrets.HEROKU_API_KEY }}" \
          -H "Accept: application/vnd.heroku+json; version=3" \
          "https://api.heroku.com/apps/pathforge-ai/formation/${{ steps.vars.outputs.PROCESS_TYPE }}")
        
        HTTP_CODE="${RESPONSE: -3}"
        if [[ "$HTTP_CODE" =~ ^2[0-9][0-9]$ ]]; then
          echo "✅ Image verification successful"
        else
          echo "⚠️ Image verification returned HTTP $HTTP_CODE"
        fi

    - name: Summary
      run: |
        echo "## 🐳 Heroku Direct Push Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** ${{ inputs.service }}" >> $GITHUB_STEP_SUMMARY
        echo "**Process Type:** ${{ steps.vars.outputs.PROCESS_TYPE }}" >> $GITHUB_STEP_SUMMARY
        echo "**Image:** \`${{ steps.vars.outputs.IMAGE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Status:** ✅ Successfully pushed to Heroku Container Registry" >> $GITHUB_STEP_SUMMARY
