name: Test SonarQube Connection

on:
  workflow_dispatch:
    inputs:
      test-type:
        description: 'Type of test to run'
        required: false
        type: choice
        options:
          - 'connectivity'
          - 'authentication'
          - 'minimal-scan'
        default: 'connectivity'

env:
  SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

jobs:
  test-connection:
    runs-on: ubuntu-latest
    name: Test SonarQube Connection
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Test SonarQube connectivity
      if: inputs.test-type == 'connectivity' || inputs.test-type == 'minimal-scan'
      run: |
        echo "🌐 Testing SonarQube server connectivity..."
        
        # Basic connectivity test
        echo "Testing: ${{ env.SONAR_HOST_URL }}"
        
        for i in {1..3}; do
          echo "Attempt $i/3..."
          if curl -s --connect-timeout 30 --max-time 60 "${{ env.SONAR_HOST_URL }}/api/system/status"; then
            echo "✅ Successfully connected to SonarQube server"
            break
          else
            echo "❌ Connection failed (attempt $i/3)"
            if [ $i -eq 3 ]; then
              echo "💥 Could not establish connection after 3 attempts"
              exit 1
            fi
            sleep 10
          fi
        done

    - name: Test SonarQube authentication
      if: inputs.test-type == 'authentication' || inputs.test-type == 'minimal-scan'
      run: |
        echo "🔐 Testing SonarQube authentication..."
        
        AUTH_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
          -H "Authorization: Bearer ${{ secrets.SONAR_TOKEN }}" \
          "${{ env.SONAR_HOST_URL }}/api/authentication/validate" || echo '{"valid":false}')
        
        echo "Auth response: $AUTH_RESPONSE"
        
        if echo "$AUTH_RESPONSE" | grep -q '"valid":true'; then
          echo "✅ Authentication is valid"
        else
          echo "❌ Authentication failed"
          exit 1
        fi

    - name: Install minimal SonarScanner for quick test
      if: inputs.test-type == 'minimal-scan'
      run: |
        echo "📦 Installing SonarScanner for minimal test..."
        
        # Download and install SonarScanner
        SCANNER_VERSION="6.2.1.4610"
        curl -L --connect-timeout 30 --max-time 300 \
          -o sonarscanner.zip \
          "https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SCANNER_VERSION}-linux-x64.zip"
        
        unzip -q sonarscanner.zip
        sudo mv "sonar-scanner-${SCANNER_VERSION}-linux-x64" /opt/sonar-scanner
        sudo ln -sf /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
        
        sonar-scanner --version

    - name: Run minimal SonarQube scan
      if: inputs.test-type == 'minimal-scan'
      run: |
        echo "🔍 Running minimal SonarQube scan test..."
        
        # Create a minimal test file to scan
        mkdir -p test-src
        echo "print('Hello, SonarQube!')" > test-src/test.py
        echo "console.log('Hello, SonarQube!');" > test-src/test.js
        
        # Run minimal scan with basic configuration
        sonar-scanner \
          -Dsonar.projectKey="test-connection-$(date +%s)" \
          -Dsonar.projectName="SonarQube Connection Test" \
          -Dsonar.sources=test-src \
          -Dsonar.host.url="${{ env.SONAR_HOST_URL }}" \
          -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
          -Dsonar.qualitygate.wait=false \
          -Dsonar.ws.timeout=120 \
          -Dhttp.socketTimeout=300000 \
          -Dhttp.connectionTimeout=60000
        
        echo "✅ Minimal scan completed successfully"
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        JAVA_OPTS: "-Xmx1024m"
      timeout-minutes: 10

    - name: Display results
      if: always()
      run: |
        echo "📊 Connection Test Results:"
        echo "Test type: ${{ inputs.test-type }}"
        echo "SonarQube URL: ${{ env.SONAR_HOST_URL }}"
        echo ""
        
        if [ "${{ job.status }}" = "success" ]; then
          echo "✅ All tests passed successfully!"
          echo ""
          echo "Your SonarQube setup is working correctly."
          echo "You can now run the full manual scan workflow."
        else
          echo "❌ Some tests failed."
          echo ""
          echo "Please check the logs above for details."
          echo "Common issues:"
          echo "- Network connectivity problems"
          echo "- Invalid SONAR_TOKEN"
          echo "- SonarQube server configuration issues"
        fi
