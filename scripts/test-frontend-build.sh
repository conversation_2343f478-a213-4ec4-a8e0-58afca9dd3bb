#!/bin/bash

# Test script for frontend Docker build
# This script helps test the frontend Docker build and provides debugging information

set -e

echo "🚀 Testing PathForge AI Frontend Docker Build"
echo "=============================================="

# Check if <PERSON><PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build the frontend image
echo "📦 Building frontend Docker image..."
docker build -f docker/Dockerfile.frontend -t pathforge-ai-frontend-test . || {
    echo "❌ Docker build failed!"
    echo ""
    echo "🔍 Debugging information:"
    echo "- Node.js version in Dockerfile: 22-alpine"
    echo "- Frontend Node.js requirement: $(cat src/frontend/.nvmrc)"
    echo "- Yarn version: $(yarn --version 2>/dev/null || echo 'Not installed')"
    echo ""
    echo "💡 Suggested fixes:"
    echo "1. Ensure Docker daemon is running"
    echo "2. Try clearing Docker build cache: docker builder prune"
    echo "3. Check if all frontend dependencies are properly declared"
    echo "4. Verify yarn.lock file is not corrupted"
    exit 1
}

echo "✅ Frontend Docker build completed successfully!"

# Test running the container
echo "🧪 Testing container startup..."
CONTAINER_ID=$(docker run -d -p 3001:8080 pathforge-ai-frontend-test)

# Wait a moment for startup
sleep 5

# Check if container is running
if docker ps | grep -q $CONTAINER_ID; then
    echo "✅ Container is running successfully!"
    echo "🌐 Frontend should be accessible at http://localhost:3001"
    
    # Test HTTP response
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001 | grep -q "200"; then
        echo "✅ HTTP endpoint is responding correctly!"
    else
        echo "⚠️  HTTP endpoint may not be fully ready yet (this is normal)"
    fi
else
    echo "❌ Container failed to start properly"
    docker logs $CONTAINER_ID
fi

# Cleanup
echo "🧹 Cleaning up test container..."
docker stop $CONTAINER_ID >/dev/null 2>&1 || true
docker rm $CONTAINER_ID >/dev/null 2>&1 || true

echo ""
echo "🎉 Frontend Docker build test completed!"
echo "📝 To manually test: docker run -p 3000:8080 pathforge-ai-frontend-test"
