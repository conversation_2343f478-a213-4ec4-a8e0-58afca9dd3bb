#!/bin/bash
#
# SonarQube Connection Test Script
# This script tests your SonarQube server connection and setup
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SONAR_HOST_URL="${SONAR_HOST_URL:-}"
SONAR_TOKEN="${SONAR_TOKEN:-}"

echo "🔍 SonarQube Connection Test"
echo "============================"

# Check if required variables are set
if [ -z "$SONAR_HOST_URL" ]; then
    echo -e "${RED}❌ SONAR_HOST_URL environment variable is not set${NC}"
    echo "Please set it with: export SONAR_HOST_URL=https://your-sonarqube-server.com"
    exit 1
fi

if [ -z "$SONAR_TOKEN" ]; then
    echo -e "${RED}❌ SONAR_TOKEN environment variable is not set${NC}"
    echo "Please set it with: export SONAR_TOKEN=your-sonar-token"
    exit 1
fi

echo "Host URL: $SONAR_HOST_URL"
echo "Token: ${SONAR_TOKEN:0:8}..."
echo ""

# Test 1: Basic connectivity
echo "🌐 Testing basic connectivity..."
if curl -s --connect-timeout 30 --max-time 60 "$SONAR_HOST_URL/api/system/status" > /tmp/sonar_status.json; then
    echo -e "${GREEN}✅ Server is reachable${NC}"
    echo "Server status:"
    cat /tmp/sonar_status.json | jq '.' 2>/dev/null || cat /tmp/sonar_status.json
else
    echo -e "${RED}❌ Cannot reach SonarQube server${NC}"
    exit 1
fi

echo ""

# Test 2: Authentication
echo "🔐 Testing authentication..."
AUTH_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
    -H "Authorization: Bearer $SONAR_TOKEN" \
    "$SONAR_HOST_URL/api/authentication/validate" 2>/dev/null || echo '{"valid":false}')

if echo "$AUTH_RESPONSE" | grep -q '"valid":true'; then
    echo -e "${GREEN}✅ Authentication is valid${NC}"
else
    echo -e "${RED}❌ Authentication failed${NC}"
    echo "Response: $AUTH_RESPONSE"
    exit 1
fi

echo ""

# Test 3: Project access
echo "📋 Testing project access..."
PROJECT_KEY="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
PROJECT_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
    -H "Authorization: Bearer $SONAR_TOKEN" \
    "$SONAR_HOST_URL/api/projects/search?projects=$PROJECT_KEY" 2>/dev/null || echo '{"components":[]}')

if echo "$PROJECT_RESPONSE" | grep -q "$PROJECT_KEY"; then
    echo -e "${GREEN}✅ Project is accessible${NC}"
else
    echo -e "${YELLOW}⚠️ Project may not exist or access is restricted${NC}"
    echo "This is normal if it's the first scan"
fi

echo ""

# Test 4: Check plugin availability
echo "🔌 Checking available plugins..."
PLUGINS_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
    -H "Authorization: Bearer $SONAR_TOKEN" \
    "$SONAR_HOST_URL/api/plugins/installed" 2>/dev/null || echo '{"plugins":[]}')

if echo "$PLUGINS_RESPONSE" | grep -q "python"; then
    echo -e "${GREEN}✅ Python plugin is available${NC}"
else
    echo -e "${YELLOW}⚠️ Python plugin may not be installed${NC}"
fi

if echo "$PLUGINS_RESPONSE" | grep -q "javascript"; then
    echo -e "${GREEN}✅ JavaScript/TypeScript plugin is available${NC}"
else
    echo -e "${YELLOW}⚠️ JavaScript/TypeScript plugin may not be installed${NC}"
fi

echo ""

# Test 5: Test file upload capability
echo "📤 Testing file upload capability..."
echo "test content" > /tmp/test_upload.txt
UPLOAD_RESPONSE=$(curl -s --connect-timeout 30 --max-time 60 \
    -H "Authorization: Bearer $SONAR_TOKEN" \
    -w "%{http_code}" \
    -o /dev/null \
    "$SONAR_HOST_URL/api/system/health" 2>/dev/null || echo "000")

if [ "$UPLOAD_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Server accepts requests${NC}"
else
    echo -e "${YELLOW}⚠️ Server health check returned: $UPLOAD_RESPONSE${NC}"
fi

rm -f /tmp/test_upload.txt /tmp/sonar_status.json

echo ""
echo -e "${GREEN}🎉 Connection test completed!${NC}"
echo ""
echo "📋 Summary:"
echo "- Server connectivity: ✅"
echo "- Authentication: ✅"
echo "- Ready for SonarQube scan"
echo ""
echo "💡 To run a local scan, use:"
echo "sonar-scanner -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.token=\$SONAR_TOKEN"
