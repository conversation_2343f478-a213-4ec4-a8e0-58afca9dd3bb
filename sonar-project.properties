# SonarQube Project Configuration
sonar.projectKey=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz
sonar.projectName=CodePluse Platform
sonar.projectVersion=1.0
sonar.organization=namnhcntt

# Source code settings - Multi-language project
sonar.sources=src
sonar.tests=tests,src/frontend/src/**/*.test.tsx,src/frontend/src/**/*.spec.tsx,src/backend/tests

# Python Agent Service Configuration
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=pytest-junit.xml

# TypeScript Frontend Configuration
sonar.typescript.lcov.reportPaths=src/frontend/coverage/lcov.info
sonar.javascript.lcov.reportPaths=src/frontend/coverage/lcov.info

# TypeScript Backend Configuration  
sonar.typescript.lcov.reportPaths=src/backend/coverage/lcov.info
sonar.javascript.lcov.reportPaths=src/backend/coverage/lcov.info

# Project-specific exclusions
sonar.exclusions=**/migrations/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/dist/**,**/build/**,**/*.pyc,**/vendor/**,**/target/**,**/streamlit*/**,src/streamlit_app.py,**/Dockerfile.streamlit,**/ai-tool-tests/**,**/.sonar/**,**/coverage/**,**/htmlcov/**

# Test exclusions
sonar.test.exclusions=**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/*.test.ts,**/*.spec.ts,**/*.test.tsx,**/*.spec.tsx

# Coverage exclusions (files to exclude from coverage calculation)
sonar.coverage.exclusions=**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/migrations/**,**/venv/**,src/frontend/src/**/*.test.ts,src/frontend/src/**/*.spec.ts,src/frontend/src/**/*.test.tsx,src/frontend/src/**/*.spec.tsx,src/backend/tests/**,src/backend/**/*.test.ts,src/backend/**/*.spec.ts,**/streamlit*/**,src/streamlit_app.py,**/ai-tool-tests/**

# Duplication exclusions
sonar.cpd.exclusions=**/migrations/**,**/tests/**,**/ai-tool-tests/**,**/streamlit*/**

# Language-specific settings
sonar.python.pylint.reportPaths=pylint-report.txt

# Encoding
sonar.sourceEncoding=UTF-8

# Quality Gate settings
sonar.qualitygate.wait=true

# Language detection and analysis
sonar.python.version=3.12

# Additional language-specific settings for multi-module project
sonar.javascript.environments=node
sonar.typescript.tsconfigPath=src/frontend/tsconfig.json,src/backend/tsconfig.json

# Scanner optimization settings
sonar.ws.timeout=300
sonar.scm.disabled=false

# Network settings for better stability
sonar.ce.javaOpts=-Xmx2g -Xms128m -XX:+HeapDumpOnOutOfMemoryError
sonar.web.javaOpts=-Xmx1g -Xms128m -XX:+HeapDumpOnOutOfMemoryError
sonar.scanner.force.timeout=true
sonar.scanner.skip.debug=true
