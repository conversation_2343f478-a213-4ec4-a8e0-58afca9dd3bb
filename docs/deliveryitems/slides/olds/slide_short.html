<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pathforge AI - HackAIthon 2025 (Short)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide { scroll-snap-align: start; }
        html { scroll-snap-type: y mandatory; }
    </style>
</head>
<body class="bg-gray-900 text-white overflow-x-hidden">

<!-- Slide 1: Project & Team Introduction -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-6xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full">
    <div class="col-span-5 flex flex-col justify-center">
      <h1 class="text-5xl font-bold text-white mb-6">Pathforge AI</h1>
      <div class="text-xl text-blue-200 mb-8">HackAIthon 2025 • Team CodePluse</div>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
          <span class="text-lg">AI-Powered Upskilling</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
          <span class="text-lg">6 Members Team</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-indigo-400 rounded-full"></div>
          <span class="text-lg">AI-First Solution</span>
        </div>
      </div>
    </div>
    <div class="col-span-7 flex items-center">
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full">
        <canvas id="teamChart" class="w-full max-h-80"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- Slide 2: The Problem & Solution -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-red-900 via-orange-900 to-yellow-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-6xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full">
    <div class="col-span-4 flex flex-col justify-center">
      <h1 class="text-4xl font-bold text-white mb-6">The Challenge</h1>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-red-400 rounded-full"></div>
          <span class="text-lg">Manual Skill Analysis</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-orange-400 rounded-full"></div>
          <span class="text-lg">Time-Consuming</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
          <span class="text-lg">Generic Roadmaps</span>
        </div>
      </div>
    </div>
    <div class="col-span-8 flex items-center">
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full">
        <canvas id="problemChart" class="w-full max-h-80"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- Slide 3: How it Works -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-6xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full">
    <div class="col-span-4 flex flex-col justify-center">
      <h1 class="text-4xl font-bold text-white mb-6">AI Pipeline</h1>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center text-sm font-bold text-black">1</div>
          <span class="text-lg">Goal Input</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-teal-400 rounded-full flex items-center justify-center text-sm font-bold text-black">2</div>
          <span class="text-lg">Skill Sync</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-sm font-bold text-black">3</div>
          <span class="text-lg">Gap Analysis</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-indigo-400 rounded-full flex items-center justify-center text-sm font-bold text-black">4</div>
          <span class="text-lg">Roadmap Gen</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center text-sm font-bold text-black">5</div>
          <span class="text-lg">Report Export</span>
        </div>
      </div>
    </div>
    <div class="col-span-8 flex items-center">
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full">
        <canvas id="pipelineChart" class="w-full max-h-80"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- Slide 4: Technology Stack -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-purple-900 via-pink-900 to-indigo-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-6xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full">
    <div class="col-span-4 flex flex-col justify-center">
      <h1 class="text-4xl font-bold text-white mb-6">Tech Stack</h1>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
          <span class="text-lg">LangGraph Agents</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-pink-400 rounded-full"></div>
          <span class="text-lg">FastAPI + React</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-indigo-400 rounded-full"></div>
          <span class="text-lg">PostgreSQL + Vector DB</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
          <span class="text-lg">Docker + Azure AKS</span>
        </div>
      </div>
    </div>
    <div class="col-span-8 flex items-center">
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full">
        <canvas id="techChart" class="w-full max-h-80"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- Slide 5: Impact & Future -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-emerald-900 via-green-900 to-teal-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-6xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full">
    <div class="col-span-4 flex flex-col justify-center">
      <h1 class="text-4xl font-bold text-white mb-6">Impact</h1>
      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-emerald-400 rounded-full"></div>
          <span class="text-lg">95% Test Coverage</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-green-400 rounded-full"></div>
          <span class="text-lg">Sub-15s Generation</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-teal-400 rounded-full"></div>
          <span class="text-lg">1000+ Users Ready</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
          <span class="text-lg">Production Ready</span>
        </div>
      </div>
    </div>
    <div class="col-span-8 flex items-center">
      <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full">
        <canvas id="impactChart" class="w-full max-h-80"></canvas>
      </div>
    </div>
  </div>
</div>

<!-- Slide 6: Thank You & Q&A -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-zinc-900 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-4xl mx-auto text-center h-full max-h-full flex flex-col justify-center">
    <h1 class="text-6xl font-bold text-white mb-8">Thank You</h1>
    <div class="text-2xl text-gray-300 mb-12">Questions & Answers</div>
    <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-12 mx-auto">
      <div class="text-4xl mb-6">🎯</div>
      <div class="text-xl text-gray-200 mb-4">Ready for Q&A</div>
      <div class="text-lg text-gray-400">Team CodePluse • Pathforge AI</div>
    </div>
  </div>
</div>

<script>
// Team Chart
const teamCtx = document.getElementById('teamChart').getContext('2d');
new Chart(teamCtx, {
  type: 'doughnut',
  data: {
    labels: ['AI Agents', 'Frontend', 'Backend', 'DevOps', 'Testing', 'Design'],
    datasets: [{
      data: [25, 20, 20, 15, 12, 8],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(99, 102, 241, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(249, 115, 22, 0.8)',
        'rgba(239, 68, 68, 0.8)'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    }
  }
});

// Problem Chart
const problemCtx = document.getElementById('problemChart').getContext('2d');
new Chart(problemCtx, {
  type: 'bar',
  data: {
    labels: ['Manual Process', 'Time Required', 'Accuracy', 'Scalability'],
    datasets: [{
      label: 'Current State',
      data: [90, 85, 60, 30],
      backgroundColor: 'rgba(239, 68, 68, 0.8)',
      borderColor: 'rgba(239, 68, 68, 1)',
      borderWidth: 1
    }, {
      label: 'With Pathforge AI',
      data: [10, 15, 95, 95],
      backgroundColor: 'rgba(34, 197, 94, 0.8)',
      borderColor: 'rgba(34, 197, 94, 1)',
      borderWidth: 1
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: { display: false },
      x: { display: false }
    }
  }
});

// Pipeline Chart
const pipelineCtx = document.getElementById('pipelineChart').getContext('2d');
new Chart(pipelineCtx, {
  type: 'line',
  data: {
    labels: ['Goal Input', 'Skill Sync', 'Gap Analysis', 'Roadmap Gen', 'Report Export'],
    datasets: [{
      label: 'Processing Time (seconds)',
      data: [2, 5, 8, 15, 3],
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgba(59, 130, 246, 1)',
      borderWidth: 3,
      fill: true,
      tension: 0.4
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: { display: false },
      x: { display: false }
    }
  }
});

// Tech Chart
const techCtx = document.getElementById('techChart').getContext('2d');
new Chart(techCtx, {
  type: 'radar',
  data: {
    labels: ['AI/ML', 'Backend', 'Frontend', 'Database', 'DevOps', 'Testing'],
    datasets: [{
      label: 'Implementation Level',
      data: [95, 90, 85, 88, 92, 85],
      backgroundColor: 'rgba(147, 51, 234, 0.2)',
      borderColor: 'rgba(147, 51, 234, 1)',
      borderWidth: 2
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      r: { display: false }
    }
  }
});

// Impact Chart
const impactCtx = document.getElementById('impactChart').getContext('2d');
new Chart(impactCtx, {
  type: 'bar',
  data: {
    labels: ['Test Coverage', 'Performance', 'Scalability', 'Production Ready'],
    datasets: [{
      data: [95, 88, 92, 90],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(249, 115, 22, 0.8)'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: { display: false },
      x: { display: false }
    }
  }
});
</script>

</body>
</html>
