<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technology & Achievements</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .slide {
            width: 95%;
            max-width: 1400px;
            background: white;
            border-radius: 20px;
            padding: 45px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #a29bfe, #6c5ce7, #fd79a8);
        }
        
        .header {
            text-align: center;
            margin-bottom: 35px;
        }
        
        .title {
            font-size: 2.6rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.1rem;
            color: #a29bfe;
            font-weight: 500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .tech-section {
            background: linear-gradient(135deg, #a29bfe15, #6c5ce715);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #a29bfe;
        }
        
        .section-title {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            width: 35px;
            height: 35px;
            background: #a29bfe;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }
        
        .tech-list {
            list-style: none;
        }
        
        .tech-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            font-size: 0.85rem;
            color: #495057;
            line-height: 1.4;
        }
        
        .tech-list li::before {
            content: '⚡';
            position: absolute;
            left: 0;
            font-size: 1rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 5px;
            border-radius: 3px;
            font-weight: 600;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            border-color: #a29bfe;
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #a29bfe;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.8rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .achievement-banner {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-top: 25px;
            box-shadow: 0 10px 20px rgba(0, 184, 148, 0.3);
        }
        
        .achievement-banner h3 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .achievement-banner p {
            font-size: 1rem;
            opacity: 0.95;
            line-height: 1.5;
        }
        
        .quality-badges {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .badge {
            background: #6c5ce7;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .badge-icon {
            margin-right: 5px;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="header">
            <h1 class="title">Core Technology & Achievements</h1>
            <p class="subtitle">3-Week Development Sprint Results</p>
        </div>
        
        <div class="content-grid">
            <div class="tech-section">
                <h3 class="section-title"><span class="section-icon">🤖</span>AI Technologies</h3>
                <ul class="tech-list">
                    <li><span class="highlight">LangGraph v0.3</span> multi-agent orchestration</li>
                    <li><span class="highlight">FastAPI</span> streaming service (port 8000)</li>
                    <li><span class="highlight">CV Extractor Agent</span> with JSON validation</li>
                    <li><span class="highlight">RAG System</span> PostgreSQL + PGVector + ChromaDB</li>
                    <li><span class="highlight">OpenAI embeddings</span> for semantic search</li>
                </ul>
            </div>
            
            <div class="tech-section">
                <h3 class="section-title"><span class="section-icon">🏗️</span>Tech Stack</h3>
                <ul class="tech-list">
                    <li><span class="highlight">Backend:</span> Node.js 22 + Express + TypeScript</li>
                    <li><span class="highlight">Frontend:</span> React 19.1 + Vite 6.3 + Mantine UI</li>
                    <li><span class="highlight">Database:</span> PostgreSQL with Prisma ORM</li>
                    <li><span class="highlight">Agent Service:</span> Python 3.12 + uv manager</li>
                    <li><span class="highlight">Deployment:</span> Docker + Azure AKS + CI/CD</li>
                </ul>
            </div>
            
            <div class="tech-section">
                <h3 class="section-title"><span class="section-icon">📊</span>Performance</h3>
                <ul class="tech-list">
                    <li><span class="highlight">Skill sync:</span> ≤ 5 seconds</li>
                    <li><span class="highlight">Roadmap generation:</span> ≤ 15 seconds</li>
                    <li><span class="highlight">Agent report:</span> ≤ 3 seconds</li>
                    <li><span class="highlight">Concurrent users:</span> 1,000+ supported</li>
                    <li><span class="highlight">Auto-scaling:</span> HPA at 70% CPU/80% memory</li>
                </ul>
            </div>
            
            <div class="tech-section">
                <h3 class="section-title"><span class="section-icon">🔒</span>Quality & Security</h3>
                <ul class="tech-list">
                    <li><span class="highlight">Backend coverage:</span> 95%+ test coverage</li>
                    <li><span class="highlight">SonarQube:</span> Multi-language analysis</li>
                    <li><span class="highlight">JWT authentication:</span> Security foundations</li>
                    <li><span class="highlight">Error handling:</span> Comprehensive recovery</li>
                    <li><span class="highlight">CI/CD:</span> GitHub Actions + Azure DevOps</li>
                </ul>
            </div>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">95%</div>
                <div class="metric-label">Backend Test Coverage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4</div>
                <div class="metric-label">Dockerized Services</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">5</div>
                <div class="metric-label">AI Agent Types</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">15s</div>
                <div class="metric-label">Max Response Time</div>
            </div>
        </div>
        
        <div class="quality-badges">
            <div class="badge">
                <span class="badge-icon">🚀</span>
                Production Ready
            </div>
            <div class="badge">
                <span class="badge-icon">🔧</span>
                Auto-scaling
            </div>
            <div class="badge">
                <span class="badge-icon">🛡️</span>
                Security Enabled
            </div>
            <div class="badge">
                <span class="badge-icon">📈</span>
                Performance Optimized
            </div>
        </div>
        
        <div class="achievement-banner">
            <h3>🎯 Production-Grade MVP Delivered</h3>
            <p>Complete AI pipeline with vector search, real-time streaming, comprehensive testing, and cloud-native deployment - all built in 3 weeks following enterprise standards.</p>
        </div>
    </div>
</body>
</html>
