<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Problem & AI Solution</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        
        .slide {
            width: 90%;
            max-width: 1200px;
            background: white;
            border-radius: 20px;
            padding: 60px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #ff9ff3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .title {
            font-size: 3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.2;
        }
        
        .subtitle {
            font-size: 1.3rem;
            color: #ff6b6b;
            font-weight: 500;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
        }
        
        .problem-solution {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        
        .problem, .solution {
            padding: 40px;
            border-radius: 15px;
            position: relative;
        }
        
        .problem {
            background: linear-gradient(135deg, #ff6b6b20, #feca5720);
            border-left: 5px solid #ff6b6b;
        }
        
        .solution {
            background: linear-gradient(135deg, #48cae420, #06ffa520);
            border-left: 5px solid #48cae4;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .problem .icon {
            background: #ff6b6b;
        }
        
        .solution .icon {
            background: #48cae4;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .pain-points {
            list-style: none;
            margin-top: 20px;
        }
        
        .pain-points li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            line-height: 1.6;
            color: #495057;
        }
        
        .pain-points li::before {
            content: '⚠️';
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }
        
        .solution-points {
            list-style: none;
            margin-top: 20px;
        }
        
        .solution-points li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            line-height: 1.6;
            color: #495057;
        }
        
        .solution-points li::before {
            content: '🤖';
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 600;
        }
        
        .ai-emphasis {
            background: #48cae4;
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
            box-shadow: 0 10px 20px rgba(72, 202, 228, 0.3);
        }
        
        .ai-emphasis h3 {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .ai-emphasis p {
            font-size: 1.2rem;
            opacity: 0.95;
        }
        
        .arrow {
            text-align: center;
            font-size: 3rem;
            color: #feca57;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="header">
            <h1 class="title">The Problem & AI Solution</h1>
            <p class="subtitle">Why AI is the only way forward</p>
        </div>
        
        <div class="content">
            <div class="problem-solution">
                <div class="problem">
                    <div class="section-header">
                        <div class="icon">⚠️</div>
                        <h2 class="section-title">Current Challenge</h2>
                    </div>
                    <ul class="pain-points">
                        <li>Manual skill gap identification is <span class="highlight">time-consuming</span></li>
                        <li>Generic learning paths don't fit <span class="highlight">individual needs</span></li>
                        <li>Complex process using <span class="highlight">traditional methods</span></li>
                        <li>Difficulty scaling personalized upskilling across <span class="highlight">large teams</span></li>
                        <li>Limited visibility into <span class="highlight">skill progression</span></li>
                    </ul>
                </div>
                
                <div class="solution">
                    <div class="section-header">
                        <div class="icon">🤖</div>
                        <h2 class="section-title">AI-First Solution</h2>
                    </div>
                    <ul class="solution-points">
                        <li><span class="highlight">AI Agents</span> automate entire analysis process</li>
                        <li>Process <span class="highlight">large skill datasets</span> at scale</li>
                        <li>Generate <span class="highlight">personalized roadmaps</span> instantly</li>
                        <li>Boost <span class="highlight">productivity</span> for users & support teams</li>
                        <li>Enable <span class="highlight">collaboration with AI</span> workflow</li>
                    </ul>
                </div>
            </div>
            
            <div class="arrow">↓</div>
            
            <div class="ai-emphasis">
                <h3>🚀 AI as the Only Way</h3>
                <p>Pathforge AI demonstrates <strong>applied AI into real outcomes</strong> - fostering FSOFT's AI culture while delivering practical productivity improvements through human-AI collaboration.</p>
            </div>
        </div>
    </div>
</body>
</html>
