<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVP Pipeline Demo Flow</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .slide {
            width: 95%;
            max-width: 1400px;
            background: white;
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #74b9ff, #0984e3, #a29bfe);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 2.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #74b9ff;
            font-weight: 500;
        }
        
        .pipeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .step {
            flex: 1;
            min-width: 200px;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            position: relative;
            border: 3px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border-color: #74b9ff;
        }
        
        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: #74b9ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .step-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .step-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .step-desc {
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 1.4;
        }
        
        .arrow {
            font-size: 2rem;
            color: #74b9ff;
            margin: 0 10px;
        }
        
        .tech-highlights {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .tech-section {
            background: linear-gradient(135deg, #74b9ff20, #0984e320);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #74b9ff;
        }
        
        .tech-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
        }
        
        .tech-icon {
            width: 30px;
            height: 30px;
            background: #74b9ff;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        .tech-list {
            list-style: none;
        }
        
        .tech-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            font-size: 0.9rem;
            color: #495057;
        }
        
        .tech-list li::before {
            content: '⚡';
            position: absolute;
            left: 0;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        
        .demo-badge {
            background: #00b894;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 20px;
            font-weight: 600;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="header">
            <h1 class="title">How It Works</h1>
            <p class="subtitle">MVP Pipeline Demo Flow - Built in 3 Weeks</p>
            <div class="demo-badge">🎯 Working Pipeline Ready for Demo</div>
        </div>
        
        <div class="pipeline">
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-icon">🎯</div>
                <div class="step-title">Goal Input</div>
                <div class="step-desc">React frontend with TypeScript validation & chat interface</div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-icon">🔄</div>
                <div class="step-title">Skill Sync</div>
                <div class="step-desc">Auto-aggregation from Jira, OKR, iMocha with PostgreSQL storage</div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-icon">🔍</div>
                <div class="step-title">Gap Analysis</div>
                <div class="step-desc">AI agents analyze gaps using RAG system with ChromaDB</div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-icon">🗺️</div>
                <div class="step-title">Roadmap Gen</div>
                <div class="step-desc">Course recommendation with external platform integration</div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-icon">📊</div>
                <div class="step-title">Report Export</div>
                <div class="step-desc">Markdown summaries with PDF export capability</div>
            </div>
        </div>
        
        <div class="tech-highlights">
            <div class="tech-section">
                <h3><span class="tech-icon">🔧</span>Core Technologies</h3>
                <ul class="tech-list">
                    <li><span class="highlight">LangGraph v0.3</span> multi-agent orchestration</li>
                    <li><span class="highlight">FastAPI</span> streaming endpoints (port 8000)</li>
                    <li><span class="highlight">PostgreSQL + PGVector</span> for embeddings</li>
                    <li><span class="highlight">ChromaDB</span> course database RAG</li>
                    <li><span class="highlight">React 19.1 + TypeScript</span> frontend</li>
                </ul>
            </div>
            
            <div class="tech-section">
                <h3><span class="tech-icon">⚡</span>Performance Features</h3>
                <ul class="tech-list">
                    <li><span class="highlight">Real-time processing</span> with WebSocket support</li>
                    <li><span class="highlight">State management</span> with conversation persistence</li>
                    <li><span class="highlight">Error handling</span> with comprehensive recovery</li>
                    <li><span class="highlight">Auto-scaling</span> ready for 1,000+ users</li>
                    <li><span class="highlight">Security</span> with JWT validation & RBAC</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
