<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PathForge AI - HackAIthon 2025 Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .slide {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .slide.active {
            display: block;
            opacity: 1;
        }
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body class="font-sans">
    <!-- Navigation -->
    <div class="fixed top-4 right-4 z-50 flex gap-2">
        <button id="prevBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i data-lucide="chevron-left"></i>
        </button>
        <span id="slideCounter" class="bg-gray-800 text-white px-4 py-2 rounded-lg">1 / 8</span>
        <button id="nextBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i data-lucide="chevron-right"></i>
        </button>
    </div>

    <!-- Slide 1: Welcome Slide -->
    <div class="slide active" data-slide="1">
        <div class="w-full h-screen bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-4xl mx-auto text-center text-white fade-in">
                <div class="mb-8">
                    <i data-lucide="zap" class="w-20 h-20 mx-auto mb-6 text-yellow-300"></i>
                </div>
                <h1 class="text-6xl font-bold mb-6">PathForge AI</h1>
                <p class="text-2xl mb-8">AI-Powered Personalized Upskill Solution for FSOFT</p>
                <div class="text-lg space-y-2">
                    <p class="flex items-center justify-center gap-2">
                        <i data-lucide="users" class="w-5 h-5"></i>
                        Team CodePluse
                    </p>
                    <p class="flex items-center justify-center gap-2">
                        <i data-lucide="calendar" class="w-5 h-5"></i>
                        HackAIthon 2025
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 2: Table of Contents -->
    <div class="slide" data-slide="2">
        <div class="w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-6xl mx-auto w-full fade-in">
                <h1 class="text-4xl font-bold text-gray-900 mb-12 text-center">Presentation Agenda</h1>
                <div class="grid grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div class="flex items-center gap-4 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                            <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Project & Team Introduction</h3>
                                <p class="text-sm text-gray-600">CodePluse Team & PathForge AI Overview</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors">
                            <div class="w-10 h-10 bg-indigo-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Problem & AI Solution</h3>
                                <p class="text-sm text-gray-600">Why PathForge AI vs External Solutions</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                            <div class="w-10 h-10 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">How it Works</h3>
                                <p class="text-sm text-gray-600">MVP Pipeline & Technical Implementation</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                            <div class="w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center font-bold">4</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">AI Technology & Architecture</h3>
                                <p class="text-sm text-gray-600">LangGraph, FastAPI, RAG System</p>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-6">
                        <div class="flex items-center gap-4 p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                            <div class="w-10 h-10 bg-yellow-600 text-white rounded-full flex items-center justify-center font-bold">5</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Development Metrics</h3>
                                <p class="text-sm text-gray-600">Code Quality, Performance, Cost Analysis</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                            <div class="w-10 h-10 bg-red-600 text-white rounded-full flex items-center justify-center font-bold">6</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Live Demo</h3>
                                <p class="text-sm text-gray-600">Interactive PathForge AI Features</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 p-4 bg-teal-50 rounded-lg hover:bg-teal-100 transition-colors">
                            <div class="w-10 h-10 bg-teal-600 text-white rounded-full flex items-center justify-center font-bold">7</div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Impact & Future Vision</h3>
                                <p class="text-sm text-gray-600">Business Impact & Post-HackAIthon Roadmap</p>
                            </div>
                        </div>
                        <div class="text-center p-4 bg-gray-100 rounded-lg">
                            <p class="text-sm text-gray-600"><i data-lucide="clock" class="w-4 h-4 inline mr-1"></i>Duration: 5 minutes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 3: Problem & Solution -->
    <div class="slide" data-slide="3">
        <div class="w-full h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">The Problem & AI Solution</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Why PathForge AI?</h2>
                    <div class="space-y-6">
                        <div class="flex items-start gap-3">
                            <i data-lucide="alert-triangle" class="w-6 h-6 text-red-500 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Current Challenge</h3>
                                <p class="text-sm text-gray-600">Manual skill gap analysis is time-consuming</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-3">
                            <i data-lucide="zap" class="w-6 h-6 text-blue-500 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">AI-First Solution</h3>
                                <p class="text-sm text-gray-600">Automated multi-agent processing</p>
                            </div>
                        </div>
                        <div class="flex items-start gap-3">
                            <i data-lucide="building" class="w-6 h-6 text-green-500 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">FSOFT Integration</h3>
                                <p class="text-sm text-gray-600">Ready for AkaJob & FHU systems</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold mb-4 text-center">PathForge AI vs External Solutions</h3>
                            <canvas id="comparisonChart" class="w-full" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 4: How it Works -->
    <div class="slide" data-slide="4">
        <div class="w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">How it Works</h1>
                    <h2 class="text-lg text-gray-600 mb-6">MVP Pipeline Flow</h2>
                    <div class="space-y-4 flex-1">
                        <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Goal Input</h4>
                                <p class="text-xs text-gray-600">Career goals & constraints</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Skill Sync</h4>
                                <p class="text-xs text-gray-600">Multi-source aggregation</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                            <div class="w-8 h-8 bg-yellow-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Gap Analysis</h4>
                                <p class="text-xs text-gray-600">AI-powered skill comparison</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Roadmap Generation</h4>
                                <p class="text-xs text-gray-600">Personalized learning paths</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                            <div class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Report Export</h4>
                                <p class="text-xs text-gray-600">Markdown & PDF output</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="bg-gray-50 rounded-lg p-6 w-full">
                        <h3 class="text-lg font-semibold mb-4 text-center">Pipeline Performance</h3>
                        <canvas id="pipelineChart" class="w-full" style="height: 320px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 5: AI Technology & Architecture -->
    <div class="slide" data-slide="5">
        <div class="w-full h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">AI Technology</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Core Architecture</h2>
                    <div class="space-y-4">
                        <div class="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
                            <i data-lucide="brain" class="w-6 h-6 text-purple-600"></i>
                            <div>
                                <h4 class="font-semibold">LangGraph</h4>
                                <p class="text-xs text-gray-600">Multi-agent orchestration</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
                            <i data-lucide="server" class="w-6 h-6 text-blue-600"></i>
                            <div>
                                <h4 class="font-semibold">FastAPI</h4>
                                <p class="text-xs text-gray-600">RESTful backend service</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
                            <i data-lucide="database" class="w-6 h-6 text-green-600"></i>
                            <div>
                                <h4 class="font-semibold">PostgreSQL</h4>
                                <p class="text-xs text-gray-600">Vector database & storage</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm">
                            <i data-lucide="monitor" class="w-6 h-6 text-indigo-600"></i>
                            <div>
                                <h4 class="font-semibold">React + Mantine</h4>
                                <p class="text-xs text-gray-600">Modern UI framework</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold mb-4 text-center">System Architecture</h3>
                            <div class="grid grid-cols-3 gap-4 h-80">
                                <div class="bg-blue-100 rounded-lg p-4 flex flex-col items-center justify-center">
                                    <i data-lucide="monitor" class="w-12 h-12 text-blue-600 mb-2"></i>
                                    <h4 class="font-semibold text-sm">Frontend</h4>
                                    <p class="text-xs text-gray-600 text-center">React + TypeScript</p>
                                </div>
                                <div class="bg-green-100 rounded-lg p-4 flex flex-col items-center justify-center">
                                    <i data-lucide="server" class="w-12 h-12 text-green-600 mb-2"></i>
                                    <h4 class="font-semibold text-sm">Backend</h4>
                                    <p class="text-xs text-gray-600 text-center">Express.js + PostgreSQL</p>
                                </div>
                                <div class="bg-purple-100 rounded-lg p-4 flex flex-col items-center justify-center">
                                    <i data-lucide="brain" class="w-12 h-12 text-purple-600 mb-2"></i>
                                    <h4 class="font-semibold text-sm">AI Service</h4>
                                    <p class="text-xs text-gray-600 text-center">LangGraph + FastAPI</p>
                                </div>
                                <div class="col-span-3 bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                                    <div class="text-center">
                                        <i data-lucide="shield" class="w-8 h-8 text-gray-600 mb-2 mx-auto"></i>
                                        <h4 class="font-semibold text-sm">Authentication: Supabase OAuth2</h4>
                                        <p class="text-xs text-gray-600">Ready for FSOFT SSO Integration</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 6: Development Metrics -->
    <div class="slide" data-slide="6">
        <div class="w-full h-screen bg-gray-50 p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto h-full max-h-full w-full fade-in">
                <div class="bg-white rounded-xl shadow-lg p-8 h-full max-h-full flex flex-col">
                    <div class="grid grid-cols-12 gap-8 h-full">
                        <div class="col-span-4 flex flex-col justify-start pt-8">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Development Metrics</h1>
                            <h2 class="text-lg text-gray-600 mb-6">Quality & Performance</h2>
                            <div class="space-y-4">
                                <div class="bg-blue-50 p-3 rounded-lg">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="code" class="w-4 h-4 text-blue-600"></i>
                                        <span class="font-semibold text-sm">Total LOC</span>
                                    </div>
                                    <p class="text-2xl font-bold text-blue-600">25,000</p>
                                </div>
                                <div class="bg-green-50 p-3 rounded-lg">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="git-commit" class="w-4 h-4 text-green-600"></i>
                                        <span class="font-semibold text-sm">Commits</span>
                                    </div>
                                    <p class="text-2xl font-bold text-green-600">347</p>
                                </div>
                                <div class="bg-yellow-50 p-3 rounded-lg">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="dollar-sign" class="w-4 h-4 text-yellow-600"></i>
                                        <span class="font-semibold text-sm">Monthly Cost</span>
                                    </div>
                                    <p class="text-2xl font-bold text-yellow-600">$364</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-8 flex flex-col pt-8">
                            <div class="grid grid-cols-12 gap-6 flex-1">
                                <div class="col-span-8">
                                    <div class="bg-gray-50 rounded-lg p-4 h-full">
                                        <h3 class="font-semibold mb-4">Code Quality Metrics</h3>
                                        <canvas id="qualityChart" style="height: 200px;"></canvas>
                                    </div>
                                </div>
                                <div class="col-span-4">
                                    <div class="space-y-4 h-full">
                                        <div class="bg-green-100 p-4 rounded-lg">
                                            <h4 class="font-semibold text-sm text-green-800">Backend Coverage</h4>
                                            <p class="text-xl font-bold text-green-700">95%+</p>
                                        </div>
                                        <div class="bg-orange-100 p-4 rounded-lg">
                                            <h4 class="font-semibold text-sm text-orange-800">Agent Service</h4>
                                            <p class="text-xl font-bold text-orange-700">15.2%</p>
                                        </div>
                                        <div class="bg-blue-100 p-4 rounded-lg">
                                            <h4 class="font-semibold text-sm text-blue-800">Functions Done</h4>
                                            <p class="text-xl font-bold text-blue-700">87%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 7: Live Demo -->
    <div class="slide" data-slide="7">
        <div class="w-full h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Live Demo</h1>
                    <h2 class="text-xl text-gray-600 mb-8">PathForge AI in Action</h2>
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center gap-3 mb-2">
                                <i data-lucide="user" class="w-5 h-5 text-blue-600"></i>
                                <h3 class="font-semibold">Demo Scenario</h3>
                            </div>
                            <p class="text-sm text-gray-600">Sarah: Senior Developer → AI/ML Engineer</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center gap-3 mb-2">
                                <i data-lucide="clock" class="w-5 h-5 text-green-600"></i>
                                <h3 class="font-semibold">Timeline</h3>
                            </div>
                            <p class="text-sm text-gray-600">6 months, 10 hours/week</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center gap-3 mb-2">
                                <i data-lucide="zap" class="w-5 h-5 text-purple-600"></i>
                                <h3 class="font-semibold">Performance</h3>
                            </div>
                            <p class="text-sm text-gray-600">Sub-15 second generation</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold mb-4 text-center">Demo Flow</h3>
                            <div class="space-y-3">
                                <div class="flex items-center gap-4 p-3 bg-blue-50 rounded-lg">
                                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                                        <i data-lucide="log-in" class="w-4 h-4"></i>
                                    </div>
                                    <span class="font-medium">Authentication & Goal Setting</span>
                                </div>
                                <div class="flex items-center gap-4 p-3 bg-green-50 rounded-lg">
                                    <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center">
                                        <i data-lucide="file-text" class="w-4 h-4"></i>
                                    </div>
                                    <span class="font-medium">CV Analysis & Skill Extraction</span>
                                </div>
                                <div class="flex items-center gap-4 p-3 bg-yellow-50 rounded-lg">
                                    <div class="w-8 h-8 bg-yellow-600 text-white rounded-full flex items-center justify-center">
                                        <i data-lucide="search" class="w-4 h-4"></i>
                                    </div>
                                    <span class="font-medium">AI Gap Analysis</span>
                                </div>
                                <div class="flex items-center gap-4 p-3 bg-purple-50 rounded-lg">
                                    <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center">
                                        <i data-lucide="map" class="w-4 h-4"></i>
                                    </div>
                                    <span class="font-medium">Personalized Roadmap Generation</span>
                                </div>
                                <div class="flex items-center gap-4 p-3 bg-red-50 rounded-lg">
                                    <div class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center">
                                        <i data-lucide="download" class="w-4 h-4"></i>
                                    </div>
                                    <span class="font-medium">Report Export & Sharing</span>
                                </div>
                            </div>
                            <div class="mt-6 text-center">
                                <button class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto">
                                    <i data-lucide="play" class="w-4 h-4"></i>
                                    Start Demo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: Impact & Future -->
    <div class="slide" data-slide="8">
        <div class="w-full h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-8 overflow-hidden" style="aspect-ratio: 16/9;">
            <div class="max-w-6xl mx-auto text-center text-white fade-in">
                <h1 class="text-5xl font-bold mb-8">Impact & Future Vision</h1>
                <div class="grid grid-cols-3 gap-8 mb-12">
                    <div class="bg-white/10 backdrop-blur rounded-lg p-6">
                        <i data-lucide="users" class="w-12 h-12 mx-auto mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">For Employees</h3>
                        <p class="text-sm">Personalized upskilling paths with clear career progression</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur rounded-lg p-6">
                        <i data-lucide="building" class="w-12 h-12 mx-auto mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">For FSOFT</h3>
                        <p class="text-sm">Enhanced productivity and strategic L&D insights</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur rounded-lg p-6">
                        <i data-lucide="trending-up" class="w-12 h-12 mx-auto mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Future Growth</h3>
                        <p class="text-sm">Scale to full solution within 3 months</p>
                    </div>
                </div>
                <div class="bg-white/10 backdrop-blur rounded-lg p-6 mb-8">
                    <h3 class="text-2xl font-semibold mb-4">Next Phase Features</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-left">
                            <ul class="space-y-2">
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>Deeper FSOFT system integration</li>
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>External learning platform connections</li>
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>Advanced skill progression tracking</li>
                            </ul>
                        </div>
                        <div class="text-left">
                            <ul class="space-y-2">
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>Team analysis & reporting</li>
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>RBAC & enterprise security</li>
                                <li class="flex items-center gap-2"><i data-lucide="check" class="w-4 h-4"></i>Mobile accessibility</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h2 class="text-3xl font-semibold mb-4">Thank You!</h2>
                <p class="text-xl mb-6">Questions & Discussion</p>
                <div class="flex items-center justify-center gap-8 text-lg">
                    <div class="flex items-center gap-2">
                        <i data-lucide="mail" class="w-5 h-5"></i>
                        <span>Team CodePluse</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i data-lucide="github" class="w-5 h-5"></i>
                        <span>PathForge AI</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Slide navigation
        let currentSlide = 1;
        const totalSlides = 8;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            const counter = document.getElementById('slideCounter');
            
            // Hide all slides
            slides.forEach(slide => {
                slide.classList.remove('active');
            });
            
            // Show current slide
            const targetSlide = document.querySelector(`[data-slide="${n}"]`);
            if (targetSlide) {
                targetSlide.classList.add('active');
                // Trigger fade-in animation
                const fadeElements = targetSlide.querySelectorAll('.fade-in');
                fadeElements.forEach(el => {
                    el.style.animation = 'none';
                    el.offsetHeight; // Trigger reflow
                    el.style.animation = 'fadeIn 0.6s ease-in';
                });
            }
            
            counter.textContent = `${n} / ${totalSlides}`;
            
            // Update button states
            document.getElementById('prevBtn').disabled = n === 1;
            document.getElementById('nextBtn').disabled = n === totalSlides;
        }

        function nextSlide() {
            if (currentSlide < totalSlides) {
                currentSlide++;
                showSlide(currentSlide);
                initializeSlideCharts(currentSlide);
            }
        }

        function prevSlide() {
            if (currentSlide > 1) {
                currentSlide--;
                showSlide(currentSlide);
                initializeSlideCharts(currentSlide);
            }
        }

        // Event listeners
        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('prevBtn').addEventListener('click', prevSlide);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                prevSlide();
            }
        });

        // Chart initialization
        function initializeSlideCharts(slideNumber) {
            if (slideNumber === 3) {
                initComparisonChart();
            } else if (slideNumber === 4) {
                initPipelineChart();
            } else if (slideNumber === 6) {
                initQualityChart();
            }
        }

        function initComparisonChart() {
            const ctx = document.getElementById('comparisonChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['FSOFT Integration', 'Internal Data', 'Customization', 'Performance', 'Cost Efficiency', 'Scalability'],
                    datasets: [{
                        label: 'PathForge AI',
                        data: [95, 90, 95, 85, 90, 88],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderWidth: 2
                    }, {
                        label: 'External Solutions',
                        data: [30, 40, 50, 70, 60, 65],
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function initPipelineChart() {
            const ctx = document.getElementById('pipelineChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Goal Input', 'Skill Sync', 'Gap Analysis', 'Roadmap Gen', 'Report Export'],
                    datasets: [{
                        label: 'Processing Time (seconds)',
                        data: [2, 5, 8, 12, 3],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(234, 179, 8, 0.8)',
                            'rgba(147, 51, 234, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ],
                        borderColor: [
                            'rgb(59, 130, 246)',
                            'rgb(34, 197, 94)',
                            'rgb(234, 179, 8)',
                            'rgb(147, 51, 234)',
                            'rgb(239, 68, 68)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Time (seconds)'
                            }
                        }
                    }
                }
            });
        }

        function initQualityChart() {
            const ctx = document.getElementById('qualityChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['TypeScript', 'Python', 'Config/Docs'],
                    datasets: [{
                        data: [63, 27, 10],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(234, 179, 8, 0.8)'
                        ],
                        borderColor: [
                            'rgb(59, 130, 246)',
                            'rgb(34, 197, 94)',
                            'rgb(234, 179, 8)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize first slide
        showSlide(1);
        initializeSlideCharts(1);
    </script>
</body>
</html>
