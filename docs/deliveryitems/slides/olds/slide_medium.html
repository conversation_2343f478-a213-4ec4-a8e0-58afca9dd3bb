<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pathforge AI - HackAIthon 2025 (Medium)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide { scroll-snap-align: start; }
        html { scroll-snap-type: y mandatory; }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 overflow-x-hidden">

<!-- Slide 1: Project & Team Introduction -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-7xl mx-auto h-full max-h-full">
    <div class="grid grid-cols-12 gap-12 h-full">
      <div class="col-span-6 flex flex-col justify-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Pathforge AI</h1>
        <h2 class="text-2xl text-blue-600 mb-8">HackAIthon 2025 • Team CodePluse</h2>
        <div class="space-y-6">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2">Project Goal</h3>
            <p class="text-gray-700">AI-Powered Personalized Upskill Solution for FSOFT employees using AI-first approach</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2">Team Composition</h3>
            <p class="text-gray-700">6 members: NamNH46, PhongTN2, TrungDD22, QuyetDB, DaiNQ11, TruongPH2</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2">3-Week MVP Target</h3>
            <p class="text-gray-700">Complete core pipeline: Goal input → Skill sync → Gap analysis → Roadmap generation → Report export</p>
          </div>
        </div>
      </div>
      <div class="col-span-6 flex items-center">
        <div class="bg-white rounded-lg p-6 w-full shadow-lg">
          <canvas id="teamOverviewChart" class="w-full max-h-96"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 2: The Problem & AI Solution -->
<div class="slide h-screen max-h-screen bg-white flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-7xl mx-auto h-full max-h-full">
    <div class="grid grid-cols-12 gap-12 h-full">
      <div class="col-span-6 flex flex-col justify-center">
        <h2 class="text-3xl font-semibold mb-6 text-gray-900">The Challenge</h2>
        <div class="space-y-4 flex-1">
          <div class="bg-red-50 border-l-4 border-red-500 p-4 rounded">
            <h3 class="font-semibold text-red-800 mb-2">Current Bottleneck</h3>
            <p class="text-red-700">Manual skill gap analysis and generic learning paths are time-consuming and ineffective</p>
          </div>
          <div class="bg-orange-50 border-l-4 border-orange-500 p-4 rounded">
            <h3 class="font-semibold text-orange-800 mb-2">Scale Limitation</h3>
            <p class="text-orange-700">Traditional methods cannot handle personalized roadmaps for thousands of employees</p>
          </div>
          <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded">
            <h3 class="font-semibold text-yellow-800 mb-2">Productivity Impact</h3>
            <p class="text-yellow-700">Employees spend too much time searching for relevant learning resources</p>
          </div>
          <div class="bg-blue-50 border-l-4 border-blue-500 p-4 rounded">
            <h3 class="font-semibold text-blue-800 mb-2">AI-First Solution</h3>
            <p class="text-blue-700">Pathforge AI automates the entire process using multi-agent AI system with RAG capabilities</p>
          </div>
        </div>
      </div>
      <div class="col-span-6 flex items-center">
        <div class="bg-gray-50 rounded-lg p-6 w-full">
          <canvas id="problemSolutionChart" class="w-full max-h-96"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 3: How it Works - MVP Pipeline -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-green-50 to-teal-100 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-7xl mx-auto h-full max-h-full">
    <div class="grid grid-cols-12 gap-12 h-full">
      <div class="col-span-6 flex flex-col justify-center">
        <h2 class="text-3xl font-semibold mb-6 text-gray-900">Core AI Pipeline</h2>
        <div class="space-y-4 flex-1 overflow-y-auto">
          <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-green-500">
            <h3 class="font-semibold text-lg mb-2">1. Goal & Constraint Input</h3>
            <p class="text-gray-700">React frontend with TypeScript validation, chat interface powered by LangGraph agents</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-teal-500">
            <h3 class="font-semibold text-lg mb-2">2. Skill Sync</h3>
            <p class="text-gray-700">Automatic aggregation from Jira, OKR, iMocha with CV Extractor Agent and PostgreSQL storage</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-blue-500">
            <h3 class="font-semibold text-lg mb-2">3. Gap Analysis</h3>
            <p class="text-gray-700">AI agents analyze skill gaps using RAG system with ChromaDB vector search</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-indigo-500">
            <h3 class="font-semibold text-lg mb-2">4. Roadmap Generation</h3>
            <p class="text-gray-700">Comprehensive roadmap with course recommendation engine and time constraint analysis</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm border-l-4 border-purple-500">
            <h3 class="font-semibold text-lg mb-2">5. Report Generation</h3>
            <p class="text-gray-700">Agent Report Generator creates markdown summaries with PDF export capability</p>
          </div>
        </div>
      </div>
      <div class="col-span-6 flex items-center">
        <div class="bg-white rounded-lg p-6 w-full shadow-lg">
          <canvas id="pipelineFlowChart" class="w-full max-h-96"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 4: Technology Stack & Achievements -->
<div class="slide h-screen max-h-screen bg-white flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-7xl mx-auto h-full max-h-full">
    <div class="grid grid-cols-12 gap-12 h-full">
      <div class="col-span-6 flex flex-col justify-center">
        <h2 class="text-3xl font-semibold mb-6 text-gray-900">Technical Excellence</h2>
        <div class="space-y-4 flex-1 overflow-y-auto">
          <div class="bg-purple-50 rounded-lg p-4">
            <h3 class="font-semibold text-lg mb-2 text-purple-800">AI Technologies</h3>
            <p class="text-purple-700">LangGraph v0.3 multi-agent framework, FastAPI service, RAG with PostgreSQL + PGVector</p>
          </div>
          <div class="bg-blue-50 rounded-lg p-4">
            <h3 class="font-semibold text-lg mb-2 text-blue-800">Full Stack</h3>
            <p class="text-blue-700">Node.js 22 + Express + TypeScript, React 19.1 + Vite 6.3, Python 3.12 + FastAPI</p>
          </div>
          <div class="bg-green-50 rounded-lg p-4">
            <h3 class="font-semibold text-lg mb-2 text-green-800">Performance Metrics</h3>
            <p class="text-green-700">Skill sync ≤ 5s, Roadmap generation ≤ 15s, 1,000+ concurrent users support</p>
          </div>
          <div class="bg-orange-50 rounded-lg p-4">
            <h3 class="font-semibold text-lg mb-2 text-orange-800">Quality & DevOps</h3>
            <p class="text-orange-700">95%+ backend test coverage, SonarQube integration, Docker + Azure AKS deployment</p>
          </div>
          <div class="bg-indigo-50 rounded-lg p-4">
            <h3 class="font-semibold text-lg mb-2 text-indigo-800">Production Ready</h3>
            <p class="text-indigo-700">Auto-scaling HPA, health checks, comprehensive logging, security patterns</p>
          </div>
        </div>
      </div>
      <div class="col-span-6 flex items-center">
        <div class="bg-gray-50 rounded-lg p-6 w-full">
          <canvas id="techStackChart" class="w-full max-h-96"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 5: Impact & Future -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-emerald-50 to-green-100 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-7xl mx-auto h-full max-h-full">
    <div class="grid grid-cols-12 gap-12 h-full">
      <div class="col-span-6 flex flex-col justify-center">
        <h2 class="text-3xl font-semibold mb-6 text-gray-900">Impact & Vision</h2>
        <div class="space-y-4 flex-1 overflow-y-auto">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2 text-emerald-800">For FSOFT Employees</h3>
            <p class="text-gray-700">Clear, personalized upskilling guide saving time on information search and empowering career growth</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2 text-green-800">For FSOFT Organization</h3>
            <p class="text-gray-700">Foster AI culture, enhance productivity, support skill management, provide L&D strategy data</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2 text-teal-800">Production Achievements</h3>
            <p class="text-gray-700">Fully containerized, Azure AKS ready, 95%+ test coverage, sub-15s generation time</p>
          </div>
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2 text-blue-800">Future Roadmap (3 months)</h3>
            <p class="text-gray-700">Complete advanced features, deeper integrations, external learning sources, advanced analytics</p>
          </div>
          <div class="bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-lg p-4 shadow-sm">
            <h3 class="font-semibold text-lg mb-2">AI-First Success</h3>
            <p>Demonstrating applied AI for real outcomes and AI collaboration culture</p>
          </div>
        </div>
      </div>
      <div class="col-span-6 flex items-center">
        <div class="bg-white rounded-lg p-6 w-full shadow-lg">
          <canvas id="impactMetricsChart" class="w-full max-h-96"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 6: Thank You & Q&A -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-slate-50 to-gray-100 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-4xl mx-auto text-center h-full max-h-full flex flex-col justify-center">
    <h1 class="text-6xl font-bold text-gray-900 mb-8">Thank You</h1>
    <div class="text-3xl text-gray-600 mb-12">Questions & Answers</div>
    <div class="bg-white rounded-2xl p-12 mx-auto shadow-xl border">
      <div class="text-6xl mb-6">🎯</div>
      <div class="text-2xl text-gray-800 mb-4">Ready for Your Questions</div>
      <div class="text-lg text-gray-600 mb-6">We're excited to discuss Pathforge AI with you</div>
      <div class="text-base text-gray-500">Team CodePluse • HackAIthon 2025</div>
    </div>
  </div>
</div>

<script>
// Team Overview Chart
const teamOverviewCtx = document.getElementById('teamOverviewChart').getContext('2d');
new Chart(teamOverviewCtx, {
  type: 'doughnut',
  data: {
    labels: ['AI/ML Development', 'Backend Services', 'Frontend Development', 'DevOps & Infrastructure', 'Testing & QA', 'Design & UX'],
    datasets: [{
      data: [30, 25, 20, 15, 8, 2],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(249, 115, 22, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(156, 163, 175, 0.8)'
      ],
      borderWidth: 2,
      borderColor: '#fff'
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { 
        display: true,
        position: 'bottom'
      }
    }
  }
});

// Problem vs Solution Chart
const problemSolutionCtx = document.getElementById('problemSolutionChart').getContext('2d');
new Chart(problemSolutionCtx, {
  type: 'bar',
  data: {
    labels: ['Time to Create Roadmap', 'Personalization Level', 'Scalability', 'Accuracy'],
    datasets: [{
      label: 'Traditional Approach',
      data: [240, 30, 20, 60],
      backgroundColor: 'rgba(239, 68, 68, 0.8)',
      borderColor: 'rgba(239, 68, 68, 1)',
      borderWidth: 1
    }, {
      label: 'Pathforge AI',
      data: [15, 95, 95, 90],
      backgroundColor: 'rgba(34, 197, 94, 0.8)',
      borderColor: 'rgba(34, 197, 94, 1)',
      borderWidth: 1
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { 
        display: true,
        position: 'top'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Performance Score'
        }
      }
    }
  }
});

// Pipeline Flow Chart
const pipelineFlowCtx = document.getElementById('pipelineFlowChart').getContext('2d');
new Chart(pipelineFlowCtx, {
  type: 'line',
  data: {
    labels: ['Goal Input', 'Skill Sync', 'Gap Analysis', 'Roadmap Generation', 'Report Export'],
    datasets: [{
      label: 'Processing Time (seconds)',
      data: [2, 5, 8, 15, 3],
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgba(59, 130, 246, 1)',
      borderWidth: 3,
      fill: true,
      tension: 0.4,
      pointBackgroundColor: 'rgba(59, 130, 246, 1)',
      pointBorderColor: '#fff',
      pointBorderWidth: 2,
      pointRadius: 6
    }, {
      label: 'Completion Rate (%)',
      data: [100, 95, 92, 90, 98],
      backgroundColor: 'rgba(34, 197, 94, 0.2)',
      borderColor: 'rgba(34, 197, 94, 1)',
      borderWidth: 3,
      fill: false,
      tension: 0.4,
      pointBackgroundColor: 'rgba(34, 197, 94, 1)',
      pointBorderColor: '#fff',
      pointBorderWidth: 2,
      pointRadius: 6,
      yAxisID: 'y1'
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { 
        display: true,
        position: 'top'
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Time (seconds)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Completion Rate (%)'
        },
        grid: {
          drawOnChartArea: false,
        },
      }
    }
  }
});

// Tech Stack Chart
const techStackCtx = document.getElementById('techStackChart').getContext('2d');
new Chart(techStackCtx, {
  type: 'radar',
  data: {
    labels: ['AI/ML Integration', 'Backend Architecture', 'Frontend Experience', 'Database Design', 'DevOps & Deployment', 'Testing & Quality'],
    datasets: [{
      label: 'Implementation Completeness (%)',
      data: [95, 90, 85, 88, 92, 85],
      backgroundColor: 'rgba(147, 51, 234, 0.2)',
      borderColor: 'rgba(147, 51, 234, 1)',
      borderWidth: 2,
      pointBackgroundColor: 'rgba(147, 51, 234, 1)',
      pointBorderColor: '#fff',
      pointBorderWidth: 2
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { 
        display: true,
        position: 'top'
      }
    },
    scales: {
      r: {
        angleLines: {
          display: true
        },
        suggestedMin: 0,
        suggestedMax: 100,
        pointLabels: {
          font: {
            size: 12
          }
        }
      }
    }
  }
});

// Impact Metrics Chart
const impactMetricsCtx = document.getElementById('impactMetricsChart').getContext('2d');
new Chart(impactMetricsCtx, {
  type: 'bar',
  data: {
    labels: ['Test Coverage', 'Performance Score', 'Scalability Rating', 'Production Readiness', 'User Experience', 'AI Accuracy'],
    datasets: [{
      data: [95, 88, 92, 90, 87, 93],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(249, 115, 22, 0.8)',
        'rgba(99, 102, 241, 0.8)',
        'rgba(236, 72, 153, 0.8)'
      ],
      borderColor: [
        'rgba(34, 197, 94, 1)',
        'rgba(59, 130, 246, 1)',
        'rgba(147, 51, 234, 1)',
        'rgba(249, 115, 22, 1)',
        'rgba(99, 102, 241, 1)',
        'rgba(236, 72, 153, 1)'
      ],
      borderWidth: 2
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Achievement Score (%)'
        }
      }
    }
  }
});
</script>

</body>
</html>
