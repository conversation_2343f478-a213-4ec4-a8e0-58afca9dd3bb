<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pathforge AI - HackAIthon 2025 Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body, html {
            height: 100%;
            overflow: hidden;
        }
        .slide {
            display: none;
            height: 100vh;
            max-height: 100vh;
            overflow: hidden;
        }
        .slide.active {
            display: flex;
            flex-direction: column;
        }
        .slide-transition {
            transition: all 0.3s ease-in-out;
        }
        .animate-fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }
        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 1rem;
            max-height: calc(100vh - 2rem);
            overflow: hidden;
        }
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chart-container {
                height: 150px;
            }
            .slide-content {
                padding: 0.5rem;
                max-height: calc(100vh - 1rem);
            }
            /* Reduce text sizes on mobile */
            .mobile-text-sm {
                font-size: 0.75rem;
            }
            .mobile-title-sm {
                font-size: 1.5rem;
            }
        }
        @media (max-height: 800px) {
            .chart-container {
                height: 200px;
            }
            /* Compact spacing for shorter screens */
            .compact-spacing > * {
                margin-bottom: 0.5rem !important;
            }
        }
        @media (max-height: 600px) {
            .chart-container {
                height: 150px;
            }
            .compact-spacing > * {
                margin-bottom: 0.25rem !important;
            }
        }
    </style>
</head>
<body class="font-sans bg-gray-50">

<!-- Navigation -->
<nav class="fixed top-4 right-4 z-50 flex space-x-2">
    <button onclick="previousSlide()" class="bg-white shadow-lg rounded-full p-2 hover:bg-gray-100 transition-colors">
        <i data-lucide="chevron-left" class="w-5 h-5"></i>
    </button>
    <div class="bg-white shadow-lg rounded-full px-4 py-2 text-sm font-medium">
        <span id="currentSlide">1</span> / <span id="totalSlides">5</span>
    </div>
    <button onclick="nextSlide()" class="bg-white shadow-lg rounded-full p-2 hover:bg-gray-100 transition-colors">
        <i data-lucide="chevron-right" class="w-5 h-5"></i>
    </button>
</nav>

<!-- Slide 1: Project & Team Introduction -->
<div class="slide active bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="slide-content">
        <div class="max-w-7xl mx-auto h-full flex flex-col justify-center">
            <!-- Project Header -->
            <div class="text-center mb-4 animate-fade-in compact-spacing">
                <div class="flex items-center justify-center mb-2">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-2 mr-3">
                        <i data-lucide="zap" class="w-6 h-6 lg:w-8 lg:h-8 text-white"></i>
                    </div>
                    <h1 class="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mobile-title-sm">Pathforge AI</h1>
                </div>
                <p class="text-lg lg:text-xl text-gray-600 mb-3">AI-Powered Personalized Upskill Solution for FSOFT</p>
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg px-4 py-1 inline-block">
                    <span class="font-semibold text-sm lg:text-base">HackAIthon 2025 • Team CodePluse</span>
                </div>
            </div>

            <!-- Team Members Section -->
            <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 mb-4 animate-fade-in compact-spacing flex-1 min-h-0">
                <h2 class="text-lg lg:text-xl font-bold text-gray-900 mb-3 text-center">Meet Our Team</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 lg:gap-3 mb-4">
                    <!-- Team Member Cards -->
                    <div class="bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-blue-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">NamNH46</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Senior Dev</p>
                    </div>
                    
                    <div class="bg-gradient-to-br from-green-100 to-green-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-green-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">PhongTN2</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Junior Dev</p>
                    </div>
                    
                    <div class="bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-purple-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">TrungDD22</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Senior Dev</p>
                    </div>
                    
                    <div class="bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-yellow-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">QuyetDB</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Junior Dev</p>
                    </div>
                    
                    <div class="bg-gradient-to-br from-red-100 to-red-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-red-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">DaiNQ11</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Senior Dev</p>
                    </div>
                    
                    <div class="bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-lg p-2 lg:p-3 text-center transform hover:scale-105 transition-transform">
                        <div class="bg-indigo-500 rounded-full w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center mx-auto mb-1">
                            <i data-lucide="user" class="w-4 h-4 lg:w-5 lg:h-5 text-white"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 text-xs lg:text-sm mobile-text-sm">TruongPH2</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Junior Dev</p>
                    </div>
                </div>
                
                <div class="flex justify-center space-x-4 lg:space-x-6 text-center">
                    <div class="bg-blue-50 rounded-lg px-2 lg:px-3 py-1">
                        <div class="text-lg lg:text-xl font-bold text-blue-600">6</div>
                        <div class="text-xs text-gray-600 mobile-text-sm">Team Members</div>
                    </div>
                    <div class="bg-green-50 rounded-lg px-2 lg:px-3 py-1">
                        <div class="text-lg lg:text-xl font-bold text-green-600">50%</div>
                        <div class="text-xs text-gray-600 mobile-text-sm">Senior Mix</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg px-2 lg:px-3 py-1">
                        <div class="text-lg lg:text-xl font-bold text-purple-600">3</div>
                        <div class="text-xs text-gray-600 mobile-text-sm">Weeks Sprint</div>
                    </div>
                </div>
            </div>

            <!-- MVP Pipeline Flow -->
            <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 animate-fade-in compact-spacing flex-1 min-h-0 overflow-hidden">
                <h2 class="text-lg lg:text-xl font-bold text-gray-900 mb-3 text-center">MVP Pipeline Flow</h2>
                
                <!-- Pipeline Steps -->
                <div class="relative mb-3">
                    <!-- Connecting Lines -->
                    <div class="hidden lg:block absolute top-12 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-300 via-green-300 via-yellow-300 via-purple-300 to-red-300"></div>
                    
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 lg:gap-4">
                        <!-- Pipeline steps with reduced content -->
                        <div class="relative bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 lg:p-4 text-center transform hover:scale-105 transition-transform">
                            <div class="bg-blue-500 rounded-full w-10 h-10 lg:w-12 lg:h-12 flex items-center justify-center mx-auto mb-2">
                                <i data-lucide="target" class="w-5 h-5 lg:w-6 lg:h-6 text-white"></i>
                            </div>
                            <h3 class="font-bold text-gray-900 mb-1 text-xs lg:text-sm">Goal Input</h3>
                            <p class="text-xs text-gray-600 mobile-text-sm">React + TypeScript</p>
                            <div class="bg-blue-500 text-white text-xs px-1 py-0.5 rounded-full inline-block mt-1">Step 1</div>
                        </div>
                        
                        <div class="relative bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 lg:p-4 text-center transform hover:scale-105 transition-transform">
                            <div class="bg-green-500 rounded-full w-10 h-10 lg:w-12 lg:h-12 flex items-center justify-center mx-auto mb-2">
                                <i data-lucide="sync" class="w-5 h-5 lg:w-6 lg:h-6 text-white"></i>
                            </div>
                            <h3 class="font-bold text-gray-900 mb-1 text-xs lg:text-sm">Skill Sync</h3>
                            <p class="text-xs text-gray-600 mobile-text-sm">Multi-source</p>
                            <div class="bg-green-500 text-white text-xs px-1 py-0.5 rounded-full inline-block mt-1">Step 2</div>
                        </div>
                        
                        <div class="relative bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-3 lg:p-4 text-center transform hover:scale-105 transition-transform">
                            <div class="bg-yellow-500 rounded-full w-10 h-10 lg:w-12 lg:h-12 flex items-center justify-center mx-auto mb-2">
                                <i data-lucide="search" class="w-5 h-5 lg:w-6 lg:h-6 text-white"></i>
                            </div>
                            <h3 class="font-bold text-gray-900 mb-1 text-xs lg:text-sm">Gap Analysis</h3>
                            <p class="text-xs text-gray-600 mobile-text-sm">AI + RAG</p>
                            <div class="bg-yellow-500 text-white text-xs px-1 py-0.5 rounded-full inline-block mt-1">Step 3</div>
                        </div>
                        
                        <div class="relative bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-3 lg:p-4 text-center transform hover:scale-105 transition-transform">
                            <div class="bg-purple-500 rounded-full w-10 h-10 lg:w-12 lg:h-12 flex items-center justify-center mx-auto mb-2">
                                <i data-lucide="map" class="w-5 h-5 lg:w-6 lg:h-6 text-white"></i>
                            </div>
                            <h3 class="font-bold text-gray-900 mb-1 text-xs lg:text-sm">Roadmap Gen</h3>
                            <p class="text-xs text-gray-600 mobile-text-sm">Course Engine</p>
                            <div class="bg-purple-500 text-white text-xs px-1 py-0.5 rounded-full inline-block mt-1">Step 4</div>
                        </div>
                        
                        <div class="relative bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-3 lg:p-4 text-center transform hover:scale-105 transition-transform">
                            <div class="bg-red-500 rounded-full w-10 h-10 lg:w-12 lg:h-12 flex items-center justify-center mx-auto mb-2">
                                <i data-lucide="file-text" class="w-5 h-5 lg:w-6 lg:h-6 text-white"></i>
                            </div>
                            <h3 class="font-bold text-gray-900 mb-1 text-xs lg:text-sm">Report Export</h3>
                            <p class="text-xs text-gray-600 mobile-text-sm">MD + PDF</p>
                            <div class="bg-red-500 text-white text-xs px-1 py-0.5 rounded-full inline-block mt-1">Step 5</div>
                        </div>
                    </div>
                </div>
                
                <!-- Goals Summary -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 lg:gap-3">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-2 lg:p-3 text-center">
                        <i data-lucide="brain" class="w-5 h-5 lg:w-6 lg:h-6 text-blue-600 mx-auto mb-1"></i>
                        <h4 class="font-semibold text-gray-900 mb-1 text-xs lg:text-sm">AI-First Approach</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Leveraging AI as the only way</p>
                    </div>
                    
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-2 lg:p-3 text-center">
                        <i data-lucide="target" class="w-5 h-5 lg:w-6 lg:h-6 text-green-600 mx-auto mb-1"></i>
                        <h4 class="font-semibold text-gray-900 mb-1 text-xs lg:text-sm">Real FSOFT Bottleneck</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Solving actual challenges</p>
                    </div>
                    
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-2 lg:p-3 text-center">
                        <i data-lucide="gauge" class="w-5 h-5 lg:w-6 lg:h-6 text-purple-600 mx-auto mb-1"></i>
                        <h4 class="font-semibold text-gray-900 mb-1 text-xs lg:text-sm">50% Core MVP</h4>
                        <p class="text-xs text-gray-600 mobile-text-sm">Working prototype in 3 weeks</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Slide 2: The Problem & AI Solution -->
<div class="slide bg-gradient-to-br from-red-50 to-orange-100">
    <div class="slide-content">
        <div class="max-w-7xl mx-auto h-full flex flex-col justify-center compact-spacing">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center mb-6">
                <div class="animate-fade-in">
                    <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 h-full">
                        <div class="flex items-center mb-4">
                            <div class="bg-red-500 rounded-xl p-2 mr-3">
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-white"></i>
                            </div>
                            <h2 class="text-xl lg:text-2xl font-bold text-gray-900">The Problem</h2>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex items-start space-x-3">
                                <div class="bg-red-100 rounded-full p-1 mt-1">
                                    <i data-lucide="clock" class="w-3 h-3 text-red-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">Time-consuming manual skill gap identification</p>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="bg-red-100 rounded-full p-1 mt-1">
                                    <i data-lucide="users" class="w-3 h-3 text-red-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">Complex personalized learning path creation</p>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="bg-red-100 rounded-full p-1 mt-1">
                                    <i data-lucide="trending-down" class="w-3 h-3 text-red-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">Inefficient traditional upskilling methods</p>
                            </div>
                        </div>
                        
                        <div class="bg-red-50 rounded-lg p-3">
                            <p class="text-xs lg:text-sm text-red-800 font-medium">Current bottleneck affecting FSOFT employee productivity and growth</p>
                        </div>
                    </div>
                </div>
                
                <div class="animate-fade-in">
                    <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 h-full">
                        <div class="flex items-center mb-4">
                            <div class="bg-green-500 rounded-xl p-2 mr-3">
                                <i data-lucide="sparkles" class="w-5 h-5 text-white"></i>
                            </div>
                            <h2 class="text-xl lg:text-2xl font-bold text-gray-900">AI Solution</h2>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex items-start space-x-3">
                                <div class="bg-green-100 rounded-full p-1 mt-1">
                                    <i data-lucide="cpu" class="w-3 h-3 text-green-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">AI-First automated analysis and roadmap generation</p>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="bg-green-100 rounded-full p-1 mt-1">
                                    <i data-lucide="zap" class="w-3 h-3 text-green-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">Multi-agent orchestration with LangGraph</p>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="bg-green-100 rounded-full p-1 mt-1">
                                    <i data-lucide="trending-up" class="w-3 h-3 text-green-600"></i>
                                </div>
                                <p class="text-sm lg:text-base text-gray-700">Scalable personalized learning at enterprise level</p>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 rounded-lg p-3">
                            <p class="text-xs lg:text-sm text-green-800 font-medium">Boosting productivity through AI collaboration - the only way forward</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 animate-fade-in">
                <h3 class="text-lg font-semibold mb-3 text-center text-gray-900">Problem vs Solution Impact</h3>
                <div class="chart-container">
                    <canvas id="impactChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Slide 3: How it Works (MVP Pipeline Demo Flow) -->
<div class="slide bg-gradient-to-br from-purple-50 to-pink-100">
    <div class="slide-content">
        <div class="max-w-7xl mx-auto h-full flex flex-col justify-center compact-spacing">
            <div class="text-center mb-4 animate-fade-in">
                <h1 class="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">MVP Pipeline Flow</h1>
                <p class="text-lg lg:text-xl text-gray-600">Complete working pipeline built in 3 weeks</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 lg:gap-3 mb-4">
                <!-- Pipeline Steps - Simplified for height constraints -->
                <div class="bg-white rounded-xl shadow-lg p-2 lg:p-3 transform hover:scale-105 transition-transform animate-fade-in">
                    <div class="bg-blue-500 rounded-lg p-2 mb-2">
                        <i data-lucide="target" class="w-4 h-4 lg:w-5 lg:h-5 text-white mx-auto"></i>
                    </div>
                    <h3 class="font-semibold text-xs lg:text-sm mb-1">Goal Input</h3>
                    <p class="text-xs text-gray-600 mobile-text-sm">React + TypeScript</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-2 lg:p-3 transform hover:scale-105 transition-transform animate-fade-in">
                    <div class="bg-green-500 rounded-lg p-2 mb-2">
                        <i data-lucide="sync" class="w-4 h-4 lg:w-5 lg:h-5 text-white mx-auto"></i>
                    </div>
                    <h3 class="font-semibold text-xs lg:text-sm mb-1">Skill Sync</h3>
                    <p class="text-xs text-gray-600 mobile-text-sm">Multi-source PostgreSQL</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-2 lg:p-3 transform hover:scale-105 transition-transform animate-fade-in">
                    <div class="bg-yellow-500 rounded-lg p-2 mb-2">
                        <i data-lucide="search" class="w-4 h-4 lg:w-5 lg:h-5 text-white mx-auto"></i>
                    </div>
                    <h3 class="font-semibold text-xs lg:text-sm mb-1">Gap Analysis</h3>
                    <p class="text-xs text-gray-600 mobile-text-sm">AI agents + RAG</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-2 lg:p-3 transform hover:scale-105 transition-transform animate-fade-in">
                    <div class="bg-purple-500 rounded-lg p-2 mb-2">
                        <i data-lucide="map" class="w-4 h-4 lg:w-5 lg:h-5 text-white mx-auto"></i>
                    </div>
                    <h3 class="font-semibold text-xs lg:text-sm mb-1">Roadmap Gen</h3>
                    <p class="text-xs text-gray-600 mobile-text-sm">Course engine</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-2 lg:p-3 transform hover:scale-105 transition-transform animate-fade-in">
                    <div class="bg-red-500 rounded-lg p-2 mb-2">
                        <i data-lucide="file-text" class="w-4 h-4 lg:w-5 lg:h-5 text-white mx-auto"></i>
                    </div>
                    <h3 class="font-semibold text-xs lg:text-sm mb-1">Report Export</h3>
                    <p class="text-xs text-gray-600 mobile-text-sm">Markdown + PDF</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 flex-1 min-h-0">
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 animate-fade-in">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900">Technical Implementation</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="bg-blue-100 rounded-full p-1">
                                <i data-lucide="activity" class="w-3 h-3 text-blue-600"></i>
                            </div>
                            <span class="mobile-text-sm">FastAPI streaming with WebSocket support</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-green-100 rounded-full p-1">
                                <i data-lucide="database" class="w-3 h-3 text-green-600"></i>
                            </div>
                            <span class="mobile-text-sm">LangGraph state persistence in PostgreSQL</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-yellow-100 rounded-full p-1">
                                <i data-lucide="shield" class="w-3 h-3 text-yellow-600"></i>
                            </div>
                            <span class="mobile-text-sm">Comprehensive error recovery & retry logic</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-purple-100 rounded-full p-1">
                                <i data-lucide="gauge" class="w-3 h-3 text-purple-600"></i>
                            </div>
                            <span class="mobile-text-sm">Connection pooling & async processing</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-6 animate-fade-in">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900">Performance Targets</h3>
                    <div class="chart-container">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Slide 4: Core AI Technology & Achievements -->
<div class="slide bg-gradient-to-br from-emerald-50 to-teal-100">
    <div class="slide-content">
        <div class="max-w-7xl mx-auto h-full flex flex-col justify-center compact-spacing">
            <div class="text-center mb-4 animate-fade-in">
                <h1 class="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">AI Technology Stack & Achievements</h1>
                <p class="text-lg lg:text-xl text-gray-600">Production-ready architecture with comprehensive testing</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-4 mb-4">
                <div class="bg-white rounded-2xl shadow-xl p-3 lg:p-4 animate-fade-in">
                    <div class="flex items-center mb-3">
                        <div class="bg-blue-500 rounded-lg p-1 mr-2">
                            <i data-lucide="cpu" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm lg:text-base">Core AI Technologies</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm mobile-text-sm">
                        <p>• LangGraph v0.3 Multi-Agent Framework</p>
                        <p>• FastAPI Service (Port 8000)</p>
                        <p>• RAG with PostgreSQL + PGVector</p>
                        <p>• ChromaDB for Course Database</p>
                        <p>• OpenAI Embeddings</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-3 lg:p-4 animate-fade-in">
                    <div class="flex items-center mb-3">
                        <div class="bg-green-500 rounded-lg p-1 mr-2">
                            <i data-lucide="layers" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm lg:text-base">Tech Stack</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm mobile-text-sm">
                        <p>• Node.js 22 + Express + TypeScript</p>
                        <p>• React 19.1 + Vite 6.3 + Mantine UI</p>
                        <p>• Python 3.12 + LangGraph + FastAPI</p>
                        <p>• Docker + Azure AKS + HPA</p>
                        <p>• PostgreSQL with Prisma ORM</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-3 lg:p-4 animate-fade-in">
                    <div class="flex items-center mb-3">
                        <div class="bg-purple-500 rounded-lg p-1 mr-2">
                            <i data-lucide="gauge" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm lg:text-base">Performance Metrics</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm mobile-text-sm">
                        <p>• Skill Sync: ≤ 5s</p>
                        <p>• Roadmap Generation: ≤ 15s</p>
                        <p>• Agent Report: ≤ 3s</p>
                        <p>• Support: 1,000+ concurrent users</p>
                        <p>• Auto-scaling: 1-5 replicas</p>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 flex-1 min-h-0">
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-5 animate-fade-in">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900">Code Quality & Testing</h3>
                    <div class="chart-container mb-3">
                        <canvas id="qualityChart"></canvas>
                    </div>
                    <div class="grid grid-cols-3 gap-2 text-center">
                        <div class="bg-green-50 rounded-lg p-1">
                            <div class="text-sm lg:text-base font-bold text-green-600">95%</div>
                            <div class="text-xs text-gray-600 mobile-text-sm">Backend</div>
                        </div>
                        <div class="bg-yellow-50 rounded-lg p-1">
                            <div class="text-sm lg:text-base font-bold text-yellow-600">29%</div>
                            <div class="text-xs text-gray-600 mobile-text-sm">Frontend</div>
                        </div>
                        <div class="bg-red-50 rounded-lg p-1">
                            <div class="text-sm lg:text-base font-bold text-red-600">15%</div>
                            <div class="text-xs text-gray-600 mobile-text-sm">Agent</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-5 animate-fade-in">
                    <h3 class="text-lg font-semibold mb-3 text-gray-900">AI Integration Achievements</h3>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <div class="bg-blue-100 rounded-full p-1">
                                <i data-lucide="file-check" class="w-3 h-3 text-blue-600"></i>
                            </div>
                            <span class="text-xs lg:text-sm mobile-text-sm">Resume Processing Pipeline with JSON validation</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-green-100 rounded-full p-1">
                                <i data-lucide="map" class="w-3 h-3 text-green-600"></i>
                            </div>
                            <span class="text-xs lg:text-sm mobile-text-sm">Dual-mode Roadmap Generation (Simple/Advisor)</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-purple-100 rounded-full p-1">
                                <i data-lucide="search" class="w-3 h-3 text-purple-600"></i>
                            </div>
                            <span class="text-xs lg:text-sm mobile-text-sm">Vector Search with OpenAI Embeddings</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-yellow-100 rounded-full p-1">
                                <i data-lucide="activity" class="w-3 h-3 text-yellow-600"></i>
                            </div>
                            <span class="text-xs lg:text-sm mobile-text-sm">Real-time Streaming with WebSocket</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Slide 5: Impact & Future -->
<div class="slide bg-gradient-to-br from-indigo-50 to-purple-100">
    <div class="slide-content">
        <div class="max-w-7xl mx-auto h-full flex flex-col justify-center compact-spacing">
            <div class="text-center mb-4 animate-fade-in">
                <h1 class="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-2">Impact & Future Vision</h1>
                <p class="text-lg lg:text-xl text-gray-600">Scaling fast to full solution within 3 months</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-4">
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-5 animate-fade-in">
                    <div class="flex items-center mb-3">
                        <div class="bg-green-500 rounded-lg p-1 mr-2">
                            <i data-lucide="trending-up" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Current Impact</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-blue-50 rounded-lg p-3">
                            <h4 class="font-medium text-blue-900 mb-1 text-sm">For FSOFT Employees</h4>
                            <p class="text-xs lg:text-sm text-blue-700 mobile-text-sm">Clear, personalized upskilling guides saving time on information search</p>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-3">
                            <h4 class="font-medium text-purple-900 mb-1 text-sm">For FSOFT Organization</h4>
                            <p class="text-xs lg:text-sm text-purple-700 mobile-text-sm">Fosters AI culture, enhances productivity, provides L&D strategy data</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-5 animate-fade-in">
                    <div class="flex items-center mb-3">
                        <div class="bg-blue-500 rounded-lg p-1 mr-2">
                            <i data-lucide="rocket" class="w-4 h-4 text-white"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Technical Achievements</h3>
                    </div>
                    <div class="space-y-2 text-xs lg:text-sm">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="check-circle" class="w-3 h-3 text-green-500"></i>
                            <span class="mobile-text-sm">Fully containerized with Azure AKS auto-scaling</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i data-lucide="check-circle" class="w-3 h-3 text-green-500"></i>
                            <span class="mobile-text-sm">SonarQube integration with quality gates</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i data-lucide="check-circle" class="w-3 h-3 text-green-500"></i>
                            <span class="mobile-text-sm">Sub-15s roadmap generation performance</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i data-lucide="check-circle" class="w-3 h-3 text-green-500"></i>
                            <span class="mobile-text-sm">JWT authentication & API security patterns</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-2xl shadow-xl p-4 lg:p-5 mb-4 animate-fade-in">
                <h3 class="text-lg font-semibold mb-3 text-center text-gray-900">Future Roadmap - Phase 2 (Next 3 Months)</h3>
                <div class="chart-container">
                    <canvas id="roadmapChart"></canvas>
                </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-4 mb-4">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-3 lg:p-4 text-white animate-fade-in">
                    <div class="flex items-center mb-2">
                        <i data-lucide="integration" class="w-5 h-5 mr-2"></i>
                        <h3 class="font-semibold text-sm lg:text-base">Advanced Features</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm opacity-90 mobile-text-sm">
                        <p>• Deep Jira, OKR, iMocha integration</p>
                        <p>• External learning platforms (Coursera, Udemy)</p>
                        <p>• Advanced reports & team analysis</p>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-xl p-3 lg:p-4 text-white animate-fade-in">
                    <div class="flex items-center mb-2">
                        <i data-lucide="bar-chart" class="w-5 h-5 mr-2"></i>
                        <h3 class="font-semibold text-sm lg:text-base">Business Impact</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm opacity-90 mobile-text-sm">
                        <p>• Time savings quantification</p>
                        <p>• Skill development acceleration</p>
                        <p>• L&D ROI measurement</p>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl shadow-xl p-3 lg:p-4 text-white animate-fade-in">
                    <div class="flex items-center mb-2">
                        <i data-lucide="zap" class="w-5 h-5 mr-2"></i>
                        <h3 class="font-semibold text-sm lg:text-base">Technology Evolution</h3>
                    </div>
                    <div class="space-y-1 text-xs lg:text-sm opacity-90 mobile-text-sm">
                        <p>• LLM cost optimization</p>
                        <p>• Advanced AI features</p>
                        <p>• Mobile accessibility</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center animate-fade-in">
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl shadow-xl p-4 lg:p-5 text-white">
                    <h3 class="text-lg lg:text-xl font-bold mb-1">Thank You!</h3>
                    <p class="text-indigo-100 text-sm lg:text-base">Committed to AI-first culture and bringing real value to FSOFT</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
lucide.createIcons();

let currentSlideIndex = 0;
const slides = document.querySelectorAll('.slide');
const totalSlides = slides.length;

document.getElementById('totalSlides').textContent = totalSlides;

function showSlide(index) {
    slides.forEach(slide => slide.classList.remove('active'));
    slides[index].classList.add('active');
    document.getElementById('currentSlide').textContent = index + 1;
}

function nextSlide() {
    currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
    showSlide(currentSlideIndex);
    initializeCharts();
}

function previousSlide() {
    currentSlideIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
    showSlide(currentSlideIndex);
    initializeCharts();
}

// Keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowRight' || e.key === ' ') nextSlide();
    if (e.key === 'ArrowLeft') previousSlide();
});

// Initialize charts based on current slide
function initializeCharts() {
    setTimeout(() => {
        if (currentSlideIndex === 1) initImpactChart();
        if (currentSlideIndex === 2) initPerformanceChart();
        if (currentSlideIndex === 3) initQualityChart();
        if (currentSlideIndex === 4) initRoadmapChart();
    }, 100);
}

function initTeamChart() {
    // Team chart removed - slide 1 now uses visual team member cards
}

function initImpactChart() {
    const ctx = document.getElementById('impactChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Time to Create Path', 'Personalization Level', 'Scalability', 'Accuracy'],
            datasets: [{
                label: 'Traditional Method',
                data: [30, 40, 20, 60],
                backgroundColor: 'rgba(239, 68, 68, 0.8)'
            }, {
                label: 'AI Solution',
                data: [5, 95, 90, 95],
                backgroundColor: 'rgba(34, 197, 94, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' }
            },
            scales: {
                y: { beginAtZero: true, max: 100 }
            }
        }
    });
}

function initPerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['Skill Sync (≤5s)', 'Roadmap Gen (≤15s)', 'Report Gen (≤3s)', 'Concurrent Users (1000+)', 'Uptime (99.9%)'],
            datasets: [{
                label: 'Current Performance',
                data: [90, 85, 95, 80, 98],
                backgroundColor: 'rgba(147, 51, 234, 0.2)',
                borderColor: 'rgba(147, 51, 234, 1)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: { beginAtZero: true, max: 100 }
            }
        }
    });
}

function initQualityChart() {
    const ctx = document.getElementById('qualityChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Backend', 'Frontend', 'Agent Service'],
            datasets: [{
                label: 'Test Coverage %',
                data: [95, 29, 15],
                backgroundColor: ['rgba(34, 197, 94, 0.8)', 'rgba(251, 191, 36, 0.8)', 'rgba(239, 68, 68, 0.8)']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: { beginAtZero: true, max: 100 }
            }
        }
    });
}

function initRoadmapChart() {
    const ctx = document.getElementById('roadmapChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Month 1', 'Month 2', 'Month 3'],
            datasets: [{
                label: 'Feature Completion %',
                data: [50, 75, 100],
                borderColor: 'rgba(99, 102, 241, 1)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                fill: true,
                tension: 0.4
            }, {
                label: 'User Adoption',
                data: [10, 40, 80],
                borderColor: 'rgba(34, 197, 94, 1)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' }
            },
            scales: {
                y: { beginAtZero: true, max: 100 }
            }
        }
    });
}

// Initialize first slide
initializeCharts();

// Add touch support for mobile
let startX = 0;
let startY = 0;

document.addEventListener('touchstart', (e) => {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
});

document.addEventListener('touchend', (e) => {
    if (!startX || !startY) return;
    
    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;
    
    const diffX = startX - endX;
    const diffY = startY - endY;
    
    if (Math.abs(diffX) > Math.abs(diffY)) {
        if (diffX > 50) nextSlide();
        else if (diffX < -50) previousSlide();
    }
    
    startX = 0;
    startY = 0;
});
</script>

</body>
</html>
