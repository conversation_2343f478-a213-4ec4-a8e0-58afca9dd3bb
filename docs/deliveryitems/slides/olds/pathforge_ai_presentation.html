<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PathForge AI - HackAIthon 2025 Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .slide {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .slide.active {
            display: block;
            opacity: 1;
        }
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            margin-bottom: 16px;
        }
        .pipeline-step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -15px;
            width: 0;
            height: 0;
            border-left: 15px solid #667eea;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
        }
        .metric-card {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .metric-card:hover {
            transform: scale(1.05);
        }
        .demo-frame {
            border: 3px solid #667eea;
            border-radius: 12px;
            background: white;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Navigation -->
    <div class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-white rounded-full px-6 py-2 shadow-lg">
        <div class="flex space-x-4">
            <button onclick="goToSlide(0)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">1</button>
            <button onclick="goToSlide(1)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">2</button>
            <button onclick="goToSlide(2)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">3</button>
            <button onclick="goToSlide(3)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">4</button>
            <button onclick="goToSlide(4)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">5</button>
            <button onclick="goToSlide(5)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">6</button>
            <button onclick="goToSlide(6)" class="px-4 py-2 rounded-full hover:bg-gray-100 transition-colors">7</button>
        </div>
    </div>

    <!-- Slide 1: Project & Team Introduction -->
    <div class="slide active h-screen max-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-12 h-full max-h-full">
            <div class="col-span-7 flex flex-col justify-center animate-fade-in-up">
                <div class="mb-8">
                    <h1 class="text-5xl font-bold gradient-text mb-4">Pathforge AI</h1>
                    <h2 class="text-2xl text-gray-700 mb-6">AI-Powered Personalized Upskill Solution for FSOFT</h2>
                    <div class="bg-white rounded-lg p-6 shadow-lg mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">HackAIthon 2025 Goals</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-robot text-blue-600 mr-3"></i>
                                <span class="text-gray-700">AI-First Approach to Upskilling</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-chart-line text-green-600 mr-3"></i>
                                <span class="text-gray-700">Foster AI Culture & Productivity</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-target text-purple-600 mr-3"></i>
                                <span class="text-gray-700">Applied AI into Practical Outcomes</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Team CodePluse</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="team-card bg-gradient-to-br from-blue-100 to-blue-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">👨‍💻</div>
                            <div class="text-sm font-medium">NamNH46</div>
                            <div class="text-xs text-gray-600">Tech Lead</div>
                        </div>
                        <div class="team-card bg-gradient-to-br from-green-100 to-green-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🔧</div>
                            <div class="text-sm font-medium">PhongTN2</div>
                            <div class="text-xs text-gray-600">Backend</div>
                        </div>
                        <div class="team-card bg-gradient-to-br from-purple-100 to-purple-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🎨</div>
                            <div class="text-sm font-medium">TrungDD22</div>
                            <div class="text-xs text-gray-600">Frontend</div>
                        </div>
                        <div class="team-card bg-gradient-to-br from-yellow-100 to-yellow-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🤖</div>
                            <div class="text-sm font-medium">QuyetDB</div>
                            <div class="text-xs text-gray-600">AI Engineer</div>
                        </div>
                        <div class="team-card bg-gradient-to-br from-red-100 to-red-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">☁️</div>
                            <div class="text-sm font-medium">DaiNQ11</div>
                            <div class="text-xs text-gray-600">DevOps</div>
                        </div>
                        <div class="team-card bg-gradient-to-br from-indigo-100 to-indigo-200 p-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">📊</div>
                            <div class="text-sm font-medium">TruongPH2</div>
                            <div class="text-xs text-gray-600">Data Analyst</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-span-5 flex items-center justify-center">
                <div class="bg-white rounded-xl shadow-xl p-8 w-full">
                    <h3 class="text-xl font-semibold mb-6 text-center">3-Week MVP Progress</h3>
                    <canvas id="progressChart" class="w-full max-h-80"></canvas>
                </div>
            </div>
        </div>
        <div class="slide-number">1 / 7</div>
    </div>

    <!-- Slide 2: The Problem & AI Solution -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-red-50 to-orange-50 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-12 h-full max-h-full">
            <div class="col-span-6 flex flex-col justify-center">
                <div class="bg-white rounded-xl shadow-xl p-8 mb-6">
                    <h2 class="text-3xl font-bold text-red-600 mb-6">The Problem</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Time-Consuming Skill Assessment</h4>
                                <p class="text-gray-600 text-sm">Manual identification of skill gaps takes weeks</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-puzzle-piece text-orange-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Complex Learning Path Creation</h4>
                                <p class="text-gray-600 text-sm">No personalized roadmaps for individual needs</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-database text-yellow-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Scattered Skill Data</h4>
                                <p class="text-gray-600 text-sm">Information spread across multiple systems</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-xl p-8">
                    <h2 class="text-3xl font-bold text-blue-600 mb-6">AI-First Solution</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-robot text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Intelligent Automation</h4>
                                <p class="text-gray-600 text-sm">AI agents handle entire analysis pipeline</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-lightning-bolt text-green-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Scale & Speed</h4>
                                <p class="text-gray-600 text-sm">Process thousands of profiles in minutes</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-users text-purple-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-gray-800">Collaborative AI</h4>
                                <p class="text-gray-600 text-sm">Humans + AI working together effectively</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-span-6 flex items-center justify-center">
                <div class="bg-white rounded-xl shadow-xl p-8 w-full">
                    <h3 class="text-xl font-semibold mb-6 text-center">Impact Comparison</h3>
                    <canvas id="impactChart" class="w-full max-h-96"></canvas>
                </div>
            </div>
        </div>
        <div class="slide-number">2 / 7</div>
    </div>

    <!-- Slide 3: How it Works (MVP Pipeline) -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-green-50 to-teal-50 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto h-full max-h-full flex flex-col">
            <h1 class="text-4xl font-bold text-center gradient-text mb-8">MVP Pipeline Flow</h1>
            
            <div class="flex-1 grid grid-cols-12 gap-8 overflow-hidden">
                <div class="col-span-8 overflow-y-auto pr-4">
                    <div class="space-y-4">
                        <div class="pipeline-step">
                            <h3 class="font-bold text-lg text-blue-600 mb-2">1. Goal & Constraint Input</h3>
                            <p class="text-sm text-gray-700 mb-2">React frontend with TypeScript validation, chat interface powered by LangGraph agents</p>
                            <div class="flex space-x-2">
                                <span class="tech-badge">React 19.1</span>
                                <span class="tech-badge">TypeScript</span>
                                <span class="tech-badge">LangGraph</span>
                            </div>
                        </div>
                        
                        <div class="pipeline-step">
                            <h3 class="font-bold text-lg text-green-600 mb-2">2. Skill Sync</h3>
                            <p class="text-sm text-gray-700 mb-2">Automatic aggregation from Jira, OKR, iMocha with PostgreSQL storage</p>
                            <div class="flex space-x-2">
                                <span class="tech-badge">PostgreSQL</span>
                                <span class="tech-badge">CV Extractor</span>
                                <span class="tech-badge">JSON Validation</span>
                            </div>
                        </div>
                        
                        <div class="pipeline-step">
                            <h3 class="font-bold text-lg text-purple-600 mb-2">3. Gap Analysis</h3>
                            <p class="text-sm text-gray-700 mb-2">AI agents analyze skill gaps using RAG system with ChromaDB vector search</p>
                            <div class="flex space-x-2">
                                <span class="tech-badge">RAG System</span>
                                <span class="tech-badge">ChromaDB</span>
                                <span class="tech-badge">Vector Search</span>
                            </div>
                        </div>
                        
                        <div class="pipeline-step">
                            <h3 class="font-bold text-lg text-orange-600 mb-2">4. Roadmap Generation</h3>
                            <p class="text-sm text-gray-700 mb-2">Comprehensive roadmap with course recommendation engine and external platform integration</p>
                            <div class="flex space-x-2">
                                <span class="tech-badge">Course Engine</span>
                                <span class="tech-badge">Time Analysis</span>
                                <span class="tech-badge">External APIs</span>
                            </div>
                        </div>
                        
                        <div class="pipeline-step">
                            <h3 class="font-bold text-lg text-red-600 mb-2">5. Report Generation</h3>
                            <p class="text-sm text-gray-700 mb-2">Agent Report Generator creates markdown summaries with PDF export capability</p>
                            <div class="flex space-x-2">
                                <span class="tech-badge">Markdown</span>
                                <span class="tech-badge">PDF Export</span>
                                <span class="tech-badge">Tracking</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-span-4 flex flex-col space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Performance Targets</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm">Skill Sync</span>
                                <span class="text-sm font-bold text-green-600">≤ 5s</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm">Roadmap Gen</span>
                                <span class="text-sm font-bold text-blue-600">≤ 15s</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm">Agent Report</span>
                                <span class="text-sm font-bold text-purple-600">≤ 3s</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm">Concurrent Users</span>
                                <span class="text-sm font-bold text-orange-600">1,000+</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Technical Highlights</h3>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <i class="fas fa-stream text-blue-500 mr-2"></i>
                                <span class="text-sm">Real-time Processing</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-database text-green-500 mr-2"></i>
                                <span class="text-sm">State Management</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-red-500 mr-2"></i>
                                <span class="text-sm">Error Handling</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-tachometer-alt text-purple-500 mr-2"></i>
                                <span class="text-sm">Performance Optimization</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slide-number">3 / 7</div>
    </div>

    <!-- Slide 4: Core AI Technology & Achievements -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-purple-50 to-pink-50 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto h-full max-h-full flex flex-col">
            <h1 class="text-4xl font-bold text-center gradient-text mb-6">Core AI Technology & 3-Week Achievements</h1>
            
            <div class="flex-1 grid grid-cols-12 gap-8 overflow-hidden">
                <div class="col-span-8 overflow-y-auto pr-4">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-blue-600 mb-4">AI Technologies</h3>
                            <div class="space-y-3">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm">LangGraph Agent Framework</h4>
                                    <p class="text-xs text-gray-600">Multi-agent orchestration v0.3</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm">FastAPI Service</h4>
                                    <p class="text-xs text-gray-600">RESTful API on port 8000</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm">RAG System</h4>
                                    <p class="text-xs text-gray-600">PostgreSQL + ChromaDB + OpenAI</p>
                                </div>
                                <div class="border-l-4 border-orange-500 pl-3">
                                    <h4 class="font-semibold text-sm">AI Agent Services</h4>
                                    <p class="text-xs text-gray-600">CV, Resume, Roadmap, Goal agents</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-green-600 mb-4">Tech Stack</h3>
                            <div class="space-y-3">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm">Backend</h4>
                                    <p class="text-xs text-gray-600">Node.js 22 + Express + TypeScript</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm">Frontend</h4>
                                    <p class="text-xs text-gray-600">React 19.1 + Vite 6.3 + Mantine</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm">Agent Service</h4>
                                    <p class="text-xs text-gray-600">Python 3.12 + LangGraph + FastAPI</p>
                                </div>
                                <div class="border-l-4 border-orange-500 pl-3">
                                    <h4 class="font-semibold text-sm">Database</h4>
                                    <p class="text-xs text-gray-600">PostgreSQL + Prisma ORM</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-purple-600 mb-4">Code Quality</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Backend Coverage</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-bold">95%+</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Agent Service</span>
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-bold">15.2%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Frontend</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-bold">29.2%</span>
                                </div>
                                <div class="border-t pt-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="text-xs">SonarQube Integration</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="text-xs">CI/CD Pipeline</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-orange-600 mb-4">Deployment</h3>
                            <div class="space-y-3">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm">Docker Services</h4>
                                    <p class="text-xs text-gray-600">4 services with health checks</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm">Azure AKS</h4>
                                    <p class="text-xs text-gray-600">Auto-scaling (HPA) ready</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm">Scaling Config</h4>
                                    <p class="text-xs text-gray-600">Agent: 1-3, Backend: 1-5 replicas</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-span-4 flex flex-col space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Performance Metrics</h3>
                        <canvas id="performanceChart" class="w-full max-h-40"></canvas>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">AI Achievements</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                                <span class="text-sm">Resume Processing Pipeline</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-route text-green-500 mr-2"></i>
                                <span class="text-sm">Learning Roadmap Generation</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-search text-purple-500 mr-2"></i>
                                <span class="text-sm">Vector Search System</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-comments text-orange-500 mr-2"></i>
                                <span class="text-sm">Agent Communication</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-stream text-red-500 mr-2"></i>
                                <span class="text-sm">Real-time Streaming</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slide-number">4 / 7</div>
    </div>

    <!-- Slide 5: Impact & Future -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto h-full max-h-full flex flex-col">
            <h1 class="text-4xl font-bold text-center gradient-text mb-6">Impact & Future Vision</h1>
            
            <div class="flex-1 grid grid-cols-12 gap-8 overflow-hidden">
                <div class="col-span-8 overflow-y-auto pr-4">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-blue-600 mb-4">MVP Impact</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">For FSOFT Employees</h4>
                                    <p class="text-xs text-gray-600">Clear, personalized upskilling guide saving time on information search</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">For FSOFT</h4>
                                    <p class="text-xs text-gray-600">Fosters AI culture, enhances productivity, supports skill management</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Applied AI Outcomes</h4>
                                    <p class="text-xs text-gray-600">Demonstrates real AI value and "AI for good" principles</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-green-600 mb-4">Production Readiness</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Deployment Architecture</h4>
                                    <p class="text-xs text-gray-600">Fully containerized with Azure AKS and auto-scaling</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Security Implementation</h4>
                                    <p class="text-xs text-gray-600">JWT authentication, API security, RBAC planning</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Performance Metrics</h4>
                                    <p class="text-xs text-gray-600">Sub-15s roadmap generation, 1000+ concurrent users</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-purple-600 mb-4">Scalability Demonstrated</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Multi-Agent Architecture</h4>
                                    <p class="text-xs text-gray-600">Modular design for easy extension and integration</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">API-First Design</h4>
                                    <p class="text-xs text-gray-600">RESTful architecture with OpenAPI documentation</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Cloud-Native Features</h4>
                                    <p class="text-xs text-gray-600">Container orchestration and monitoring ready</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-bold text-orange-600 mb-4">Phase 2 - Post-HackAIthon</h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Scale to Full Solution</h4>
                                    <p class="text-xs text-gray-600">Complete deployment within 3 months</p>
                                </div>
                                <div class="border-l-4 border-green-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Advanced Features</h4>
                                    <p class="text-xs text-gray-600">Deeper integrations, external learning sources</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-3">
                                    <h4 class="font-semibold text-sm mb-1">Measurable Impact</h4>
                                    <p class="text-xs text-gray-600">Time savings, skill acceleration, ROI metrics</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-span-4 flex flex-col space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Business Impact Projection</h3>
                        <canvas id="impactProjectionChart" class="w-full max-h-48"></canvas>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Future Roadmap</h3>
                        <div class="space-y-3">
                            <div class="metric-card">
                                <div class="text-2xl font-bold text-blue-600">3</div>
                                <div class="text-xs text-gray-600">Months to Full Solution</div>
                            </div>
                            <div class="metric-card">
                                <div class="text-2xl font-bold text-green-600">10+</div>
                                <div class="text-xs text-gray-600">External Integrations</div>
                            </div>
                            <div class="metric-card">
                                <div class="text-2xl font-bold text-purple-600">5000+</div>
                                <div class="text-xs text-gray-600">Target Users</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slide-number">5 / 7</div>
    </div>

    <!-- Slide 6: Demo Application -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-indigo-50 to-blue-50 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto h-full max-h-full flex flex-col">
            <h1 class="text-4xl font-bold text-center gradient-text mb-8">Live Demo</h1>
            
            <div class="flex-1 grid grid-cols-12 gap-8 overflow-hidden">
                <div class="col-span-8 flex items-center justify-center">
                    <div class="demo-frame w-full h-full flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🚀</div>
                            <h2 class="text-2xl font-bold text-gray-800 mb-4">Pathforge AI Demo</h2>
                            <p class="text-gray-600 mb-6">Experience the complete AI-powered upskilling pipeline</p>
                            
                            <div class="bg-gray-100 rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold mb-4">Demo Flow</h3>
                                <div class="flex justify-center space-x-4">
                                    <div class="demo-step bg-blue-500 text-white p-3 rounded-full w-12 h-12 flex items-center justify-center font-bold">1</div>
                                    <div class="demo-arrow">→</div>
                                    <div class="demo-step bg-green-500 text-white p-3 rounded-full w-12 h-12 flex items-center justify-center font-bold">2</div>
                                    <div class="demo-arrow">→</div>
                                    <div class="demo-step bg-purple-500 text-white p-3 rounded-full w-12 h-12 flex items-center justify-center font-bold">3</div>
                                    <div class="demo-arrow">→</div>
                                    <div class="demo-step bg-orange-500 text-white p-3 rounded-full w-12 h-12 flex items-center justify-center font-bold">4</div>
                                </div>
                                <div class="grid grid-cols-4 gap-4 mt-4 text-sm">
                                    <div class="text-center">
                                        <div class="font-semibold">Goal Input</div>
                                        <div class="text-gray-600">Career objectives</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-semibold">Skill Sync</div>
                                        <div class="text-gray-600">CV analysis</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-semibold">Gap Analysis</div>
                                        <div class="text-gray-600">AI assessment</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-semibold">Roadmap</div>
                                        <div class="text-gray-600">Learning plan</div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                                <i class="fas fa-play mr-2"></i>
                                Start Live Demo
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-span-4 space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Demo Highlights</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-bolt text-yellow-500 mr-3"></i>
                                <span class="text-sm">Real-time AI processing</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-chart-line text-blue-500 mr-3"></i>
                                <span class="text-sm">Interactive visualizations</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-robot text-purple-500 mr-3"></i>
                                <span class="text-sm">Multi-agent coordination</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-file-export text-green-500 mr-3"></i>
                                <span class="text-sm">PDF report generation</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Key Features</h3>
                        <div class="space-y-3">
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <h4 class="font-semibold text-sm text-blue-800">Intelligent CV Analysis</h4>
                                <p class="text-xs text-blue-600">AI extracts skills and experience automatically</p>
                            </div>
                            <div class="bg-green-50 p-3 rounded-lg">
                                <h4 class="font-semibold text-sm text-green-800">Personalized Roadmaps</h4>
                                <p class="text-xs text-green-600">Custom learning paths based on goals and gaps</p>
                            </div>
                            <div class="bg-purple-50 p-3 rounded-lg">
                                <h4 class="font-semibold text-sm text-purple-800">Course Recommendations</h4>
                                <p class="text-xs text-purple-600">RAG-powered suggestions from multiple sources</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibent mb-4">Performance Live Stats</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center">
                                <div class="text-xl font-bold text-blue-600">4.2s</div>
                                <div class="text-xs text-gray-600">Avg Processing</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">99.5%</div>
                                <div class="text-xs text-gray-600">Accuracy Rate</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-purple-600">156</div>
                                <div class="text-xs text-gray-600">Active Sessions</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-orange-600">12ms</div>
                                <div class="text-xs text-gray-600">Response Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slide-number">6 / 7</div>
    </div>

    <!-- Slide 7: Thank You & Q&A -->
    <div class="slide h-screen max-h-screen bg-gradient-to-br from-blue-50 to-purple-100 items-center justify-center p-8 overflow-hidden">
        <div class="max-w-6xl mx-auto h-full max-h-full flex flex-col items-center justify-center text-center">
            <div class="mb-12">
                <h1 class="text-6xl font-bold gradient-text mb-6">Thank You!</h1>
                <p class="text-2xl text-gray-700 mb-8">Team CodePluse - Pathforge AI</p>
                
                <div class="bg-white rounded-xl shadow-xl p-8 mb-8">
                    <h2 class="text-3xl font-bold text-gray-800 mb-6">We're Ready for Questions</h2>
                    <div class="grid grid-cols-3 gap-8 mb-6">
                        <div class="text-center">
                            <div class="text-4xl mb-3">🤖</div>
                            <h3 class="font-semibold text-lg">AI Technology</h3>
                            <p class="text-gray-600 text-sm">LangGraph, RAG, Multi-agents</p>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl mb-3">🏗️</div>
                            <h3 class="font-semibold text-lg">Architecture</h3>
                            <p class="text-gray-600 text-sm">Scalable, Cloud-native, Production-ready</p>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl mb-3">📈</div>
                            <h3 class="font-semibold text-lg">Business Impact</h3>
                            <p class="text-gray-600 text-sm">FSOFT upskilling transformation</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-6 mb-8">
                    <h3 class="text-xl font-bold mb-3">Our Commitment</h3>
                    <p class="text-lg">Fostering AI-first culture through collaborative innovation and practical AI solutions that deliver real value to FSOFT</p>
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="flex items-center justify-center space-x-6 text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-2"></i>
                        <span>Contact: <EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <i class="fab fa-github mr-2"></i>
                        <span>GitHub: pathforge-ai</span>
                    </div>
                </div>
                
                <div class="text-4xl">
                    <span class="inline-block animate-bounce">💬</span>
                    <span class="text-2xl font-semibold text-gray-700 ml-4">Questions & Answers</span>
                </div>
            </div>
        </div>
        <div class="slide-number">7 / 7</div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            currentSlide = index;
            
            // Initialize charts when slide becomes active
            if (index === 0) initProgressChart();
            if (index === 1) initImpactChart();
            if (index === 3) initPerformanceChart();
            if (index === 4) initImpactProjectionChart();
        }

        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                showSlide(index);
            }
        }

        function nextSlide() {
            goToSlide((currentSlide + 1) % totalSlides);
        }

        function prevSlide() {
            goToSlide((currentSlide - 1 + totalSlides) % totalSlides);
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // Chart initialization functions
        function initProgressChart() {
            const ctx = document.getElementById('progressChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'In Progress', 'Remaining'],
                    datasets: [{
                        data: [60, 25, 15],
                        backgroundColor: ['#10B981', '#F59E0B', '#E5E7EB'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initImpactChart() {
            const ctx = document.getElementById('impactChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Time to Create Roadmap', 'Accuracy', 'User Satisfaction', 'Scalability'],
                    datasets: [{
                        label: 'Traditional Method',
                        data: [20, 60, 65, 30],
                        backgroundColor: 'rgba(239, 68, 68, 0.7)'
                    }, {
                        label: 'Pathforge AI',
                        data: [95, 92, 88, 95],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function initPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Speed', 'Accuracy', 'Scalability', 'Quality', 'Security'],
                    datasets: [{
                        label: 'Pathforge AI',
                        data: [95, 92, 88, 95, 85],
                        backgroundColor: 'rgba(139, 92, 246, 0.2)',
                        borderColor: 'rgba(139, 92, 246, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function initImpactProjectionChart() {
            const ctx = document.getElementById('impactProjectionChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Month 1', 'Month 2', 'Month 3', 'Month 6', 'Month 12'],
                    datasets: [{
                        label: 'User Adoption',
                        data: [100, 500, 1000, 3000, 5000],
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true
                    }, {
                        label: 'Time Saved (Hours)',
                        data: [50, 300, 800, 2500, 5000],
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Initialize first slide
        showSlide(0);
    </script>
</body>
</html>
