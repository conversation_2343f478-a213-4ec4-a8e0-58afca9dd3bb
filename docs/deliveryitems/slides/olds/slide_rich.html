<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pathforge AI - HackAIthon 2025 (Rich Content)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .slide { scroll-snap-align: start; }
        html { scroll-snap-type: y mandatory; }
        .prose h1 { @apply text-2xl font-bold text-gray-900 mb-4; }
        .prose h2 { @apply text-xl font-semibold text-gray-800 mb-3; }
        .prose h3 { @apply text-lg font-medium text-gray-700 mb-2; }
        .prose p { @apply text-gray-600 mb-3 leading-relaxed; }
        .prose ul { @apply list-disc list-inside mb-4 text-gray-600; }
        .prose li { @apply mb-1; }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 overflow-x-hidden">

<!-- Slide 1: Project & Team Introduction -->
<div class="slide h-screen max-h-screen bg-gray-50 p-8 overflow-hidden">
  <div class="max-w-5xl mx-auto h-full max-h-full">
    <div class="bg-white rounded-xl shadow-lg p-12 h-full max-h-full flex flex-col">
      <h1 class="text-3xl font-bold mb-8 text-gray-900">Pathforge AI: HackAIthon 2025 Project Introduction</h1>
      <div class="grid grid-cols-12 gap-8 flex-1 overflow-hidden">
        <div class="col-span-8 overflow-y-auto">
          <div class="prose prose-lg max-w-none">
            <h2>Project Overview</h2>
            <p>
              <strong>Pathforge AI</strong> represents our team's commitment to the HackAIthon 2025 theme of fostering AI culture and productivity through an AI-first approach. 
              This innovative solution addresses a critical bottleneck within FSOFT: the challenge of creating personalized upskill roadmaps for employees efficiently and effectively.
            </p>
            
            <h2>Team CodePluse Composition</h2>
            <p>
              Our diverse team of 6 members brings together complementary skills and experience levels, adhering to the competition's collaboration spirit:
            </p>
            <ul>
              <li><strong>NamNH46</strong> - Technical Lead & AI Architecture</li>
              <li><strong>PhongTN2</strong> - Backend Development & Database Design</li>
              <li><strong>TrungDD22</strong> - Frontend Development & User Experience</li>
              <li><strong>QuyetDB</strong> - DevOps & Infrastructure</li>
              <li><strong>DaiNQ11</strong> - AI Agent Development & Testing</li>
              <li><strong>TruongPH2</strong> - Quality Assurance & Documentation</li>
            </ul>
            
            <h2>3-Week HackAIthon Objectives</h2>
            <p>
              During the intensive 3-week development period, our primary goal is to deliver a Minimum Viable Product (MVP) that demonstrates the core working pipeline. 
              This includes: career goal input, existing skill synchronization, skill gap analysis, personalized learning roadmap generation, and comprehensive summary reporting.
            </p>
            
            <h2>Strategic Alignment with Contest Goals</h2>
            <p>
              Our project embodies the contest's core principles by applying AI as the only viable solution for personalized upskilling at scale, 
              generating practical outcomes that enhance productivity, and fostering a collaborative relationship between humans and AI systems.
            </p>
          </div>
        </div>
        <div class="col-span-4 overflow-y-auto">
          <div class="space-y-6">
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="font-semibold text-blue-800 mb-2">Project Stats</h3>
              <div class="text-sm text-blue-700">
                <div>Team Size: 6 members</div>
                <div>Duration: 3 weeks</div>
                <div>Target: MVP completion</div>
                <div>Approach: AI-first</div>
              </div>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <canvas id="teamRolesChart" class="w-full h-32"></canvas>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
              <h3 class="font-semibold text-purple-800 mb-2">Core Technologies</h3>
              <div class="text-sm text-purple-700">
                <div>• LangGraph Agents</div>
                <div>• FastAPI + React</div>
                <div>• PostgreSQL + Vector DB</div>
                <div>• Docker + Azure AKS</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 2: The Problem & AI Solution -->
<div class="slide h-screen max-h-screen bg-gray-50 p-8 overflow-hidden">
  <div class="max-w-5xl mx-auto h-full max-h-full">
    <div class="bg-white rounded-xl shadow-lg p-12 h-full max-h-full flex flex-col">
      <h1 class="text-3xl font-bold mb-8 text-gray-900">Problem Analysis & AI-First Solution</h1>
      <div class="grid grid-cols-12 gap-8 flex-1 overflow-hidden">
        <div class="col-span-8 overflow-y-auto">
          <div class="prose prose-lg max-w-none">
            <h2>The Current Challenge</h2>
            <p>
              FSOFT faces significant challenges in personalizing employee upskilling programs. The traditional approach requires manual analysis of individual skill sets, 
              manual identification of skill gaps, and generic learning path recommendations that don't account for personal constraints, learning preferences, or specific career goals.
            </p>
            
            <h2>Scale and Complexity Issues</h2>
            <p>
              With thousands of employees across diverse technical domains, the manual approach to skill gap analysis becomes increasingly impractical. 
              HR and L&D teams struggle to provide timely, relevant, and personalized guidance to each individual, resulting in:
            </p>
            <ul>
              <li>Delayed career development planning</li>
              <li>Inefficient resource allocation for training programs</li>
              <li>Reduced employee engagement in learning initiatives</li>
              <li>Difficulty tracking skill evolution across the organization</li>
            </ul>
            
            <h2>Why AI is the Only Solution</h2>
            <p>
              Pathforge AI leverages artificial intelligence as the primary and only viable approach to solve this bottleneck. Our AI-first methodology includes:
            </p>
            <ul>
              <li><strong>Multi-Agent Architecture:</strong> Specialized AI agents handle different aspects of the pipeline (CV extraction, skill analysis, roadmap generation)</li>
              <li><strong>RAG Integration:</strong> Retrieval-Augmented Generation ensures recommendations are based on current, relevant learning resources</li>
              <li><strong>Real-time Processing:</strong> Automated pipeline reduces roadmap generation from weeks to minutes</li>
              <li><strong>Personalization at Scale:</strong> AI analyzes individual profiles and constraints to create truly personalized learning paths</li>
            </ul>
            
            <h2>Productivity and Collaboration Benefits</h2>
            <p>
              The solution embodies the "AI for good" principle by enhancing productivity for both employees and support teams. 
              Employees gain clear, actionable guidance for their career development, while HR teams can focus on strategic initiatives rather than manual analysis tasks.
            </p>
          </div>
        </div>
        <div class="col-span-4 overflow-y-auto">
          <div class="space-y-6">
            <div class="bg-red-50 rounded-lg p-4">
              <h3 class="font-semibold text-red-800 mb-2">Current Problems</h3>
              <canvas id="problemMetricsChart" class="w-full h-32"></canvas>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <h3 class="font-semibold text-green-800 mb-2">AI Solution Benefits</h3>
              <div class="text-sm text-green-700">
                <div>• 95% faster processing</div>
                <div>• 100% personalized</div>
                <div>• Scalable to 1000+ users</div>
                <div>• Real-time updates</div>
              </div>
            </div>
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="font-semibold text-blue-800 mb-2">Implementation Approach</h3>
              <div class="text-sm text-blue-700">
                <div>AI-First Methodology</div>
                <div>Multi-Agent System</div>
                <div>RAG Integration</div>
                <div>Green Field Development</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 3: How it Works - Detailed Pipeline -->
<div class="slide h-screen max-h-screen bg-gray-50 p-8 overflow-hidden">
  <div class="max-w-5xl mx-auto h-full max-h-full">
    <div class="bg-white rounded-xl shadow-lg p-12 h-full max-h-full flex flex-col">
      <h1 class="text-3xl font-bold mb-8 text-gray-900">MVP Pipeline: Comprehensive Technical Implementation</h1>
      <div class="grid grid-cols-12 gap-8 flex-1 overflow-hidden">
        <div class="col-span-8 overflow-y-auto">
          <div class="prose prose-lg max-w-none">
            <h2>1. Goal & Constraint Input System</h2>
            <p>
              The user journey begins with our React-based frontend featuring TypeScript validation and an intuitive chat interface. 
              The system supports both structured form input and natural language interaction powered by LangGraph agents. 
              Users define their career objectives, learning preferences, time constraints, and specific technology interests.
            </p>
            
            <h2>2. Intelligent Skill Synchronization</h2>
            <p>
              Our CV Extractor Agent automatically aggregates skill data from multiple sources including Jira project history, 
              OKR achievements, and iMocha assessment results. The system processes resume files with JSON validation and 
              stores structured skill profiles in PostgreSQL through the embedded_people_skills module. This automated approach 
              eliminates manual data entry and ensures comprehensive skill coverage.
            </p>
            
            <h2>3. Advanced Gap Analysis Engine</h2>
            <p>
              The Gap Analysis component leverages AI agents with RAG capabilities using ChromaDB vector search. 
              The Learning Roadmap Agent operates in dual modes - Simple for straightforward recommendations and 
              Advisor mode with clarifying questions for complex career transitions. The system analyzes current skills 
              against target profiles and identifies specific learning requirements.
            </p>
            
            <h2>4. Personalized Roadmap Generation</h2>
            <p>
              Our comprehensive roadmap engine includes course recommendation capabilities with RAG retrieval from an extensive course database. 
              The system considers time constraints, learning preferences, and integrates with external platforms like Coursera and Udemy. 
              Each roadmap includes milestone tracking, estimated completion times, and prerequisite mapping.
            </p>
            
            <h2>5. Intelligent Report Generation</h2>
            <p>
              The Agent Report Generator creates detailed markdown summaries with PDF export functionality. 
              Reports include skill progression tracking, learning recommendations, completion milestones, and metadata for future analysis. 
              The system supports multiple report formats for different stakeholders.
            </p>
            
            <h2>Technical Excellence Features</h2>
            <ul>
              <li><strong>Real-time Processing:</strong> FastAPI streaming endpoints with WebSocket support</li>
              <li><strong>State Management:</strong> LangGraph conversation persistence with checkpoint recovery</li>
              <li><strong>Error Handling:</strong> Comprehensive recovery mechanisms with retry logic</li>
              <li><strong>Performance Optimization:</strong> Connection pooling and caching strategies</li>
              <li><strong>Security:</strong> SSO integration assumptions and JWT validation</li>
            </ul>
          </div>
        </div>
        <div class="col-span-4 overflow-y-auto">
          <div class="space-y-6">
            <div class="bg-indigo-50 rounded-lg p-4">
              <h3 class="font-semibold text-indigo-800 mb-2">Pipeline Performance</h3>
              <canvas id="pipelineTimingChart" class="w-full h-32"></canvas>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
              <h3 class="font-semibold text-purple-800 mb-2">State Machine Flow</h3>
              <div class="text-xs text-purple-700">
                <div>Authenticated → PR Created</div>
                <div>PR Created → Skill Profile Fetched</div>
                <div>Skill Profile → Gap Analysis Complete</div>
                <div>Gap Analysis → Roadmap Generated</div>
                <div>Roadmap Generated → Report Ready</div>
              </div>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <h3 class="font-semibold text-green-800 mb-2">Key Integrations</h3>
              <div class="text-sm text-green-700">
                <div>• PostgreSQL + PGVector</div>
                <div>• ChromaDB Vector Search</div>
                <div>• OpenAI Embeddings</div>
                <div>• External Course APIs</div>
              </div>
            </div>
            <div class="bg-orange-50 rounded-lg p-4">
              <h3 class="font-semibold text-orange-800 mb-2">Architecture Highlights</h3>
              <div class="text-sm text-orange-700">
                <div>Multi-Agent Orchestration</div>
                <div>Async Processing</div>
                <div>Connection Pooling</div>
                <div>Real-time Streaming</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 4: Technical Excellence & Development Metrics -->
<div class="slide h-screen max-h-screen bg-gray-50 p-8 overflow-hidden">
  <div class="max-w-5xl mx-auto h-full max-h-full">
    <div class="bg-white rounded-xl shadow-lg p-12 h-full max-h-full flex flex-col">
      <h1 class="text-3xl font-bold mb-8 text-gray-900">Technical Excellence & Development Achievements</h1>
      <div class="grid grid-cols-12 gap-8 flex-1 overflow-hidden">
        <div class="col-span-8 overflow-y-auto">
          <div class="prose prose-lg max-w-none">
            <h2>Core AI Technology Stack</h2>
            <p>
              Our implementation leverages cutting-edge AI technologies including LangGraph v0.3 for multi-agent orchestration with state management and human-in-the-loop capabilities. 
              The FastAPI service provides both streaming and non-streaming endpoints for agent interactions, supporting real-time user engagement and batch processing requirements.
            </p>
            
            <h2>Comprehensive Architecture</h2>
            <p>
              The system architecture includes specialized AI Agent Services: CV Extractor Agent for resume processing, Resume RAG Agent for skill analysis, 
              Learning Roadmap Agent for personalized recommendations, and Goal Definition Agent for requirement gathering. 
              Each agent operates with specialized tools and integration patterns optimized for their specific domain.
            </p>
            
            <h2>Database and Performance Architecture</h2>
            <p>
              Our data layer utilizes PostgreSQL with PGVector extension for vector embeddings, ChromaDB for course database management, 
              and OpenAI embeddings for semantic search capabilities. The architecture supports separate databases for agent service checkpoints 
              (pathforge_ai) and user management (pathforge_backend), ensuring data isolation and performance optimization.
            </p>
            
            <h2>Full Stack Implementation</h2>
            <p>
              The technology stack includes Node.js 22 with Express.js and TypeScript for backend services, React 19.1 with TypeScript 5.8, 
              Vite 6.3, and Mantine UI for frontend development. The Agent Service runs on Python 3.12 with LangGraph, FastAPI, 
              and uv package manager, utilizing async/await design patterns for optimal performance.
            </p>
            
            <h2>Quality Assurance and DevOps</h2>
            <p>
              Code quality is maintained through comprehensive testing with 95%+ backend coverage, SonarQube integration for multi-language analysis, 
              and automated CI/CD pipelines using GitHub Actions and Azure DevOps. The deployment strategy includes Docker containerization 
              and Kubernetes deployment with horizontal pod autoscaling configured for different service tiers.
            </p>
            
            <h2>Production Readiness Features</h2>
            <ul>
              <li><strong>Auto-scaling:</strong> HPA configured with CPU and memory triggers</li>
              <li><strong>Health Monitoring:</strong> Comprehensive health checks and service dependencies</li>
              <li><strong>Security:</strong> JWT authentication, API security patterns, and database access controls</li>
              <li><strong>Performance:</strong> Connection pooling, async processing, and caching strategies</li>
              <li><strong>Logging:</strong> Request tracking, performance monitoring, and error aggregation</li>
            </ul>
          </div>
        </div>
        <div class="col-span-4 overflow-y-auto">
          <div class="space-y-6">
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="font-semibold text-blue-800 mb-2">Performance Metrics</h3>
              <canvas id="performanceChart" class="w-full h-32"></canvas>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <h3 class="font-semibold text-green-800 mb-2">Test Coverage</h3>
              <div class="text-sm text-green-700">
                <div>Backend: 95%+</div>
                <div>Agent Service: 15.2%</div>
                <div>Frontend: 29.2%</div>
                <div>Integration: Comprehensive</div>
              </div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
              <h3 class="font-semibold text-purple-800 mb-2">Deployment Architecture</h3>
              <div class="text-sm text-purple-700">
                <div>4 Docker Services</div>
                <div>Azure AKS Ready</div>
                <div>Auto-scaling HPA</div>
                <div>Static IP Configuration</div>
              </div>
            </div>
            <div class="bg-orange-50 rounded-lg p-4">
              <h3 class="font-semibold text-orange-800 mb-2">Service Ports</h3>
              <div class="text-sm text-orange-700">
                <div>Agent: 8000</div>
                <div>Backend: 8080</div>
                <div>Frontend: 3000</div>
                <div>Streamlit: 8501</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 5: Impact Analysis & Future Roadmap -->
<div class="slide h-screen max-h-screen bg-gray-50 p-8 overflow-hidden">
  <div class="max-w-5xl mx-auto h-full max-h-full">
    <div class="bg-white rounded-xl shadow-lg p-12 h-full max-h-full flex flex-col">
      <h1 class="text-3xl font-bold mb-8 text-gray-900">Impact Analysis & Strategic Future Vision</h1>
      <div class="grid grid-cols-12 gap-8 flex-1 overflow-hidden">
        <div class="col-span-8 overflow-y-auto">
          <div class="prose prose-lg max-w-none">
            <h2>Immediate Impact for FSOFT Employees</h2>
            <p>
              Pathforge AI transforms the employee experience by providing clear, personalized upskilling guidance that saves significant time previously spent on information search. 
              Employees gain ownership of their career growth through data-driven recommendations tailored to their specific goals, constraints, and learning preferences. 
              The system empowers individuals to make informed decisions about their professional development trajectory.
            </p>
            
            <h2>Organizational Benefits for FSOFT</h2>
            <p>
              The platform fosters AI culture adoption across the organization while enhancing technical team productivity through automated skill management processes. 
              FSOFT gains valuable data insights for Learning & Development strategy through comprehensive reports including Skill Evolution analysis, 
              Gap Analysis snapshots, and Usage Analytics. This strategic data enables evidence-based decision making for training investments and talent development initiatives.
            </p>
            
            <h2>Technical Achievements Demonstrating Production Readiness</h2>
            <p>
              Our implementation showcases enterprise-grade technical capabilities including fully containerized deployment architecture, 
              Azure AKS readiness with auto-scaling capabilities, and production-grade database setup with connection pooling and backup strategies. 
              The system achieves sub-15 second roadmap generation times while supporting 1000+ concurrent users with efficient resource utilization and comprehensive monitoring.
            </p>
            
            <h2>Scalability and Extensibility Framework</h2>
            <p>
              The modular multi-agent architecture enables easy extension with new capabilities and integration points. 
              Our API-first design with OpenAPI documentation supports external system integration, while the vector database infrastructure 
              provides real-time sync capabilities and versioning support for evolving skill profiles. Cloud-native features ensure container orchestration readiness and service mesh compatibility.
            </p>
            
            <h2>Phase 2 Development Plan (Post-HackAIthon)</h2>
            <p>
              The strategic roadmap for scaling to a full solution within 3 months includes deeper integration with internal systems (Jira, OKR, iMocha), 
              expanded external learning source connections (Coursera, Udemy, internal training platforms), and advanced reporting capabilities for skill progression and team analysis. 
              Additional features include detailed roadmap editing interfaces, comprehensive access control (RBAC), and enhanced non-functional requirements covering security, performance, and monitoring.
            </p>
            
            <h2>Measurable Business Impact Projections</h2>
            <ul>
              <li><strong>Time Savings:</strong> 80% reduction in career planning time per employee</li>
              <li><strong>Skill Development Acceleration:</strong> 50% faster skill acquisition through targeted learning</li>
              <li><strong>L&D ROI Improvement:</strong> Data-driven training investment optimization</li>
              <li><strong>Employee Engagement:</strong> Increased participation in learning initiatives</li>
            </ul>
            
            <h2>Commitment to AI-First Culture</h2>
            <p>
              Pathforge AI exemplifies the contest's vision of applied AI creating real outcomes and demonstrates our commitment to fostering 
              AI-first culture and human-AI collaboration principles established during the HackAIthon. The project serves as a catalyst for 
              broader AI adoption within FSOFT and establishes a foundation for future AI-powered organizational tools.
            </p>
          </div>
        </div>
        <div class="col-span-4 overflow-y-auto">
          <div class="space-y-6">
            <div class="bg-emerald-50 rounded-lg p-4">
              <h3 class="font-semibold text-emerald-800 mb-2">Business Impact</h3>
              <canvas id="businessImpactChart" class="w-full h-32"></canvas>
            </div>
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="font-semibold text-blue-800 mb-2">Scalability Metrics</h3>
              <div class="text-sm text-blue-700">
                <div>Concurrent Users: 1000+</div>
                <div>Response Time: <15s</div>
                <div>Uptime Target: 99.9%</div>
                <div>Auto-scaling: Dynamic</div>
              </div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
              <h3 class="font-semibold text-purple-800 mb-2">Future Features</h3>
              <div class="text-sm text-purple-700">
                <div>• Advanced Analytics</div>
                <div>• Mobile App</div>
                <div>• Team Dashboards</div>
                <div>• LLM Optimization</div>
              </div>
            </div>
            <div class="bg-orange-50 rounded-lg p-4">
              <h3 class="font-semibold text-orange-800 mb-2">3-Month Roadmap</h3>
              <div class="text-sm text-orange-700">
                <div>Month 1: System Integrations</div>
                <div>Month 2: Advanced Features</div>
                <div>Month 3: Enterprise Launch</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Slide 6: Thank You & Q&A -->
<div class="slide h-screen max-h-screen bg-gradient-to-br from-slate-50 to-gray-100 flex items-center justify-center p-8 overflow-hidden">
  <div class="max-w-4xl mx-auto text-center h-full max-h-full flex flex-col justify-center">
    <h1 class="text-6xl font-bold text-gray-900 mb-8">Thank You</h1>
    <div class="text-3xl text-gray-600 mb-12">Questions & Answers</div>
    <div class="bg-white rounded-2xl p-12 mx-auto shadow-xl border max-w-2xl">
      <div class="text-6xl mb-6">🎯</div>
      <div class="text-2xl text-gray-800 mb-4">Ready for Your Questions</div>
      <div class="text-lg text-gray-600 mb-6">
        We're excited to discuss the technical details, business impact, 
        and future vision of Pathforge AI with you
      </div>
      <div class="text-base text-gray-500 mb-4">Team CodePluse • HackAIthon 2025</div>
      <div class="text-sm text-gray-400">
        Thank you for your time and consideration. 
        We look forward to continuing this AI-first journey at FSOFT.
      </div>
    </div>
  </div>
</div>

<script>
// Team Roles Chart
const teamRolesCtx = document.getElementById('teamRolesChart').getContext('2d');
new Chart(teamRolesCtx, {
  type: 'doughnut',
  data: {
    labels: ['AI/ML', 'Backend', 'Frontend', 'DevOps', 'Testing', 'Design'],
    datasets: [{
      data: [30, 25, 20, 15, 8, 2],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(249, 115, 22, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(156, 163, 175, 0.8)'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } }
  }
});

// Problem Metrics Chart
const problemMetricsCtx = document.getElementById('problemMetricsChart').getContext('2d');
new Chart(problemMetricsCtx, {
  type: 'bar',
  data: {
    labels: ['Time', 'Manual Work', 'Errors'],
    datasets: [{
      data: [95, 90, 80],
      backgroundColor: ['rgba(239, 68, 68, 0.8)', 'rgba(249, 115, 22, 0.8)', 'rgba(245, 158, 11, 0.8)'],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: { y: { display: false }, x: { display: false } }
  }
});

// Pipeline Timing Chart
const pipelineTimingCtx = document.getElementById('pipelineTimingChart').getContext('2d');
new Chart(pipelineTimingCtx, {
  type: 'line',
  data: {
    labels: ['Input', 'Sync', 'Analysis', 'Generate', 'Export'],
    datasets: [{
      data: [2, 5, 8, 15, 3],
      backgroundColor: 'rgba(99, 102, 241, 0.2)',
      borderColor: 'rgba(99, 102, 241, 1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: { y: { display: false }, x: { display: false } }
  }
});

// Performance Chart
const performanceCtx = document.getElementById('performanceChart').getContext('2d');
new Chart(performanceCtx, {
  type: 'radar',
  data: {
    labels: ['Speed', 'Accuracy', 'Scalability', 'Reliability'],
    datasets: [{
      data: [95, 92, 88, 94],
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgba(59, 130, 246, 1)',
      borderWidth: 2
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: { r: { display: false } }
  }
});

// Business Impact Chart
const businessImpactCtx = document.getElementById('businessImpactChart').getContext('2d');
new Chart(businessImpactCtx, {
  type: 'bar',
  data: {
    labels: ['Time Savings', 'Engagement', 'ROI', 'Satisfaction'],
    datasets: [{
      data: [80, 65, 75, 85],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(147, 51, 234, 0.8)',
        'rgba(249, 115, 22, 0.8)'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: { y: { display: false }, x: { display: false } }
  }
});
</script>

</body>
</html>
