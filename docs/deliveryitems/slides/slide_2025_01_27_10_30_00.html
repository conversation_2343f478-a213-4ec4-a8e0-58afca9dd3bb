<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PathForge AI - HackAIthon 2025 Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .slide {
            display: none;
            aspect-ratio: 16/9;
        }

        .slide.active {
            display: flex;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 120px;
            height: auto;
            z-index: 10;
        }
    </style>
</head>

<body class="bg-gray-100 font-sans">
    <!-- Navigation Controls -->
    <div class="fixed bottom-6 right-6 z-20 flex gap-3">
        <button id="prevBtn"
            class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button id="nextBtn"
            class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="fixed bottom-6 left-6 z-20 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full">
        <span id="slideCounter">1 / 9</span>
    </div>

    <!-- FPT Software Logo (appears on all slides) -->
    <img src="https://www.pinclipart.com/picdir/big/396-3966460_fpt-fpt-software-logo-png-clipart.png"
        alt="FPT Software Logo" class="logo">

    <!-- Slide 1: Welcome Slide -->
    <div
        class="slide active w-full h-screen bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-5xl mx-auto text-center text-white fade-in">
            <div class="mb-8">
                <h1 class="text-6xl font-bold mb-6">PathForge AI</h1>
                <p class="text-2xl mb-4">AI-Powered Personalized Upskill Solution for FSOFT</p>
                <p class="text-xl mb-8 text-indigo-200">HackAIthon 2025 - Team CodePluse</p>
            </div>
            <div class="grid grid-cols-2 gap-8 text-lg">
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">Team Members</h3>
                    <div class="space-y-2">
                        <p>NamNH46 • PhongTN2 • TrungDD22</p>
                        <p>QuyetDB • DaiNQ11 • TruongPH2</p>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">AI-First Approach</h3>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-robot text-4xl mr-4"></i>
                        <span>Fostering AI Culture & Productivity</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 2: Table of Contents -->
    <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-6xl mx-auto w-full fade-in">
            <h1 class="text-4xl font-bold text-gray-900 mb-12 text-center">Table of Contents</h1>
            <div class="grid grid-cols-2 gap-8">
                <div class="space-y-6">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                        <div
                            class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            1</div>
                        <span class="text-lg">Project & Team Introduction</span>
                    </div>
                    <div class="flex items-center p-4 bg-green-50 rounded-lg">
                        <div
                            class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            2</div>
                        <span class="text-lg">The Problem & The AI Solution</span>
                    </div>
                    <div class="flex items-center p-4 bg-purple-50 rounded-lg">
                        <div
                            class="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            3</div>
                        <span class="text-lg">How it Works - MVP Pipeline</span>
                    </div>
                    <div class="flex items-center p-4 bg-orange-50 rounded-lg">
                        <div
                            class="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            4</div>
                        <span class="text-lg">Core AI Technology & Architecture</span>
                    </div>
                </div>
                <div class="space-y-6">
                    <div class="flex items-center p-4 bg-red-50 rounded-lg">
                        <div
                            class="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            5</div>
                        <span class="text-lg">Development Metrics & Quality</span>
                    </div>
                    <div class="flex items-center p-4 bg-indigo-50 rounded-lg">
                        <div
                            class="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            6</div>
                        <span class="text-lg">Live Demo - PathForge AI in Action</span>
                    </div>
                    <div class="flex items-center p-4 bg-teal-50 rounded-lg">
                        <div
                            class="bg-teal-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                            7</div>
                        <span class="text-lg">Impact & Future Vision</span>
                    </div>
                    <div class="text-center mt-8">
                        <div class="bg-gray-100 rounded-lg p-4">
                            <i class="fas fa-clock text-2xl text-gray-600 mb-2"></i>
                            <p class="text-gray-600">Presentation Duration: 5 minutes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 3: The Problem & The AI Solution -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">The Problem & AI Solution</h1>
                <h2 class="text-xl text-gray-600 mb-8">Why PathForge AI vs External Solutions</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                        <span class="text-lg font-semibold">Current Challenge</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-robot text-blue-500 mr-3"></i>
                        <span class="text-lg font-semibold">AI-First Solution</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-link text-green-500 mr-3"></i>
                        <span class="text-lg font-semibold">FSOFT Integration</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Content -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-2 gap-6 w-full">
                    <!-- Problem Section -->
                    <div class="bg-red-50 rounded-lg p-6 border-l-4 border-red-500">
                        <h3 class="text-xl font-bold text-red-700 mb-4">Current Bottleneck</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-clock text-red-500 mr-2 mt-1"></i>
                                <span>Time-consuming skill gap analysis</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-puzzle-piece text-red-500 mr-2 mt-1"></i>
                                <span>Complex learning path creation</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-chart-line text-red-500 mr-2 mt-1"></i>
                                <span>Manual process doesn't scale</span>
                            </li>
                        </ul>
                    </div>
                    <!-- Solution Section -->
                    <div class="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                        <h3 class="text-xl font-bold text-blue-700 mb-4">AI-First Approach</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-users-cog text-blue-500 mr-2 mt-1"></i>
                                <span>Multi-Agent automation</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-database text-blue-500 mr-2 mt-1"></i>
                                <span>Large-scale data processing</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-rocket text-blue-500 mr-2 mt-1"></i>
                                <span>Personalized roadmaps at scale</span>
                            </li>
                        </ul>
                    </div>
                    <!-- Integration Diagram -->
                    <div class="col-span-2 bg-green-50 rounded-lg p-6 border-l-4 border-green-500">
                        <h3 class="text-xl font-bold text-green-700 mb-4 text-center">Deep FSOFT Integration</h3>
                        <div class="flex justify-center items-center space-x-4">
                            <div class="text-center">
                                <div class="bg-blue-600 text-white rounded-lg p-3 mb-2">
                                    <i class="fas fa-briefcase text-xl"></i>
                                </div>
                                <span class="text-sm">AkaJob</span>
                            </div>
                            <i class="fas fa-arrow-right text-green-600"></i>
                            <div class="text-center">
                                <div class="bg-purple-600 text-white rounded-lg p-3 mb-2">
                                    <i class="fas fa-brain text-xl"></i>
                                </div>
                                <span class="text-sm">PathForge AI</span>
                            </div>
                            <i class="fas fa-arrow-right text-green-600"></i>
                            <div class="text-center">
                                <div class="bg-orange-600 text-white rounded-lg p-3 mb-2">
                                    <i class="fas fa-graduation-cap text-xl"></i>
                                </div>
                                <span class="text-sm">LevelUp</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 4: How it Works - MVP Pipeline -->
    <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">How it Works</h1>
                <h2 class="text-xl text-gray-600 mb-8">MVP Pipeline Demo Flow</h2>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div
                            class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 text-sm font-bold">
                            1</div>
                        <span>Goal Input</span>
                    </div>
                    <div class="flex items-center">
                        <div
                            class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 text-sm font-bold">
                            2</div>
                        <span>Skill Sync</span>
                    </div>
                    <div class="flex items-center">
                        <div
                            class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 text-sm font-bold">
                            3</div>
                        <span>Gap Analysis</span>
                    </div>
                    <div class="flex items-center">
                        <div
                            class="bg-orange-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 text-sm font-bold">
                            4</div>
                        <span>Roadmap Gen</span>
                    </div>
                    <div class="flex items-center">
                        <div
                            class="bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 text-sm font-bold">
                            5</div>
                        <span>Report Export</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Pipeline Flow -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full">
                    <!-- Pipeline Steps -->
                    <div class="space-y-4">
                        <!-- Step 1 -->
                        <div class="flex items-center bg-blue-50 rounded-lg p-4">
                            <div
                                class="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-4">
                                <i class="fas fa-target"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-bold text-blue-700">Goal & Constraint Input</h3>
                                <p class="text-gray-600">React frontend with TypeScript validation, chat interface</p>
                            </div>
                            <i class="fas fa-arrow-down text-blue-600 text-xl"></i>
                        </div>
                        <!-- Step 2 -->
                        <div class="flex items-center bg-green-50 rounded-lg p-4">
                            <div
                                class="bg-green-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-4">
                                <i class="fas fa-sync"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-bold text-green-700">Skill Sync</h3>
                                <p class="text-gray-600">CV Extractor Agent, PostgreSQL storage, JSON validation</p>
                            </div>
                            <i class="fas fa-arrow-down text-green-600 text-xl"></i>
                        </div>
                        <!-- Step 3 -->
                        <div class="flex items-center bg-purple-50 rounded-lg p-4">
                            <div
                                class="bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-4">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-bold text-purple-700">Gap Analysis</h3>
                                <p class="text-gray-600">RAG system with ChromaDB, AI advisor clarifications</p>
                            </div>
                            <i class="fas fa-arrow-down text-purple-600 text-xl"></i>
                        </div>
                        <!-- Step 4 -->
                        <div class="flex items-center bg-orange-50 rounded-lg p-4">
                            <div
                                class="bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-4">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-bold text-orange-700">Learning Roadmap</h3>
                                <p class="text-gray-600">Course recommendations, time-based milestones</p>
                            </div>
                            <i class="fas fa-arrow-down text-orange-600 text-xl"></i>
                        </div>
                        <!-- Step 5 -->
                        <div class="flex items-center bg-red-50 rounded-lg p-4">
                            <div
                                class="bg-red-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-4">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-bold text-red-700">Report Generation</h3>
                                <p class="text-gray-600">Markdown summaries, PDF export, progress tracking</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 5: Core AI Technology & Architecture -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Core AI Technology</h1>
                <h2 class="text-xl text-gray-600 mb-8">Architecture & Development Stack</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-brain text-purple-500 mr-3"></i>
                        <span class="text-lg font-semibold">LangGraph Agents</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-server text-blue-500 mr-3"></i>
                        <span class="text-lg font-semibold">FastAPI Service</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-database text-green-500 mr-3"></i>
                        <span class="text-lg font-semibold">RAG System</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Architecture Diagram -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full">
                    <!-- Architecture Components -->
                    <div class="grid grid-cols-3 gap-6 mb-6">
                        <!-- Frontend -->
                        <div class="bg-blue-50 rounded-lg p-4 text-center border-2 border-blue-200">
                            <i class="fas fa-desktop text-3xl text-blue-600 mb-2"></i>
                            <h3 class="font-bold text-blue-700">Frontend</h3>
                            <p class="text-sm text-gray-600">React + Mantine UI</p>
                        </div>
                        <!-- Backend -->
                        <div class="bg-green-50 rounded-lg p-4 text-center border-2 border-green-200">
                            <i class="fas fa-cogs text-3xl text-green-600 mb-2"></i>
                            <h3 class="font-bold text-green-700">Backend</h3>
                            <p class="text-sm text-gray-600">Express.js + PostgreSQL</p>
                        </div>
                        <!-- AI Service -->
                        <div class="bg-purple-50 rounded-lg p-4 text-center border-2 border-purple-200">
                            <i class="fas fa-robot text-3xl text-purple-600 mb-2"></i>
                            <h3 class="font-bold text-purple-700">AI Service</h3>
                            <p class="text-sm text-gray-600">LangGraph + FastAPI</p>
                        </div>
                    </div>

                    <!-- Technology Stack -->
                    <div class="grid grid-cols-2 gap-6">
                        <!-- Core Technologies -->
                        <div class="bg-white rounded-lg p-4 shadow-md">
                            <h3 class="font-bold text-gray-800 mb-3">Core Technologies</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                    <span class="text-sm">LangGraph v0.3 Multi-Agent</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <span class="text-sm">FastAPI RESTful Service</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-sm">MindsDB RAG Framework</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Supabase OAuth2</span>
                                </div>
                            </div>
                        </div>

                        <!-- Data Sources -->
                        <div class="bg-white rounded-lg p-4 shadow-md">
                            <h3 class="font-bold text-gray-800 mb-3">Data Sources</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <span class="text-sm">AkaJob Skills (Mockup)</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-sm">FSOFT Career Paths</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                                    <span class="text-sm">LevelUp + Udemy Courses</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                    <span class="text-sm">Employee CVs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 6: Development Metrics & Quality -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-green-50 to-blue-100 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Development Metrics</h1>
                <h2 class="text-xl text-gray-600 mb-8">Quality Assurance & Performance</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-code text-blue-500 mr-3"></i>
                        <span class="text-lg font-semibold">25K+ Lines of Code</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt text-green-500 mr-3"></i>
                        <span class="text-lg font-semibold">Grade A Quality</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-dollar-sign text-purple-500 mr-3"></i>
                        <span class="text-lg font-semibold">Cost Optimized</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Metrics Dashboard -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full">
                    <!-- Metrics Grid -->
                    <div class="grid grid-cols-2 gap-6">
                        <!-- Code Metrics -->
                        <div class="bg-white rounded-lg p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Code Development</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Total LOC</span>
                                    <span class="font-bold text-blue-600">25,000+</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">TypeScript</span>
                                    <span class="font-bold text-green-600">63%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Python</span>
                                    <span class="font-bold text-purple-600">27%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Config</span>
                                    <span class="font-bold text-orange-600">10%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Quality Metrics -->
                        <div class="bg-white rounded-lg p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">SonarQube Quality</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Code Quality</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded font-bold">A</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Security</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded font-bold">A</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Maintainability</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded font-bold">A</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Reliability</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded font-bold">A</span>
                                </div>
                            </div>
                        </div>

                        <!-- Test Coverage -->
                        <div class="bg-white rounded-lg p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Test Coverage</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Backend</span>
                                    <div class="flex items-center">
                                        <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 95%"></div>
                                        </div>
                                        <span class="font-bold text-green-600">95%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Frontend</span>
                                    <div class="flex items-center">
                                        <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 29%"></div>
                                        </div>
                                        <span class="font-bold text-yellow-600">29%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">AI Service</span>
                                    <div class="flex items-center">
                                        <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 15%"></div>
                                        </div>
                                        <span class="font-bold text-red-600">15%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cost Analysis -->
                        <div class="bg-white rounded-lg p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">AI Cost Analysis</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Per Analysis</span>
                                    <span class="font-bold text-blue-600">$0.0045</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Per Roadmap</span>
                                    <span class="font-bold text-green-600">$0.012</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Monthly (1K users)</span>
                                    <span class="font-bold text-purple-600">$340</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Dev Tools</span>
                                    <span class="font-bold text-orange-600">$364/mo</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Completion Status -->
                    <div class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-semibold">Overall Completion</span>
                            <span class="text-2xl font-bold">87%</span>
                        </div>
                        <div class="mt-2 bg-white bg-opacity-20 rounded-full h-3">
                            <div class="bg-white h-3 rounded-full" style="width: 87%"></div>
                        </div>
                        <p class="text-sm mt-2 opacity-90">156 of 179 functions completed</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 7: Live Demo -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-indigo-50 to-cyan-100 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Live Demo</h1>
                <h2 class="text-xl text-gray-600 mb-8">PathForge AI in Action</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-user-circle text-blue-500 mr-3"></i>
                        <span class="text-lg font-semibold">Sarah's Journey</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-arrow-right text-green-500 mr-3"></i>
                        <span class="text-lg font-semibold">Full-Stack → AI/ML</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock text-purple-500 mr-3"></i>
                        <span class="text-lg font-semibold">6 Months Timeline</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Demo Flow -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full">
                    <!-- Demo Steps -->
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Step 1: Authentication -->
                        <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                            <div class="flex items-center">
                                <div
                                    class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                                    1</div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-blue-700">User Authentication & Goal Setting</h3>
                                    <p class="text-gray-600">Supabase OAuth2 → Career Goal: "AI/ML Engineer" → 6 months,
                                        10 hrs/week</p>
                                </div>
                                <i class="fas fa-sign-in-alt text-blue-600 text-xl"></i>
                            </div>
                        </div>

                        <!-- Step 2: CV Analysis -->
                        <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                            <div class="flex items-center">
                                <div
                                    class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                                    2</div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-green-700">Skill Profile & CV Analysis</h3>
                                    <p class="text-gray-600">CV Extractor Agent → JSON validation → AkaJob integration
                                        mockup</p>
                                </div>
                                <i class="fas fa-file-upload text-green-600 text-xl"></i>
                            </div>
                        </div>

                        <!-- Step 3: Gap Analysis -->
                        <div class="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                            <div class="flex items-center">
                                <div
                                    class="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                                    3</div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-purple-700">AI-Powered Gap Analysis</h3>
                                    <p class="text-gray-600">RAG system queries → Clarifying questions → Personalized
                                        recommendations</p>
                                </div>
                                <i class="fas fa-brain text-purple-600 text-xl"></i>
                            </div>
                        </div>

                        <!-- Step 4: Roadmap Generation -->
                        <div class="bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500">
                            <div class="flex items-center">
                                <div
                                    class="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                                    4</div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-orange-700">Learning Roadmap Generation</h3>
                                    <p class="text-gray-600">LevelUp + Udemy integration → Time-based milestones →
                                        Progress tracking</p>
                                </div>
                                <i class="fas fa-route text-orange-600 text-xl"></i>
                            </div>
                        </div>

                        <!-- Step 5: Report Export -->
                        <div class="bg-red-50 rounded-lg p-4 border-l-4 border-red-500">
                            <div class="flex items-center">
                                <div
                                    class="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 font-bold">
                                    5</div>
                                <div class="flex-1">
                                    <h3 class="font-bold text-red-700">Report Generation & Export</h3>
                                    <p class="text-gray-600">Markdown summary → PDF export → Manager review format</p>
                                </div>
                                <i class="fas fa-download text-red-600 text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Highlights -->
                    <div class="mt-6 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-4 text-white">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold">
                                    < 15s</div>
                                        <div class="text-sm opacity-90">Roadmap Generation</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold">1000+</div>
                                    <div class="text-sm opacity-90">Concurrent Users</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold">Azure</div>
                                    <div class="text-sm opacity-90">AKS Deployment</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: Impact & Future Vision -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-teal-50 to-green-100 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
            <!-- Left side: Title and Subtitle -->
            <div class="col-span-4 flex flex-col justify-start pt-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Impact & Future</h1>
                <h2 class="text-xl text-gray-600 mb-8">Business Impact & Post-HackAIthon Roadmap</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-users text-blue-500 mr-3"></i>
                        <span class="text-lg font-semibold">Employee Impact</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-building text-green-500 mr-3"></i>
                        <span class="text-lg font-semibold">FSOFT Benefits</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-rocket text-purple-500 mr-3"></i>
                        <span class="text-lg font-semibold">Future Roadmap</span>
                    </div>
                </div>
            </div>
            <!-- Right side: Impact & Future Content -->
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full">
                    <!-- Impact Grid -->
                    <div class="grid grid-cols-2 gap-6 mb-6">
                        <!-- Employee Impact -->
                        <div class="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                            <h3 class="text-xl font-bold text-blue-700 mb-4">For FSOFT Employees</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-blue-500 mr-2 mt-1"></i>
                                    <span>Personalized upskilling guidance</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-clock text-blue-500 mr-2 mt-1"></i>
                                    <span>Time savings on research</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-user-graduate text-blue-500 mr-2 mt-1"></i>
                                    <span>Own career growth journey</span>
                                </li>
                            </ul>
                        </div>

                        <!-- FSOFT Impact -->
                        <div class="bg-green-50 rounded-lg p-6 border-l-4 border-green-500">
                            <h3 class="text-xl font-bold text-green-700 mb-4">For FSOFT</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-start">
                                    <i class="fas fa-brain text-green-500 mr-2 mt-1"></i>
                                    <span>Foster AI culture</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-chart-line text-green-500 mr-2 mt-1"></i>
                                    <span>Enhanced productivity</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-database text-green-500 mr-2 mt-1"></i>
                                    <span>L&D strategy insights</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Technical Achievements -->
                    <div class="bg-white rounded-lg p-6 shadow-lg mb-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Production-Ready Achievements</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-docker text-blue-500 mr-2"></i>
                                    <span class="text-sm">Containerized deployment</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                                    <span class="text-sm">Security implementation</span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-tachometer-alt text-purple-500 mr-2"></i>
                                    <span class="text-sm">Performance monitoring</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-expand-arrows-alt text-orange-500 mr-2"></i>
                                    <span class="text-sm">Auto-scaling ready</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Future Roadmap -->
                    <div class="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg p-6 text-white">
                        <h3 class="text-xl font-bold mb-4">Phase 2 - Next 3 Months</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-plug text-yellow-300 mr-2"></i>
                                    <span class="text-sm">Deep system integration</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-mobile-alt text-yellow-300 mr-2"></i>
                                    <span class="text-sm">Mobile accessibility</span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-chart-bar text-yellow-300 mr-2"></i>
                                    <span class="text-sm">Advanced analytics</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-dollar-sign text-yellow-300 mr-2"></i>
                                    <span class="text-sm">Cost optimization</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 9: Thank You -->
    <div
        class="slide w-full h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-8 overflow-hidden">
        <div class="max-w-4xl mx-auto text-center text-white fade-in">
            <h1 class="text-5xl font-bold mb-8">Thank You</h1>
            <h2 class="text-3xl mb-12">Questions & Discussion</h2>

            <div class="grid grid-cols-2 gap-8 mb-8">
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">Team CodePluse</h3>
                    <div class="space-y-2 text-lg">
                        <p>NamNH46 • PhongTN2 • TrungDD22</p>
                        <p>QuyetDB • DaiNQ11 • TruongPH2</p>
                    </div>
                </div>
                <div class="bg-white bg-opacity-10 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">PathForge AI</h3>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-brain text-4xl mr-4"></i>
                        <div>
                            <p class="text-lg">AI-First Solution</p>
                            <p class="text-sm opacity-90">for FSOFT Upskilling</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-xl">
                <p class="mb-4">🚀 Ready to scale and bring real value to FSOFT</p>
                <p class="text-lg opacity-90">Committed to AI-first culture & collaboration</p>
            </div>
        </div>
    </div>

    <script>
        // Slide navigation functionality
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        const slideCounter = document.getElementById('slideCounter');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // Update slide counter
        function updateSlideCounter() {
            slideCounter.textContent = `${currentSlide + 1} / ${totalSlides}`;
        }

        // Show specific slide
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active');
                if (i === index) {
                    slide.classList.add('active');
                    // Add fade-in animation to content
                    const content = slide.querySelector('.fade-in');
                    if (content) {
                        content.style.animation = 'none';
                        setTimeout(() => {
                            content.style.animation = 'fadeIn 0.5s ease-in';
                        }, 10);
                    }
                }
            });

            // Update navigation buttons
            prevBtn.disabled = index === 0;
            nextBtn.disabled = index === totalSlides - 1;

            updateSlideCounter();
        }

        // Next slide
        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        // Previous slide
        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        // Event listeners
        nextBtn.addEventListener('click', nextSlide);
        prevBtn.addEventListener('click', prevSlide);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    prevSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    currentSlide = 0;
                    showSlide(currentSlide);
                    break;
                case 'End':
                    e.preventDefault();
                    currentSlide = totalSlides - 1;
                    showSlide(currentSlide);
                    break;
            }
        });

        // Initialize
        showSlide(0);
        updateSlideCounter();
    </script>
</body>

</html>