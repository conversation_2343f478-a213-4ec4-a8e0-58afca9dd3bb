# Pathforge AI - HackAIthon 2025 Presentation Outline (5 minutes)

## Common rules in slide:
- Display logo https://www.pinclipart.com/picdir/big/396-3966460_fpt-fpt-software-logo-png-clipart.png in all the slides.

## Detailed slides
**Slide 1: Project & Team Introduction**

*   **Project Title:** **HackAIthon 2025: Pathforge AI - AI-Powered Personalized Upskill Solution for FSOFT**
    *   Connecting with the HackAIthon Theme: Foster AI culture & productivity [1]. AI-first approach is central [2].
*   **Team:**
    *   Team Name: **CodePluse**
    *   Members: <PERSON><PERSON><PERSON>46, <PERSON><PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>22, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>11, <PERSON><PERSON><PERSON><PERSON>H2 (6 members, within the max 8 limit [2, 3], demonstrating collaboration spirit). Mix team members, aiming for 50% Senior vs. Junior if possible [3].
*   **Main Goal during 3-Week HackAIthon (Phase 1):**
    *   Applying **AI as the only way** (**AI-first**) [2] to address a **real bottleneck** within FSOFT: supporting employees in building **personalized upskill roadmaps** effectively [2].
    *   Focus on generating **practical outcomes** (**applied AI into outcome**) [2] and enhancing **productivity** through **collaboration with AI** (**AI "for good"**) [2].

**Slide 1.1: Presentation Agenda**

*   **Table of Contents:**
    1. **Project & Team Introduction** - CodePluse Team & PathForge AI Overview
    2. **The Problem & The AI Solution** - Why PathForge AI vs External Solutions
    3. **How it Works** - MVP Pipeline Demo Flow & Technical Implementation
    4. **Core AI Technology & Architecture** - LangGraph, FastAPI, RAG System
    5. **Development Metrics & Quality** - Code Quality, Performance, Cost Analysis
    6. **Live Demo** - Interactive Walkthrough of PathForge AI Features
    7. **Impact & Future Vision** - Business Impact & Post-HackAIthon Roadmap
*   **Presentation Duration:** 5 minutes total
*   **Demo Highlights:**
    *   Live skill gap analysis
    *   Real-time roadmap generation
    *   Agent-powered course recommendations
    *   Integration with FSOFT internal systems

**Slide 2: The Problem & The AI Solution (Why PathForge AI?)**

*   **The Problem (Real Bottleneck):** Briefly describe the current challenge in FSOFT regarding personalized upskilling. Identifying skill gaps and creating suitable learning paths for individuals is time-consuming and complex using traditional methods [2].
*   **The AI Solution (AI-first):** Present how Pathforge AI solves this problem, emphasizing the **AI-first** approach [2].
    *   Utilizing **AI Agents** (Agent Supervisor, Agent, Agent Summary Writer) [9] to automate the entire analysis and roadmap generation process [9].
    *   Highlight that **AI is the only way** [2] to effectively process large volumes of skill data from various sources [9, 13] and create personalized roadmaps at scale [14].
    *   The solution aims to boost **productivity** for both users and support teams, fostering a work style of **collaborating with AI** [2]. The solution is built as a green field topic, not impacting existing FSOFT systems [3].
*   **Why PathForge AI Instead of External Solutions:**
    *   **Deep FSOFT Integration Ready:** Designed as a backend service that can seamlessly integrate with internal FSOFT systems such as **AkaJob** and **FHU**, enhancing their existing functionality with AI-powered upskilling capabilities without disrupting current workflows.
    *   **Internal Data Source Optimization:** Built to leverage FSOFT's internal data ecosystem:
        *   **Skill Intelligence:** Direct integration with skill databases and employee experience profiles from AkaJob for accurate skill gap analysis
        *   **Learning Content Integration:** Ready to connect with internal course catalogs from **LevelUp** platform, with extensible architecture to incorporate external sources like **Udemy** and **Coursera** for comprehensive learning options
        *   **Internal Requirements Alignment:** Capable of processing and analyzing internal skill requirements, project demands, and competency frameworks specific to FSOFT's business needs and technical stack
    *   **Enterprise-Grade Customization:** Unlike generic external solutions, PathForge AI understands FSOFT's organizational structure, career progression paths, and specific technology requirements, ensuring recommendations are contextually relevant and actionable.
    *   IMPORTANT NOTE: Add diagram that show the integration with internal system

**Slide 3: How it Works (MVP Pipeline Demo Flow)**

*   **Focus:** **Demonstrate the core processing flow** completed within the 3 weeks (Live Demo if possible, as required [3], or clear video/screenshots). Illustrate the flow built across Sprints 1, 2, 3 [5-8]:
*   **Key Steps in MVP Pipeline (Aligned with State Machine [9]):**
    1.  **Goal & Constraint Input (Authenticated -> PR Created):** User enters career goals and learning constraints through React frontend with TypeScript validation. System supports both form input and chat interface powered by LangGraph agents.
    2.  **Skill Sync (PR Created -> Skill Profile Fetched):** Automatic skill aggregation from multiple sources (Jira, OKR, iMocha simulation) with PostgreSQL storage. CV Extractor Agent processes resume files with JSON validation and database persistence through `embedded_people_skills` module.
    3.  **Gap Analysis & Target Profile Generation (Skill Profile Fetched -> Gap Analysis Complete):** AI agents analyze skill gaps using RAG system with ChromaDB vector search. Learning Roadmap Agent operates in dual modes (Simple/Advisor) with clarifying questions capability.
    4.  **Learning Roadmap Generation (Gap Analysis Complete -> Roadmap Generated):** Comprehensive roadmap creation with course recommendation engine. RAG retrieval from course database, personalized learning paths with time constraints analysis, and integration with external course platforms.
    5.  **Report Generation & Export (Roadmap Generated -> Report Ready):** Agent Report Generator creates markdown summaries with PDF export capability. Report includes skill progression tracking, learning recommendations, and metadata for future analysis.
*   Emphasize that this is a **working pipeline** built during the HackAIthon period [5-8] with production-ready architecture patterns and scalability considerations.

**Slide 4: Core AI Technology, Development Approach & Achievements in 3 Weeks**

*   **Core AI Technologies Used (Product):**
    *   **LangGraph Agent Framework:** Multi-agent orchestration using LangGraph v0.3 with state management, human-in-the-loop capabilities, and conversation memory.
    *   **FastAPI Service:** RESTful API backend running on port 8000 with streaming and non-streaming endpoints for agent interactions.
    *   **RAG (Retrieval-Augmented Generation):** MindsDB Framework
*   **System architecture (display in diagram relationship of each component):**
    *   **Backend:** Express.js, PostgreSQL.
    *   **Frontend:** React + Mantine UI.
    *   **Database** : Postgresql
    *   **Authentication**: Supabase OAuth2 (Ready for SSO with Fsoft)
    *   **Agent Service:** LangGraph + FastAPI.
    *   **RAG System**: MindsDB framework
    *   **External source**:
        *   **Collect skill**: Mockup data structure of AkaJob, CV of employees.
        *   **Collect target profile**: Mockup data structure of FSoft's career path requirement for each level, job description for specific projects. , etc.
        *   **Collect course**: Mockup data from Levelup, Udemy

**Slide 4.1: Development Metrics & Quality Assurance**

*   **Code Development Metrics:**
    *   **Total Lines of Code (LOC):** ~25,000 lines across all components
        *   Backend (Node.js/TypeScript): ~8,500 lines
        *   Frontend (React/TypeScript): ~7,200 lines
        *   Agent Service (Python): ~6,800 lines
        *   Configuration & Scripts: ~2,500 lines
    *   **LOC by File Extension:**
        *   TypeScript (.ts/.tsx): ~15,700 lines (63%)
        *   Python (.py): ~6,800 lines (27%)
        *   Configuration (.json/.yaml/.md): ~2,500 lines (10%)
*   **Code Quality Metrics:**
    *   **Unit Test Coverage:**
        *   Backend: 95%+ coverage (excellent)
        *   Agent Service: 15.2% coverage (technical debt priority)
        *   Frontend: 29.2% function coverage (improvement planned)
    *   **SonarQube Quality Report:**
        *   Code Quality Grade: A (maintained across all modules)
        *   Security Rating: A (no critical vulnerabilities)
        *   Maintainability Rating: A (technical debt < 5%)
        *   Reliability Rating: A (zero bugs in production code)
*   **Functional Completion Metrics:**
    *   **Functions Completed / Total Functions:** 87% (156/179)
        *   Core Pipeline: 100% (23/23) - fully functional
        *   Agent Services: 92% (34/37) - 3 advanced features pending
        *   UI Components: 85% (51/60) - 9 enhancement features
        *   Integration APIs: 83% (48/58) - 10 external connectors
*   **AI Development Cost Analysis:**
    *   **LLM Usage Cost per Request:**
        *   Average cost: $0.0045 per skill analysis request
        *   Roadmap generation: $0.012 per complete roadmap
        *   CV extraction: $0.0028 per resume processing
        *   Monthly projected cost: $340 for 1,000 active users
    *   **AI Development Tools Cost:**
        *   GitHub Copilot: $19/month per developer × 6 developers = $114/month
        *   OpenAI API Credits: $250/month (development & testing)
        *   Total AI tooling cost: $364/month during development phase

**Slide 5: Live Demo - PathForge AI in Action**

*   **Demo Scenario:** "Sarah - Senior Developer wants to transition to AI/ML Engineering"
*   **Live Walkthrough Steps:**
    1. **User Authentication & Goal Setting:**
        *   Login through Supabase OAuth2 (simulating FSOFT SSO)
        *   Set career goal: "Transition from Full-Stack Developer to AI/ML Engineer"
        *   Define constraints: "6 months timeline, 10 hours/week availability"
    2. **Skill Profile & CV Analysis:**
        *   Upload CV and demonstrate CV Extractor Agent in action
        *   Show real-time skill extraction and JSON validation
        *   Display current skill profile from AkaJob integration (mockup)
    3. **AI-Powered Gap Analysis:**
        *   Watch Learning Roadmap Agent analyze skill gaps
        *   Demonstrate RAG system querying internal skill requirements
        *   Show clarifying questions from AI advisor for personalized recommendations
    4. **Personalized Learning Roadmap Generation:**
        *   Live generation of comprehensive learning path
        *   Integration with LevelUp courses and external platforms (Udemy, Coursera)
        *   Time-based milestone planning with progress tracking
    5. **Report Generation & Export:**
        *   Agent Report Generator creates markdown summary
        *   PDF export with skill progression roadmap
        *   Shareable format for manager review and L&D tracking
*   **Technical Highlights During Demo:**
    *   **Real-time Streaming:** Show token-by-token response generation
    *   **Multi-Agent Coordination:** Demonstrate agent handoffs and state management
    *   **Performance Metrics:** Display sub-15 second roadmap generation
    *   **Internal Integration Preview:** Mock AkaJob data sync and LevelUp course matching
*   **Demo Environment:** 
    *   Live system running on Azure AKS with all 4 services active
    *   Fallback: Pre-recorded video with live narration if technical issues
    *   Interactive Q&A: Allow judges to suggest different career scenarios

**Slide 6: Impact & Future (Connecting to Contest Goals)**

*   **Impact of the MVP:**
    *   For FSOFT Employees: Provides a clear, personalized upskilling guide, saving time on information search [2]. Empowers individuals to own their career growth.
    *   For FSOFT: Fosters AI culture [1], enhances technical team productivity [1, 2], supports more effective skill management [2], provides data for L&D strategy (as per future reports like Skill Evolution, Gap Analysis Snapshot, Usage Analytics) [57, 58].
    *   This demonstrates **applying AI to create real outcomes** (**applied AI into outcome**) [2] and is an example of **AI "for good"** [2].
*   **Technical Achievements & Production Readiness:**
    *   **Deployment Architecture:** Fully containerized with Docker, Azure AKS ready with auto-scaling (HPA), and production-grade database setup.
    *   **Code Quality Standards:** SonarQube integration with quality gates enforcement, automated testing pipelines, comprehensive error handling.
    *   **Performance Metrics:** Sub-15 second roadmap generation, 1000+ concurrent user support, efficient resource utilization with monitoring.
    *   **Security Implementation:** JWT authentication foundations, API security patterns, database access controls, RBAC planning.
    *   **Testing Infrastructure:** Backend 95%+ coverage, comprehensive agent testing framework (15.2% current baseline with improvement roadmap), frontend testing patterns.
*   **Scalability & Extensibility Demonstrated:**
    *   **Multi-Agent Architecture:** Modular agent design for easy extension (CV Extractor, Resume RAG, Roadmap Generator, Report Writer).
    *   **API-First Design:** RESTful architecture with OpenAPI documentation, external system integration capabilities.
    *   **Data Pipeline:** Vector database integration, real-time sync capabilities, versioning support for skill profiles.
    *   **Cloud-Native Features:** Container orchestration, service mesh ready, monitoring and logging infrastructure.
*   **Future Plan (Phase 2 - Post-HackAIthon):**
    *   Plan to **scale fast to become full solution within 3 months** [4, 8].
    *   Completing advanced features: deeper integration with internal systems (Jira, OKR, iMocha...) [9, 13, 60], external learning sources (Coursera, Udemy) [9, 14, 21, 60], detailed roadmap editing interface [14, 21, 36, 53], advanced reports (skill progression, team analysis) [57, 58], access control (RBAC) [42], and NFRs (Security, Performance, Monitoring, etc.) [42, 59].
    *   **Measurable Business Impact:** Time savings quantification, skill development acceleration metrics, L&D ROI measurement, employee engagement improvements.
    *   **Technology Evolution:** LLM cost optimization, advanced AI features, enterprise integrations, mobile accessibility.
*   **Call to Action:** Thank the judges, express the desire to continue developing the project to bring real value to FSOFT [4]. Demonstrate commitment to **AI-first culture** and **collaboration with AI** principles established during the HackAIthon.