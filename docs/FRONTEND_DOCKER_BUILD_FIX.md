# Frontend Docker Build Troubleshooting Guide

## Issue Resolution Summary

The frontend Docker build was failing with error:
```
ERROR: failed to solve: process "/bin/sh -c yarn install --frozen-lockfile" did not complete successfully: exit code: 1
```

## Root Causes and Fixes Applied

### 1. Node.js Version Mismatch
- **Problem**: Dockerfile used Node 20, but `.nvmrc` specified Node 22.11.0
- **Fix**: Updated Dockerfile to use `node:22-alpine`

### 2. Missing System Dependencies  
- **Problem**: Alpine Linux was missing build tools needed for native modules
- **Fix**: Added system dependencies:
  ```dockerfile
  RUN apk add --no-cache \
      git \
      python3 \
      make \
      g++ \
      libc6-compat \
      vips-dev
  ```

### 3. Docker Platform Compatibility
- **Problem**: Build issues on Apple Silicon Macs
- **Fix**: Added explicit platform specification: `FROM --platform=linux/amd64 node:22-alpine`

### 4. Yarn Configuration Issues
- **Problem**: Default yarn settings weren't optimal for Docker environment
- **Fix**: Added explicit yarn configuration:
  ```dockerfile
  RUN yarn cache clean && \
      yarn config set network-timeout 600000 && \
      yarn config set registry https://registry.npmjs.org/ && \
      yarn install --frozen-lockfile --verbose
  ```

### 5. Docker Compose Configuration
- **Problem**: Referenced non-existent `package-lock.json` in watch configuration
- **Fix**: Updated `compose.yaml` to reference `yarn.lock` instead

### 6. Production Target
- **Problem**: Docker Compose expected a `production` target
- **Fix**: Added explicit production stage in Dockerfile

## Testing the Fix

Run the test script to verify the build works:
```bash
./scripts/test-frontend-build.sh
```

Or manually test:
```bash
# Build the image
docker build -f docker/Dockerfile.frontend -t pathforge-ai-frontend .

# Run the container
docker run -p 3000:8080 pathforge-ai-frontend
```

## Key Improvements Made

1. **Updated Node.js version** from 20 to 22 to match project requirements
2. **Added comprehensive system dependencies** for native module compilation
3. **Improved yarn configuration** with better timeout and registry settings
4. **Added platform specification** for better compatibility
5. **Fixed Docker Compose references** to use correct lock file
6. **Added production target** for proper multi-stage builds
7. **Created test script** for easier validation

## Future Considerations

1. Consider using `.nvmrc` to dynamically set Node version in Dockerfile
2. Monitor Alpine Linux package updates that might affect builds
3. Consider caching yarn cache between builds for faster builds
4. Add health checks to the production container

## Files Modified

- `docker/Dockerfile.frontend` - Main Docker build configuration
- `compose.yaml` - Fixed watch configuration for yarn.lock
- `scripts/test-frontend-build.sh` - Added test script (new file)

The build should now work correctly with the applied fixes.
