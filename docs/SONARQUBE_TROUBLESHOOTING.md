# SonarQube Scan Troubleshooting Guide

This document provides solutions for common SonarQube scanning issues encountered in CI/CD pipelines.

## Common Error: Scanner Engine Download Timeout

### Problem
```
java.net.SocketTimeoutException: timeout
Caused by: Fail to download sonar-scanner-engine-shaded-10.3.0.82913-all.jar
```

### Root Causes
1. **Network timeouts** - GitHub Actions runner has slow connection to SonarQube server
2. **Large file downloads** - Scanner engine JAR is ~50MB and can timeout
3. **Server overload** - SonarQube server is under heavy load
4. **Firewall/proxy issues** - Network restrictions blocking the download

### Solutions Applied

#### 1. Enhanced Retry Logic
- Multiple download attempts with exponential backoff
- Alternative download sources (GitHub releases as fallback)
- Better error handling and logging

#### 2. Improved Network Configuration
```bash
# Added network timeout settings
-Dsonar.ws.timeout=300
-Dhttp.socketTimeout=600000
-Dhttp.connectionTimeout=60000
```

#### 3. Better Memory Management
```bash
export JAVA_OPTS="-Xmx3072m -XX:+UseG1GC -XX:+UseStringDeduplication"
export SONAR_SCANNER_OPTS="-Xmx3072m -XX:+UseG1GC"
```

#### 4. Caching Strategy
- Cache SonarScanner installation across workflow runs
- Skip plugin pre-download (let SonarQube handle it)
- Better cache key strategy

#### 5. Fallback Mechanisms
- Simplified scan if full scan fails
- Skip quality gate option for testing
- Alternative scanner installation methods

## Testing Your Setup

### 1. Run Connection Test
```bash
# Set your environment variables
export SONAR_HOST_URL="https://your-sonarqube-server.com"
export SONAR_TOKEN="your-token"

# Run the connection test script
./scripts/test-sonarqube-connection.sh
```

### 2. Use Test Workflow
Run the "Test SonarQube Connection" workflow in GitHub Actions:
- Go to Actions → Test SonarQube Connection
- Select test type: connectivity, authentication, or minimal-scan
- Run the workflow

### 3. Manual Local Test
```bash
# Install SonarScanner locally
wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-6.2.1.4610-linux-x64.zip
unzip sonar-scanner-cli-6.2.1.4610-linux-x64.zip
export PATH=$PATH:$(pwd)/sonar-scanner-6.2.1.4610-linux-x64/bin

# Run minimal scan
sonar-scanner \
  -Dsonar.projectKey="test-local" \
  -Dsonar.sources=src \
  -Dsonar.host.url="$SONAR_HOST_URL" \
  -Dsonar.token="$SONAR_TOKEN" \
  -Dsonar.qualitygate.wait=false
```

## Configuration Improvements

### 1. SonarQube Project Properties
Updated `sonar-project.properties` with:
```properties
# Network settings for better stability
sonar.ws.timeout=300
sonar.scm.disabled=false
sonar.ce.javaOpts=-Xmx2g -Xms128m -XX:+HeapDumpOnOutOfMemoryError
sonar.web.javaOpts=-Xmx1g -Xms128m -XX:+HeapDumpOnOutOfMemoryError
```

### 2. Workflow Improvements
- Enhanced error handling and retry logic
- Better connectivity testing
- Fallback mechanisms for network issues
- Improved logging and debugging

## Environment Variables

Ensure these are properly configured:

### GitHub Secrets
- `SONAR_TOKEN`: Your SonarQube authentication token

### GitHub Variables
- `SONAR_HOST_URL`: Your SonarQube server URL (e.g., `https://code-review.csharpp.com`)

## Monitoring and Debugging

### 1. Check Workflow Logs
- Look for specific error patterns
- Check network connectivity tests
- Review scanner installation logs

### 2. SonarQube Server Health
```bash
# Check server status
curl -s "$SONAR_HOST_URL/api/system/status"

# Check server health
curl -s "$SONAR_HOST_URL/api/system/health"

# Test authentication
curl -s -H "Authorization: Bearer $SONAR_TOKEN" \
  "$SONAR_HOST_URL/api/authentication/validate"
```

### 3. Local Debugging
```bash
# Enable debug logging
sonar-scanner -Dsonar.log.level=DEBUG -X

# Check system requirements
java -version
free -h
df -h
```

## Best Practices

### 1. Workflow Configuration
- Use appropriate timeouts (30 minutes max)
- Enable caching for dependencies
- Add continue-on-error for non-critical steps

### 2. Network Reliability
- Test connectivity before main scan
- Use retry logic for downloads
- Set appropriate timeout values

### 3. Resource Management
- Allocate sufficient memory (3GB recommended)
- Use efficient garbage collection settings
- Monitor disk space usage

### 4. Error Recovery
- Implement fallback strategies
- Provide meaningful error messages
- Enable artifact upload for debugging

## Getting Help

If issues persist:

1. **Check SonarQube Server Logs**: Contact your SonarQube administrator
2. **Review Network Configuration**: Ensure firewall/proxy settings allow access
3. **Test with Minimal Configuration**: Use the test workflows to isolate issues
4. **Monitor Resource Usage**: Check if the runner has sufficient resources

## Quick Fixes

### For Immediate Relief
1. Run the "Test SonarQube Connection" workflow to verify connectivity
2. Try the manual scan with "Skip Quality Gate" enabled
3. Use the local connection test script to verify your setup

### For Long-term Stability
1. Implement proper caching strategy
2. Configure network timeouts appropriately
3. Set up monitoring for SonarQube server health
4. Regularly test the scan pipeline
