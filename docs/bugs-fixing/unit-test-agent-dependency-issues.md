# Unit Test Agent Dependency Issues

## Title
Unit tests failing due to agent dependency issues and missing API keys during test execution

## Description
Multiple test suites are failing due to agent service dependencies that try to initialize real LLM connections during test collection and execution:

1. **Agent initialization errors**: Tests that depend on agent modules fail because agents try to create real OpenAI clients during import
2. **Service tests collection failure**: Service tests cannot be collected due to agent import issues
3. **Integration tests collection failure**: Integration tests fail with LangGraph API changes (`create_react_agent` unexpected keyword argument `state_modifier`)
4. **App tests execution failure**: Streamlit app tests fail because the app imports agent modules that require real API keys

## Current Test Status
- ✅ **Core tests**: 22/22 passing (100%)
- ✅ **Client tests**: 9/9 passing (100%) 
- ❌ **App tests**: 1/7 passing (14%) - 6 tests failing due to agent dependencies
- 🚫 **Service tests**: Cannot collect due to agent import issues
- 🚫 **Integration tests**: Cannot collect due to LangGraph API changes

## Root Causes
1. **Tight coupling**: Agent modules are imported at module level, causing initialization during test collection
2. **Missing test isolation**: No proper mocking of external dependencies during testing
3. **Environment coupling**: Tests require real API keys even for unit testing
4. **LangGraph version compatibility**: `create_react_agent` API has changed, causing integration test failures

## Solution

### Immediate Actions (Short-term fixes)
1. **Environment isolation**: Set up proper test environment variables and mocking
2. **Import isolation**: Refactor agent imports to be lazy-loaded or properly mocked in tests
3. **API key mocking**: Ensure all tests use fake API keys by default
4. **LangGraph compatibility**: Update agent code to use correct LangGraph API

### Long-term Actions (Architecture improvements)
1. **Dependency injection**: Implement proper dependency injection for agent services
2. **Test-specific configuration**: Create test-specific settings that don't require real external services
3. **Modular architecture**: Separate agent logic from initialization code
4. **Comprehensive mocking**: Implement proper mock factories for all external dependencies

## Files Affected
- `src/agents/resume_rag_agent.py` - Creates agent instance at module level
- `src/agents/learning_supervisor_agent.py` - Import dependencies cause failures
- `tests/service/` - All service tests affected
- `tests/integration/` - All integration tests affected  
- `tests/app/` - 6 out of 7 tests affected
- `src/streamlit_app.py` - Imports agent modules that require API keys

## Target Coverage Goals
According to the project guidelines:
- **Target coverage**: 80% LOC, 50% branches
- **Current backend coverage**: 95%+ (excellent)
- **Current agent service coverage**: 15.2% (critical issue)

## Priority
**HIGH** - This is blocking the majority of the test suite and preventing proper CI/CD validation.

## Expected Outcome
- All unit tests should run without requiring real API keys
- Test coverage should reach project targets (80% LOC, 50% branches)
- Tests should execute quickly and reliably in CI/CD environments
- Agent functionality should be properly testable in isolation
