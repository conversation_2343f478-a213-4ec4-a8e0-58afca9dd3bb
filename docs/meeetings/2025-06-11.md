Live demo: <PERSON><PERSON> luồng MVP:
1. <PERSON><PERSON><PERSON> ô chat bắt đầu gõ prompt.

Scenario 1 (<PERSON><PERSON><PERSON> về learning path của 1 người dùng):
2. Prompt 1: hỏi skill
- Ch<PERSON> trả thông tin skill
3. Prompt 2: prompt trả về gap
- <PERSON><PERSON><PERSON> về gap
4. Prompt 3: prompt roadmap 
- <PERSON><PERSON><PERSON> về roadmap

Scenario 2 (T<PERSON><PERSON> về gap và roadmap):
5. Prompt 1: hỏi skill
- Chờ trả thông tin skill
6. Prompt 2: prompt về nhu cầu muốn trở thành SA.

7. Xem kết quả roadmap (click vào chi tiết, xem course,...)


Về Slide:
- Tr<PERSON>nh bày thêm ý là tại sao phải tự phát triển agent của mình mà không dùng các sản phẩm có sẵn.
- Nên đưa thông tin là có dùng codevista vào
- Đưa ra viễn cảnh tiếp theo là integrate vớ<PERSON> hệ thống nào.

- <PERSON><PERSON><PERSON> chữ đi, <PERSON><PERSON> ti<PERSON><PERSON> số liệu và 1 câu highlight cho mỗi slide.