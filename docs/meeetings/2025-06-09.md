# Cuộc họp ngày 09/06/2025

## AI Support & Tools

### Hỗ trợ AI trong coding
- **Tools for coding**: NamNH46 sẽ làm report và chuẩn bị delivery item.

### Nguồn thông tin cần tập trung
- **AKA Job (CV)**: 
  - ❓ Đã mockup data giống AKA Job hay chỉ trình bày qua slide?
- **FHU align với job rank**: 
  - ❓ Đã mockup data hay chỉ trình bày qua slide?
  - 👥 **Action**: Trung kết hợp với anh <PERSON>ong
- **Radar chart**: So sánh hiện tại và goal → Nên có ở giao diện

## Điểm khác biệt so với competitors

### Lợi thế cạnh tranh
- ✅ Sử dụng data của FPT và đưa ra kết quả dựa theo các khóa học của Fsoft đang cho học free
- ✅ Dựa theo Job rank ở FHU nên ra được cái gap đúng như của Fsoft
- ✅ Lưu roadmap vào database
- ✅ Có 2 service riêng biệt: FHU (job rank) và AKA Job (Skill set)

### Định vị sản phẩm
**Cầu nối đứng ở giữa** → Customize roadmap theo skill của nhân viên

## Góp ý từ lãnh đạo trong buổi demo

### 1. Nguồn dữ liệu đầu vào

#### Ưu tiên các nguồn chính thức
- ✅ **Tập trung vào**: AKAJob và FHU (Job Assessment)
- ✅ **Lý do**: Liên kết với hệ thống sẵn có của Fsoft → dữ liệu chất lượng và thuyết phục hơn

#### Tránh các nguồn không chất lượng
- ❌ **Không sử dụng**: Jira, IMO, Okia để mô tả kỹ năng
- ❌ **Lý do**: Thiếu dữ liệu đã được tinh chỉnh
- ℹ️ **Lưu ý**: Dữ liệu từ Jira về dự án đã được tổng hợp ngược vào skill inventory trên AkaJob

#### Tận dụng hệ thống sẵn có
- 🎯 **FHU Job Rank Assessment** đã có:
  - Định nghĩa taxonomy (danh mục kỹ năng)
  - Đồ thị phân tích gap (ví dụ: Dev 3 → Dev 4)
- 📋 **Action**: Gắn bài trình bày với mạch câu chuyện này thay vì xây dựng lại từ đầu

### 2. Định vị sản phẩm và tầm nhìn

#### Quan điểm quan trọng nhất
> 🚨 **Sản phẩm không nên đứng độc lập**

#### Định vị đề xuất
- 🤖 **Vai trò**: "Agent" hoặc "Bot" cho FHU hoặc AKAJob
- 🔗 **Mục tiêu**: Cầu nối tinh gọn quá trình xây dựng career path và tư vấn nhân sự
- 📊 **Phương pháp**: Khai thác dữ liệu từ FHU và AkaJob

#### Tập trung cốt lõi
- ✅ **Nên làm**: AI cốt lõi (phân tích fit & gap, recommendation)
- ❌ **Tránh tham lam**: Hệ thống quản lý học tập hoặc quản lý lộ trình
- 🔄 **Tích hợp**: Với LMS/AkaJob hiện có (Iochara, Linkin, Coursera, Level Up, Udemy for business)

### 3. Nội dung và hình thức trình bày

#### Cải thiện demo
- 🔗 **Kết nối**: Các phần demo tạo luồng trình bày mượt mà
- 🎯 **Mục tiêu**: Chinh phục ban giám khảo

#### Cập nhật slide
- 📱 **Kiến trúc**: Đơn giản hơn, tập trung vào tính năng
- 📋 **Nội dung**: Input/output data và các agent chính
- 🏷️ **Tên gọi**: Ngắn gọn, dễ hiểu

#### Nhấn mạnh Learning Path
- 🎓 **Ưu tiên**: "Learning path" thay vì chỉ "career path"
- 📚 **Chứng minh**: Khả năng lấy khóa học từ nhiều nguồn
  - **Nội bộ Fsoft**: Level Up (ưu tiên)
  - **Bên ngoài**: Udemy, Coursera

## Action Items & Đề xuất

### 📋 Research & Development
- 🔍 **Nghiên cứu competitors**:
  - Pluralsight Skill
  - Talent Bootcamp  
  - IBM
  - GitHub
- 📝 **Mục tiêu**: Biết điểm mạnh và khác biệt của sản phẩm
- 📊 **Action**: Đưa vào báo cáo (tự cook)

### 🔄 Tính năng theo dõi tiến độ
- ⏰ **Chu kỳ đánh giá**: Sau 6 tháng hoặc sau dự án
- 🎯 **Mục tiêu**: Đánh giá lại kỹ năng, tạo phiên bản lộ trình mới
- 🔧 **Implementation**: Bổ sung action trên giao diện cho người dùng chọn
- 📊 **Độ ưu tiên**: Thấp (kịp thì làm)

### 📈 UI/UX Improvements
- 📊 **Biểu đồ lộ trình**: Vẽ ngược từ dưới lên thể hiện "level up"
- 🎨 **Design concept**: Tạo cảm giác tiến bộ rõ ràng

### 🤖 AI trong phát triển sản phẩm
- 💻 **Trình bày**: Cách team ứng dụng AI vào phát triển
  - Code generation
  - Design automation
- 🎯 **Mục tiêu**: Thể hiện năng suất và thuyết phục ban giám khảo
- 👤 **Responsible**: NamNH46 chuẩn bị

---

## 📝 Summary

### Key Takeaways
1. **Tập trung dữ liệu chính thức**: AKAJob + FHU
2. **Định vị agent/bot**: Không đứng độc lập
3. **Core AI functions**: Fit & gap analysis + recommendations
4. **Learning path focus**: Ưu tiên hơn career path
5. **Competitor analysis**: Để định vị khác biệt

### Next Steps
- [ ] Cập nhật slide theo định hướng mới
- [ ] Research competitor analysis  
- [ ] Chuẩn bị demo về AI tools usage
- [ ] Thiết kế radar chart cho giao diện
- [ ] Kết nối với anh Phong về FHU alignment